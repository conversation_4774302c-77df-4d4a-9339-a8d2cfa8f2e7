// Global variables
let authToken = localStorage.getItem('authToken');
let refreshToken = localStorage.getItem('refreshToken'); // NEW: Add refresh token
let currentUser = null;

// --- Global Utility Functions ---

function displayAlert(message, type = 'info', containerId = null) {
    const alertContainer = containerId ? $(containerId) : $('#globalAlertContainer'); // Need a global container or pass specific ones
    if (!alertContainer.length) {
        // If no specific container and no global one, fallback or log
        console.log(`Alert (${type}): ${message}`);
        $('body').prepend(`<div class="alert alert-${type} alert-dismissible fade show m-3 position-fixed top-0 end-0" role="alert" style="z-index: 1056;">${message}<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div>`);
        return;
    }
    const alertDiv = $(`
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `);
    alertContainer.empty().append(alertDiv); // empty() to replace previous alerts in the specific container
    // Auto-dismiss success/info alerts after a few seconds
    if (type === 'success' || type === 'info') {
        setTimeout(() => alertDiv.alert('close'), 5000);
    }
}

async function fetchAuthenticated(url, options = {}, isRetrying = false) {
    const originalHeaders = {
        ...(options.headers || {}),
        'Content-Type': 'application/json' // Assume JSON unless specified otherwise
    };
    if (authToken) {
        originalHeaders['Authorization'] = `Bearer ${authToken}`;
    }

    let response = await fetch(url, {
        ...options,
        headers: originalHeaders
    });

    if (response.status === 401 && !isRetrying && refreshToken) { // NEW: Check for refreshToken
        console.log('Received 401, attempting token refresh...');
        try {
            const refreshResponse = await fetch('/auth/refresh_token', { 
                method: 'POST',
                headers: {
                    // NEW: Send the REFRESH token for refresh validation
                    'Authorization': `Bearer ${refreshToken}` 
                }
            });

            if (refreshResponse.ok) {
                const refreshData = await refreshResponse.json();
                if (refreshData.access_token && refreshData.refresh_token) { // NEW: Check for both tokens
                    console.log('Token refreshed successfully.');
                    // NEW: Update both tokens
                    authToken = refreshData.access_token;
                    refreshToken = refreshData.refresh_token;
                    localStorage.setItem('authToken', authToken);
                    localStorage.setItem('refreshToken', refreshToken);

                    // Update headers for the retry
                    const retryHeaders = {
                        ...(options.headers || {}),
                        'Content-Type': 'application/json'
                    };
                    retryHeaders['Authorization'] = `Bearer ${authToken}`;
                    
                    console.log('Retrying original request with new token...');
                    // Retry the original request with the new token and new isRetrying flag
                    return await fetchAuthenticated(url, {...options, headers: retryHeaders}, true);
                } else {
                    console.error('Refresh endpoint did not return new tokens.');
                }
            } else {
                console.error('Token refresh failed with status:', refreshResponse.status);
            }
        } catch (refreshError) {
            console.error('Error during token refresh attempt:', refreshError);
        }
        
        // If refresh failed, proceed to logout
        logout();
        displayAlert('Session expired. Please log in again.', 'warning');
        throw new Error('Authentication required after failed refresh');
    }
    
    if (!response.ok) {
        let errorDetail = `HTTP error ${response.status}`;
        try {
            const errorData = await response.json();
            errorDetail = errorData.detail || errorDetail;
        } catch (e) {
            // Ignore if response is not JSON
        }
        throw new Error(errorDetail);
    }
    
    // If response has no content (e.g., 204), return null or indicate success
    if (response.status === 204) {
        return null; // Or { success: true }
    }

    return response.json(); // Assuming response is JSON
}

// Helper function to construct the correct image URL
function getImageUrl(relativePath) {
    if (!relativePath) {
        return '/static/placeholder.png';
    }
    // Normalize path separators
    let path = relativePath.replace(/\\/g, '/');
    // Check if the path already starts with 'images/' (case-insensitive)
    if (path.toLowerCase().startsWith('images/')) {
        // If it does, just prepend '/'
        return `/${path}`;
    } else {
        // Otherwise, prepend '/images/'
        return `/images/${path}`;
    }
}

// --- Global Authentication Logic ---

function updateAuthUI() {
    const authSection = $('#auth-section');
    const welcomeMessage = $('#welcome-message');
    const queryHistorySection = $('#queryHistorySection');
    // Selectors for admin-only sections
    const uploadSection = $('.upload-container'); // Assuming class selector is unique
    const settingsLink = $('.settings-link');

    authSection.empty(); // Clear existing buttons/info

    if (authToken && currentUser) {
        // User is logged in
        authSection.html(`
            <span class="me-2 text-muted">Welcome, <strong>${currentUser.username}</strong>!</span>
            <button id="logoutButton" class="btn btn-sm btn-outline-secondary">Logout</button>
        `);
        welcomeMessage.text(`Welcome back, ${currentUser.username}! Ready to find some deals?`).show();
        
        // Update model selects with user preference
        if (currentUser.preferred_model) {
            $('#parsingModelSelect').val(currentUser.preferred_model);
            $('#queryModelSelect').val(currentUser.preferred_model);
        }

        // Display query history
        displayQueryHistory(currentUser.query_history);

        // Explicitly check currentUser again before accessing properties
        if (currentUser) {
            // Show/Hide Admin sections based on is_admin flag
            if (currentUser.is_admin) {
                console.log("User is admin, showing admin sections.");
                uploadSection.show();
                settingsLink.show();
            } else {
                console.log("User is NOT admin, hiding admin sections.");
                uploadSection.hide();
                settingsLink.hide();
            }
        } else {
            console.log("updateAuthUI called but currentUser is unexpectedly null/undefined despite authToken existing.");
            uploadSection.hide();
            settingsLink.hide();
        }

    } else {
        // User is logged out
        authSection.html(`
            <button class="btn btn-sm btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#loginModal">Login</button>
            <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#signupModal">Sign Up</button>
        `);
        welcomeMessage.hide();
        queryHistorySection.hide();
        // Ensure admin sections are hidden when logged out
        uploadSection.hide(); 
        settingsLink.hide();
    }
}

function displayQueryHistory(history) {
    const historyList = $('#queryHistoryList');
    const historySection = $('#queryHistorySection');
    historyList.empty();

    if (history && history.length > 0) {
        history.forEach(query => {
            const listItem = $('<li class="list-group-item list-group-item-action" style="cursor: pointer;"></li>').text(query);
            listItem.on('click', () => {
                $('#queryInput').val(query);
                // Optional: Trigger query submission?
            });
            historyList.append(listItem);
        });
        historySection.show();
    } else {
        historySection.hide();
    }
}

async function fetchCurrentUser() {
    if (!authToken) {
        currentUser = null;
        updateAuthUI();
        loadCatalogs(); // Load catalogs, it will check global currentUser
        return;
    }
    try {
        currentUser = await fetchAuthenticated('/users/me');
        console.log("Current user:", currentUser);
    } catch (error) {
        console.error("Error fetching current user:", error);
        logout(); 
        currentUser = null;
    }
    updateAuthUI(); // Update UI based on fetched user
    loadCatalogs(); // Load catalogs, it will check global currentUser
}

function logout() {
    authToken = null;
    refreshToken = null; // NEW: Clear refresh token
    currentUser = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken'); // NEW: Remove refresh token from storage
    updateAuthUI();
    // Optionally clear model selections or other user-specific UI elements
    $('#queryModelSelect').val(''); // Reset to default
    $('#parsingModelSelect').val(''); // Reset to default
    $('#queryHistorySection').hide();
    $('#queryHistoryList').empty();
}

// --- Global Catalog Loading Logic ---

function loadCatalogs() {
    // Check global currentUser variable for admin status INSIDE the function
    const isAdmin = currentUser && currentUser.is_admin;
    console.log(`Loading catalogs... (isAdmin check inside: ${isAdmin})`);
    const catalogsListDiv = $('#catalogsList');
    const queryCatalogSelectionDiv = $('#catalogCheckboxes');

    // Start loading indication for both sections if they exist
    if (catalogsListDiv.length) {
        catalogsListDiv.html('<div class="loading d-flex justify-content-center align-items-center p-5"><span class="spinner-border spinner-border-sm me-2"></span> Loading catalogs...</div>');
    }
    if (queryCatalogSelectionDiv.length) {
        queryCatalogSelectionDiv.html('<small class="text-muted">Loading catalogs...</small>');
    }

    fetch('/catalogs')
        .then(response => {
            if (!response.ok) {
                // Try to get error details from JSON response
                return response.json().then(errData => {
                        throw new Error(errData.detail || `HTTP error ${response.status}`);
                }).catch(() => {
                    // Fallback if response is not JSON
                    throw new Error(`HTTP error ${response.status}`);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log("Catalogs received:", data);
            if (!Array.isArray(data)) {
                throw new Error("Invalid data format received from server.");
            }

            // Populate query selection buttons FIRST
            populateQuerySelectionButtons(data);

            // --- Select the target div *inside* the .then(), just before use ---
            const catalogListElement = document.getElementById('catalogList');
            if (!catalogListElement) {
                console.error("[Debug] loadCatalogs .then(): Could not find #catalogList element!");
            } else {
                const catalogsListDiv = $(catalogListElement); // Wrap with jQuery
            
                // Now populate the main list (if the div exists)
                catalogsListDiv.empty(); // Clear loading indicator
                if (data.length === 0) {
                    catalogsListDiv.html('<div class="alert alert-light text-center">No catalogs uploaded yet.</div>');
                } else {
                    console.log(`[Debug] loadCatalogs: Found ${data.length} catalogs. Starting #catalogsList population.`); // Log start
                    const rowDiv = $('<div class="row"></div>');
                    data.forEach(function(catalog, index) {
                        console.log(`[Debug] loadCatalogs: Processing catalog index ${index}, ID: ${catalog.id}`); // Log each item
                        try {
                            // Use first page image for catalog grid (prioritize first page, fallback to logo)
                            const imageUrl = catalog.first_page_image_url || catalog.store_logo_url || '/static/placeholder.png';
                            const img = $('<img/>', {
                                src: imageUrl,
                                class: 'card-img-top p-2', alt: catalog.title || 'Catalog Cover',
                                css: { 'max-height': '200px', 'object-fit': 'contain' },
                                onerror: "this.onerror=null;this.src='/static/placeholder.png';"
                            });
                            const title = $('<h5 class="card-title"></h5>').text(catalog.title || 'Untitled Catalog');
                            const subtitle = $('<h6 class="card-subtitle mb-2 text-muted"></h6>').text(catalog.store_name || 'Unknown Store');
                            const details = $('<p class="card-text small mb-2"></p>').html(
                                `ID: ${catalog.id}<br>Gyldig: ${catalog.valid_from ? new Date(catalog.valid_from).toLocaleDateString('da-DK') : 'N/A'} - ${catalog.valid_to ? new Date(catalog.valid_to).toLocaleDateString('da-DK') : 'N/A'}<br>Sider: ${catalog.pages || 0} | Produkter: ${catalog.products || 0}`
                            );
                            const buttonDiv = $('<div class="mt-auto d-flex justify-content-end pt-2"></div>');
                            
                            // --- CONDITIONALLY ADD ADMIN BUTTONS using local isAdmin check ---
                            if (isAdmin) {
                                if (catalog.products === 0) {
                                    const parseButton = $('<button class="btn btn-outline-secondary btn-sm parse-catalog ms-2"></button>')
                                        .attr('data-id', catalog.id)
                                        .html('<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cpu" viewBox="0 0 16 16"><path d="M5 0a.5.5 0 0 1 .5.5V2h1V.5a.5.5 0 0 1 1 0V2h1V.5a.5.5 0 0 1 1 0V2A2.5 2.5 0 0 1 14 4.5h1.5a.5.5 0 0 1 0 1H14v1h1.5a.5.5 0 0 1 0 1H14v1h1.5a.5.5 0 0 1 0 1H14v1h1.5a.5.5 0 0 1 0 1H14a2.5 2.5 0 0 1-2.5 2.5v1.5a.5.5 0 0 1-1 0V14h-1v1.5a.5.5 0 0 1-1 0V14h-1v1.5a.5.5 0 0 1-1 0V14A2.5 2.5 0 0 1 2 11.5H.5a.5.5 0 0 1 0-1H2v-1H.5a.5.5 0 0 1 0-1H2v-1H.5a.5.5 0 0 1 0-1H2A2.5 2.5 0 0 1 4.5 2V.5A.5.5 0 0 1 5 0m-.5 3A1.5 1.5 0 0 0 3 4.5v7A1.5 1.5 0 0 0 4.5 13h7a1.5 1.5 0 0 0 1.5-1.5v-7A1.5 1.5 0 0 0 11.5 3zM5 6.5A1.5 1.5 0 0 1 6.5 5h3A1.5 1.5 0 0 1 11 6.5v3A1.5 1.5 0 0 1 9.5 11h-3A1.5 1.5 0 0 1 5 9.5zM6.5 6a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5z"/></svg> Parse');
                                    buttonDiv.append(parseButton); 
                                }
                                const deleteButton = $('<button class="btn btn-outline-danger btn-sm delete-catalog"></button>')
                                    .attr('data-id', catalog.id)
                                    .html('<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16"><path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"/><path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z"/></svg> Slet');
                                
                                // --- Attach Listener Directly --- 
                                deleteButton.on('click', async function(event) {
                                    console.log('--- Delete Button Clicked (DIRECT HANDLER) ---');
                                    event.stopPropagation(); // Still stop propagation
                                    event.preventDefault();
                                    
                                    const button = $(this);
                                    let catalogId = button.data('id');
                                    console.log(`--- Delete Button (Direct): Catalog ID Found: ${catalogId} (Type: ${typeof catalogId}) ---`);

                                    if (!catalogId) {
                                        console.error("Direct delete handler: No catalog ID found.");
                                        displayAlert('Could not find catalog ID to delete.', 'warning');
                                        return;
                                    }
                                    catalogId = parseInt(catalogId, 10);
                                    if (isNaN(catalogId)) {
                                        console.error("Direct delete handler: Parsed catalog ID is NaN.");
                                        displayAlert('Catalog ID is invalid.', 'warning');
                                        return;
                                    }

                                    if (!confirm(`Are you sure you want to delete catalog ID ${catalogId}? This cannot be undone.`)) {
                                        return; // User canceled
                                    }

                                    console.log(`Attempting to delete catalog ID: ${catalogId} (from direct handler)`);
                                    button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span>');

                                    try {
                                        await fetchAuthenticated(`/catalog/${catalogId}`, { method: 'DELETE' });
                                        console.log(`Successfully deleted catalog ID: ${catalogId} (from direct handler)`);
                                        displayAlert(`Catalog ${catalogId} deleted successfully.`, 'success');
                                        loadCatalogs(); // Refresh the list
                                    } catch (error) {
                                        console.error(`Error deleting catalog ${catalogId} (from direct handler):`, error);
                                        displayAlert(`Failed to delete catalog ${catalogId}: ${error.message}`, 'danger');
                                        button.prop('disabled', false).html('<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16"><path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5m3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z"/><path d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4zM2.5 3h11V2h-11z"/></svg> Slet'); // Restore button
                                    }
                                });
                                // --- End Direct Listener ---

                                buttonDiv.append(deleteButton);
                            }
                            // --- END CONDITIONAL BUTTONS ---

                            const cardBody = $('<div class="card-body d-flex flex-column"></div>').append(title, subtitle, details, buttonDiv); 
                            const catalogCard = $('<div class="catalog-card card w-100 position-relative"></div>')
                                .attr('data-catalog-id', catalog.id)
                                .append(img, cardBody);
                            console.log(`[Debug] loadCatalogs: Created card elements for ID: ${catalog.id}`); // Log card creation
                            const colDiv = $('<div class="col-12 col-md-6 col-lg-4 mb-4 d-flex align-items-stretch"></div>').append(catalogCard);
                            rowDiv.append(colDiv); 
                            console.log(`[Debug] loadCatalogs: Appended card for ID: ${catalog.id} to rowDiv`); // Log appending to row
                        } catch (e) { 
                            console.error(`[Debug] [loadCatalogs] Error processing catalog ${index + 1} for #catalogsList:`, e, catalog);
                        } 
                    });
                    console.log("[Debug] loadCatalogs: Finished loop. Appending rowDiv to #catalogsList."); // Log before final append
                    catalogsListDiv.append(rowDiv); 
                    console.log("[Debug] loadCatalogs: Appended rowDiv to #catalogsList."); // Log after final append
                }
            }
        })
        .catch(error => {
            console.error('Failed to load catalogs:', error);
            // Show error in both places if they exist
            if (catalogsListDiv.length) {
                catalogsListDiv.html(`<div class="alert alert-danger">Failed to load catalogs: ${error.message}</div>`);
            }
            populateQuerySelectionButtons([]); // Clear or show error in query buttons
        });
}

function populateQuerySelectionButtons(catalogData) {
    console.log("[Debug] populateQuerySelectionButtons: Starting with image buttons...");
    const queryCatalogSelectionDiv = $('#catalogCheckboxes');
    queryCatalogSelectionDiv.empty(); 
    if (!queryCatalogSelectionDiv.length) {
        console.log("[Debug] populateQuerySelectionButtons: #catalogCheckboxes not found. Exiting.");
        return;
    }

    if (catalogData.length === 0) {
        queryCatalogSelectionDiv.html('<small class="text-muted">No catalogs available to query.</small>');
    } else {
        // Use d-flex flex-wrap for wrapping
        const imageContainer = $('<div class="d-flex flex-wrap gap-2"></div>');
        catalogData.forEach(catalog => {
            // Use store logo for query selection (prioritize logo, fallback to first page)
            const imageUrl = catalog.store_logo_url || catalog.first_page_image_url || '/static/placeholder.png';

            // Create Image Element
            const imageElement = $('<img>', {
                src: imageUrl,
                alt: `${catalog.store_name} - ${catalog.title || 'Catalog ' + catalog.id}`,
                css: {
                    width: '70px', // Slightly smaller width
                    height: '90px', // Slightly smaller height
                    objectFit: 'contain',
                    display: 'block', // Ensure block display for centering
                    margin: '0 auto 0.25rem auto' // Center image and add bottom margin
                },
                onerror: "this.onerror=null;this.src='/static/placeholder.png';"
            });

            // Create Text Element
            const storeNameElement = $('<span class="catalog-store-name"></span>').text(catalog.store_name || 'Unknown');

            // Create Container
            const containerElement = $('<div>', {
                class: 'catalog-select-container text-center', // New class for container, text centering
                'data-catalog-id': catalog.id, 
                css: {
                    border: '2px solid transparent',
                    borderRadius: '4px',
                    padding: '0.3rem',
                    cursor: 'pointer',
                    transition: 'border-color 0.2s ease',
                    width: '90px' // Container width
                }
            }).append(imageElement).append(storeNameElement);
            
            imageContainer.append(containerElement);
        });
        queryCatalogSelectionDiv.append(imageContainer);
    }
    console.log("[Debug] populateQuerySelectionButtons: Finished creating image elements.");
}

// --- Global Model Population Logic ---
async function populateModelSelectors() {
    const parsingSelect = $('#parsingModelSelect');
    const querySelect = $('#queryModelSelect');
    // Clear existing options (keep first if it's a placeholder)
    parsingSelect.find('option:not([value=""])').remove(); // Assuming default has value=""
    querySelect.find('option:not([value=""])').remove(); // Assuming default has value=""

    try {
        // Fetch models (doesn't require authentication)
        const response = await fetch('/models');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const models = await response.json();

        models.forEach(modelName => {
            parsingSelect.append(`<option value="${modelName}">${modelName}</option>`);
            querySelect.append(`<option value="${modelName}">${modelName}</option>`);
        });
        
        // After populating, update selection based on logged-in user (if any)
        if (currentUser && currentUser.preferred_model) {
            parsingSelect.val(currentUser.preferred_model);
            querySelect.val(currentUser.preferred_model);
        }

    } catch (error) {
        console.error('Error fetching or populating models:', error);
        displayAlert('Error loading AI models.', 'warning');
    }
}

// --- NEW: Function to populate Store Dropdown ---
async function populateStoreDropdown() {
    const storeSelect = $('#store_id'); // Target the correct select element
    if (!storeSelect.length) return; // Exit if the element doesn't exist

    // Clear existing store options, but keep the placeholder and "Add New"
    storeSelect.find('option').not('[value=""], [value="__add_new__"]').remove();

    try {
        const response = await fetch('/stores'); // Fetch from the /stores endpoint
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const stores = await response.json();

        // Add each store as an option before the "Add New" option
        const addNewOption = storeSelect.find('option[value="__add_new__"]');
        stores.forEach(store => {
            const option = $('<option></option>').val(store.id).text(store.name);
            addNewOption.before(option);
        });

    } catch (error) {
        console.error('Error fetching or populating stores:', error);
        // Optionally display an error message in the dropdown or using an alert
        storeSelect.find('option[value=""]').text('Error loading stores');
    }
}

// --- Prompt Suggestion Gallery Logic ---
async function loadPromptSuggestions() {
    try {
        const response = await fetch('/static/prompt_suggestions.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const suggestions = await response.json();
        initializePromptGallery(suggestions);
    } catch (error) {
        console.error('Error loading prompt suggestions:', error);
        $('#promptSuggestionGallery').html('<small class="text-muted">Could not load suggestions.</small>');
    }
}

function initializePromptGallery(suggestions) {
    const gallery = $('#promptSuggestionGallery');
    gallery.empty(); // Clear any previous content

    if (!suggestions || suggestions.length === 0) {
        gallery.hide(); // Hide gallery if no suggestions
        return;
    }

    suggestions.forEach(suggestionObject => {
        const card = $('<div class="prompt-card"></div>');
        const promptText = suggestionObject.prompt;
        card.append($('<p></p>').text(promptText));
        card.on('click', () => {
            $('#queryInput').val(promptText);
            // Optionally, you could also trigger the form submission here
            // $('#queryForm').submit(); 
        });
        gallery.append(card);
    });

    gallery.addClass('loaded').show(); // Add animation class and show
}

// --- Document Ready --- 
$(document).ready(function() {
    console.log("--- JS: DOCUMENT READY FIRED ---"); // LOUD LOG A

    // Initial setup: Fetch user data, load catalogs, populate selectors
    console.log("--- JS: Attaching initial setup calls ---"); // LOUD LOG B
    fetchCurrentUser(); // Also calls loadCatalogs within it
    populateModelSelectors(); // <<< ADD THIS CALL
    populateStoreDropdown();  // <<< ADD THIS CALL
    loadPromptSuggestions(); // <<< ADD THIS CALL

    // Event listener for logout button (delegated)
    $(document).on('click', '#logoutButton', logout);

    // --- Login/Signup Form Handlers --- 
    $('#loginForm').on('submit', async function(e) { 
        e.preventDefault();
        const username = $('#loginUsername').val();
        const password = $('#loginPassword').val();
        
        try {
            // Log for debugging
            console.log('Attempting login for:', username);
            
            const response = await fetch('/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    'username': username,
                    'password': password
                })
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Login failed');
            }
            
            const data = await response.json();
            
            // NEW: Store both tokens
            localStorage.setItem('authToken', data.access_token);
            localStorage.setItem('refreshToken', data.refresh_token);
            authToken = data.access_token;
            refreshToken = data.refresh_token;
            
            // Close the modal
            $('#loginModal').modal('hide');
            
            // Fetch current user info and update UI
            await fetchCurrentUser();
            
            // Show success message
            displayAlert('Login successful!', 'success');
        } catch (error) {
            console.error('Login error:', error);
            displayAlert('Login failed: ' + error.message, 'danger');
        }
    });

    $('#signupForm').on('submit', async function(e) {
        e.preventDefault();
        const username = $('#signupUsername').val();
        const password = $('#signupPassword').val();
        
        try {
            const response = await fetch('/users/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Signup failed');
            }
            
            // Close the signup modal
            $('#signupModal').modal('hide');
            
            // Show success message and suggest login
            displayAlert('Account created successfully! Please log in.', 'success');
            
            // Optional: Open login modal
            setTimeout(() => {
                $('#loginModal').modal('show');
            }, 1500);
            
        } catch (error) {
            console.error('Signup error:', error);
            displayAlert('Signup failed: ' + error.message, 'danger');
        }
    });

    // --- Upload Form Handlers --- 
    // Event listener for store selection change
    $('#store_id').on('change', function() {
        const selectedValue = $(this).val();
        if (selectedValue === '__add_new__') {
            $('#new_store_name_container').slideDown(); // Show with animation
            $('#new_store_name').prop('required', true); // Make input required
        } else {
            $('#new_store_name_container').slideUp(); // Hide with animation
            $('#new_store_name').prop('required', false); // Make input not required
            $('#new_store_name').val(''); // Clear the input field
        }
    });

    $('#uploadForm').on('submit', async function(e) { // Make handler async
        e.preventDefault();
        console.log("Upload form submitted.");

        const formData = new FormData(this);
        const uploadButton = $('#uploadButton');
        const uploadStatus = $('#uploadStatus');
        const spinner = uploadButton.find('.spinner-border');

        // Basic validation (ensure file is selected)
        const fileInput = $('#file')[0];
        if (!fileInput.files || fileInput.files.length === 0) {
            displayAlert('Please select a PDF file to upload.', 'warning', '#uploadStatus');
            return;
        }

        // Show loading state
        uploadButton.prop('disabled', true);
        spinner.show();
        uploadStatus.html('<div class="alert alert-info">Uploading and processing... this may take a moment.</div>').show();

        try {
            // Use fetch directly for FormData, passing the token manually
            const headers = {};
            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }
            // DO NOT set Content-Type for FormData, browser does it.

            const response = await fetch('/upload_catalog', {
                method: 'POST',
                headers: headers, // Only include auth header
                body: formData,
            });

            const responseData = await response.json(); // Try to parse JSON regardless of status

            if (!response.ok) {
                // Handle specific known errors
                if (response.status === 401) {
                    logout();
                    throw new Error('Authentication expired. Please log in again.');
                }
                 // Use detail from JSON if available, otherwise status text
                throw new Error(responseData.detail || `Upload failed with status: ${response.status}`);
            }

            // Success
            console.log("Upload successful:", responseData);
            displayAlert(`Catalog uploaded successfully (ID: ${responseData.catalog_id}). ${responseData.products > 0 ? responseData.products + ' products parsed.' : 'Parsing skipped or done later.'}`, 'success', '#uploadStatus');
            $(this)[0].reset(); // Reset the form
            $('#new_store_name_container').hide(); // Hide new store input if visible
            loadCatalogs(); // Reload the catalog list

        } catch (error) {
            console.error('Upload error:', error);
            displayAlert(`Upload failed: ${error.message}`, 'danger', '#uploadStatus');
        } finally {
            // Hide loading state
            uploadButton.prop('disabled', false);
            spinner.hide();
            // Keep status message visible until next action or alert
        }
    });

    // --- Query Form Handlers --- 
    $('#queryForm').on('submit', async function(e) {
        console.log("[Debug] Query form submitted. Preventing default...");
        e.preventDefault(); // Prevent default form submission

        try {
            const question = $('#queryInput').val();
            console.log(`[Debug] Question value: "${question}"`); // Log question value

            const selectedModel = $('#queryModelSelect').val(); // Get selected model
            const answerDiv = $('#queryResult');
            const askButton = $(this).find('button[type="submit"]'); // Get the button

            if (!question) {
                displayAlert('Please enter a question.', 'warning');
                return; // Exit if no question
            }

            // Show loading state
            answerDiv.html('<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>').show(); // Ensure it's visible
            askButton.prop('disabled', true); // Disable button

            // --- GET SELECTED CATALOGS ---
            const catalogIds = getSelectedCatalogIdsForQuery(); // Call helper function
            console.log("[Debug] Querying with catalogs:", catalogIds); // Debug log

            const response = await fetchAuthenticated('/ask', { // Using fetchAuthenticated
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: question,              // Changed from question
                    selected_catalog_ids: catalogIds, // Changed from catalog_ids
                    query_model: selectedModel      // Changed from model_name
                }),
            });

            // Note: fetchAuthenticated throws an error for non-OK responses now,
            // so we don't need the explicit !response.ok check here.

            const data = response; // fetchAuthenticated already parsed JSON
            console.log("[Debug] Query Response Data:", data); // Debug log

            console.log("[Debug] Received data:", data);

            // --- Restore Full Display Logic ---
            answerDiv.empty(); // Clear previous results

            // 1. Display LLM Text Response
            const llmResponseArea = $('<div id="llm-response-area"></div>');
            if (data && data.response) {
                 if (typeof marked !== 'undefined') {
                    llmResponseArea.html(marked.parse(data.response));
                } else {
                    const formattedAnswer = data.response.replace(/\\n/g, '<br>');
                    llmResponseArea.html(formattedAnswer);
                }
            } else {
                llmResponseArea.html('<div class="alert alert-warning">Received an empty answer.</div>');
            }
            answerDiv.append(llmResponseArea);

            // 2. Prepare Product Previews Container
            const productPreviewsArea = $('<div id="product-previews" class="mt-4 pt-3 border-top"><h5>Related Offers:</h5><div class="row"></div></div>');
            answerDiv.append(productPreviewsArea); // Append the container
            const productRow = answerDiv.find('#product-previews .row'); // Find the row inside

            // 3. Populate Product Previews (Re-enabled)
            if (data && data.products && data.products.length > 0) {
                const MAX_RELATED_OFFERS = 20;
                const limitedProducts = data.products.slice(0, MAX_RELATED_OFFERS);

                limitedProducts.forEach(product => {
                    const imageUrl = getImageUrl(product.image_path); // Use helper
                    
                    // --- Refactored Card Creation ---
                    const $productCardDiv = $('<div class="col-6 col-md-4 col-lg-3 mb-3"></div>');
                    const cardInnerHtml = `
                        <div class="card h-100 product-preview-card">
                            <img src="${imageUrl}" 
                                 class="card-img-top p-1 product-thumbnail-clickable" 
                                 alt="${product.name || 'Product Image'}" 
                                 style="height: 100px; object-fit: contain; cursor: pointer;" 
                                 onerror="this.onerror=null;this.src='/static/placeholder.png';">
                            <div class="card-body p-2 d-flex flex-column">
                                <p class="card-text small mb-1 flex-grow-1">
                                    <strong>${product.name || '-'}</strong><br>
                                    <span class="text-muted">${product.brand || ''} ${product.description || ''}</span>
                                </p>
                                <p class="card-text small mb-0">
                                    Kr ${product.price ? product.price.toFixed(2) : '-'} ${product.unit ? '(' + product.unit + ')' : ''}<br>
                                    <span class="text-danger">${product.price_per_base_unit ? product.price_per_base_unit.toFixed(2) + ' kr/' + product.base_unit_type : ''}</span><br>
                                    <small class="text-muted">${product.store_name || 'Unknown Store'}</small>
                                </p>
                            </div>
                        </div>
                    `; 
                    $productCardDiv.html(cardInnerHtml);
                    productRow.append($productCardDiv);
                    // --- End Refactored Card Creation ---
                });
            } else {
                productRow.html('<div class="col-12"><p class="text-muted small">No specific product details found for this query.</p></div>');
            }

            answerDiv.show(); // Ensure the main container is visible
            // --- End Full Display Logic ---

            // Update query history if available in response
            if (data && data.query_history) { // Assuming history is still top-level for now
                displayQueryHistory(data.query_history);
            }

        } catch (error) {
            // Catch errors *within* the try block
            console.error('[Debug] Error caught inside queryForm submit handler:', error);
            const answerDiv = $('#queryResult');
            answerDiv.html('<div class="alert alert-danger">Error processing query: ' + error.message + '</div>').show();
            // Consider if we need to re-enable the button here too
            const askButton = $(this).find('button[type="submit"]'); 
            askButton.prop('disabled', false);

        } finally {
            // This block executes whether try succeeded or failed (unless return was hit)
            // Ensure button is re-enabled if it wasn't in catch
            console.log("[Debug] Query form handler finally block.");
            const askButton = $(this).find('button[type="submit"]'); 
            if (askButton.prop('disabled')) { // Only re-enable if still disabled
                 askButton.prop('disabled', false);
            }
           
        }
    });

    // --- CLICK HANDLER FOR IMAGE PREVIEW MODAL (Delegated) ---
    $('#queryResult').on('click', '.product-thumbnail-clickable', function() {
        const clickedImageUrl = $(this).attr('src');
        if (clickedImageUrl && !clickedImageUrl.endsWith('placeholder.png')) { // Don't open placeholder
            $('#modalImageElement').attr('src', clickedImageUrl);
            const imageModal = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
            imageModal.show();
        } else {
            console.log("Placeholder or invalid image clicked, not opening modal.");
        }
    });
    // --- END MODAL HANDLER ---

    // --- CLICK HANDLER FOR CATALOG SELECT CONTAINERS ---
    $('#catalogCheckboxes').on('click', '.catalog-select-container', function() { // Target container
        $(this).toggleClass('selected'); // Toggle the 'selected' class on the container
        // Update visual style based on selection
        if ($(this).hasClass('selected')) {
            $(this).css('border-color', '#4f46e5'); // Indigo border when selected
        } else {
            $(this).css('border-color', 'transparent'); // Transparent border when not selected
        }
    });

    // --- Delete/Parse Catalog Handlers (delegated) ---
    /* --- REMOVED DELEGATED HANDLER --- 
    console.log("--- JS: ATTACHING DELETE HANDLER ---"); // LOUD LOG C
    $(document).on('click', '#catalogsList .delete-catalog', async function(event) {
        // ... entire delegated handler function body ...
    });
    */

    $('#catalogsList').on('click', '.parse-catalog', async function(event) { // <<< Keep parse handler (ensure it's delegated from #catalogsList or body)
        event.stopPropagation(); 
        const button = $(this);
        const catalogId = button.data('id');
        
        if (!catalogId) {
            console.error("Parse button clicked but no catalog ID found.");
            return;
        }

        console.log(`Attempting to parse catalog ID: ${catalogId}`);
        button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span>'); // Show loading

        try {
            // Use fetchAuthenticated for POST requests with correct API path
            const response = await fetchAuthenticated(`/api/parse_catalog/${catalogId}`, {
                method: 'POST'
            });

            console.log("Parse response:", response);
            if (response && response.response) {
                displayAlert(`Parsing completed. Result: ${response.response}`, 'success');
            } else {
                displayAlert('Parsing completed but no result received.', 'info');
            }
        } catch (error) {
            console.error(`Error parsing catalog ${catalogId}:`, error);
            displayAlert(`Failed to parse catalog ${catalogId}: ${error.message}`, 'danger');
        } finally {
            button.prop('disabled', false).html('<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cpu" viewBox="0 0 16 16"><path d="M5 0a.5.5 0 0 1 .5.5V2h1V.5a.5.5 0 0 1 1 0V2h1V.5a.5.5 0 0 1 1 0V2A2.5 2.5 0 0 1 14 4.5h1.5a.5.5 0 0 1 0 1H14v1h1.5a.5.5 0 0 1 0 1H14v1h1.5a.5.5 0 0 1 0 1H14v1h1.5a.5.5 0 0 1 0 1H14a2.5 2.5 0 0 1-2.5 2.5v1.5a.5.5 0 0 1-1 0V14h-1v1.5a.5.5 0 0 1-1 0V14h-1v1.5a.5.5 0 0 1-1 0V14A2.5 2.5 0 0 1 2 11.5H.5a.5.5 0 0 1 0-1H2v-1H.5a.5.5 0 0 1 0-1H2v-1H.5a.5.5 0 0 1 0-1H2A2.5 2.5 0 0 1 4.5 2V.5A.5.5 0 0 1 5 0m-.5 3A1.5 1.5 0 0 0 3 4.5v7A1.5 1.5 0 0 0 4.5 13h7a1.5 1.5 0 0 0 1.5-1.5v-7A1.5 1.5 0 0 0 11.5 3zM5 6.5A1.5 1.5 0 0 1 6.5 5h3A1.5 1.5 0 0 1 11 6.5v3A1.5 1.5 0 0 1 9.5 11h-3A1.5 1.5 0 0 1 5 9.5zM6.5 6a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5z"/></svg> Parse'); // Restore button
        }
    });

    // --- Settings Panel Logic (Assuming it's okay inside if only called here/on settings page) ---
    function loadSettings() { /* ... */ }
    async function populateSettingsModelSelectors() { /* ... */ }
    $('#settingsForm').on('submit', async function(e) { /* ... */ });
    // Toggle/Test API Key handlers...

    // --- Initialization on DOM Ready ---
    populateModelSelectors(); // Now global
    fetchCurrentUser();     // Fetches user, then calls loadCatalogs (now global)
    loadPromptSuggestions();  // Needs to be defined (likely globally or inside ready)
    initializePromptGallery(); // Needs to be defined (likely globally or inside ready)

    // Load settings only if on settings page
    if (window.location.pathname.endsWith('/settings')) {
        loadSettings(); // Assumes loadSettings is defined within ready scope
    }

    // Event listener for the query input textarea to handle Enter and Shift+Enter
    $('#queryInput').on('keydown', function(e) {
        // Key code 13 is Enter
        if (e.key === 'Enter' || e.keyCode === 13) {
            if (!e.shiftKey) { // If Shift is NOT pressed
                e.preventDefault(); // Prevent default Enter action (newline)
                $('#queryForm').submit(); // Trigger form submission
            } // If Shift is pressed, default action (newline) is allowed
        }
    });

    console.log("--- JS: DOCUMENT READY ENDED ---"); // LOUD LOG D
});

// --- Helper function to get selected catalog IDs ---
function getSelectedCatalogIdsForQuery() {
    const selectedIds = [];
    $('#catalogCheckboxes .catalog-select-container.selected').each(function() { // Find selected containers
        selectedIds.push($(this).data('catalog-id'));
    });
    return selectedIds;
}

console.log("--- JS: SCRIPT PARSING COMPLETED (BOTTOM OF FILE) ---"); // LOUD LOG E