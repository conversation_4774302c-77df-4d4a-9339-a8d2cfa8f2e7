#!/usr/bin/env python3
"""
Comprehensive test comparing all three utils versions:
1. Current utils.py (original)
2. utilsSUGGESTED.py (suggested refactor)  
3. utils_ENHANCED.py (enhanced with Danish patterns)
"""

import sys
import json
from typing import Dict, Any, List

# Import all three implementations
from utils import parse_and_calculate_unit_price as current_func
from utilsSUGGESTED import process_unit_data_for_product as suggested_func
from utils_ENHANCED import parse_and_calculate_unit_price as enhanced_func

# Comprehensive test cases including Danish-specific patterns
COMPREHENSIVE_TEST_CASES = [
    # Original failing cases from parser logs
    ("144 g / 6 stk.", 32.00),
    ("750 ml.", 15.00),
    ("125 g. 10 stk.", 25.00),
    ("175 g.", 12.00),
    ("900 ml.", 18.00),
    ("330-720 ml.", 20.00),
    ("stk.", 10.00),
    ("g", 5.00),
    ("42 stk.", 30.00),
    ("200-300 ml", 15.00),
    ("150-350 stk.", 25.00),
    ("500-750 ml", 22.00),
    ("40-70 stk.", 18.00),
    
    # Standard cases that should work
    ("500g", 20.00),
    ("1 kg", 35.00),
    ("1.5 l", 12.00),
    ("2x100g", 15.00),
    ("3 x 75 cl", 25.00),
    ("pakke", 8.00),
    
    # NEW: Danish-specific patterns
    ("ca. 500g", 12.00),           # "approximately"
    ("min. 2 kg", 25.00),          # "minimum"
    ("max 3 stk", 15.00),          # "maximum"
    ("cirka 750 ml", 18.00),       # "approximately" (full word)
    ("pr. stk", 5.00),             # "per piece"
    ("per styk", 8.00),            # "per piece" (alternative)
    ("6 x 330ml dåser", 30.00),    # "6 x 330ml cans"
    ("2x100 g", 18.00),            # No space in multipack
    ("1,5 kg", 25.00),             # Danish comma decimal
    ("500 g.", 15.00),             # Period after unit
    ("3x75cl", 22.00),             # Compact multipack format
    
    # Edge cases
    ("", 10.00),
    (None, 10.00),
    ("invalid unit", 10.00),
    ("just text", 12.00),
]

def analyze_result(result: Dict[str, Any], test_name: str) -> Dict[str, Any]:
    """Analyze a single result for scoring."""
    analysis = {
        "test_name": test_name,
        "has_quantity": result.get("quantity") is not None,
        "has_unit_type": result.get("unit_type") is not None,
        "has_price_calc": result.get("price_per_base_unit") is not None,
        "has_base_unit": result.get("base_unit_type") is not None,
        "failed_completely": (
            result.get("quantity") is None and 
            result.get("unit_type") is None and
            result.get("price_per_base_unit") is None
        ),
        "result": result
    }
    return analysis

def score_implementation(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Score an implementation based on its results."""
    total_cases = len(results)
    
    scores = {
        "total_cases": total_cases,
        "complete_failures": sum(1 for r in results if r["failed_completely"]),
        "has_quantity": sum(1 for r in results if r["has_quantity"]),
        "has_unit_type": sum(1 for r in results if r["has_unit_type"]),
        "has_price_calc": sum(1 for r in results if r["has_price_calc"]),
        "has_base_unit": sum(1 for r in results if r["has_base_unit"]),
    }
    
    # Calculate success rate
    scores["success_rate"] = ((total_cases - scores["complete_failures"]) / total_cases) * 100
    scores["quantity_rate"] = (scores["has_quantity"] / total_cases) * 100
    scores["price_calc_rate"] = (scores["has_price_calc"] / total_cases) * 100
    
    return scores

def run_comprehensive_test():
    """Run the comprehensive comparison test."""
    print("🧪 COMPREHENSIVE UTILS COMPARISON TEST")
    print("=" * 70)
    print("Testing 3 implementations:")
    print("1. 📜 Current utils.py (original)")
    print("2. 🔄 utilsSUGGESTED.py (suggested refactor)")
    print("3. 🚀 utils_ENHANCED.py (enhanced + Danish patterns)")
    print("=" * 70)
    
    current_results = []
    suggested_results = []
    enhanced_results = []
    
    for i, (unit_string, price) in enumerate(COMPREHENSIVE_TEST_CASES, 1):
        print(f"\n📝 Test {i:2d}: '{unit_string}' with price {price}")
        
        # Test current implementation
        try:
            current_result = current_func(unit_string, price)
            current_analysis = analyze_result(current_result, "current")
        except Exception as e:
            current_result = {"error": str(e)}
            current_analysis = analyze_result(current_result, "current")
            print(f"❌ Current failed: {e}")
        
        # Test suggested implementation  
        try:
            suggested_result = suggested_func(unit_string, price)
            suggested_analysis = analyze_result(suggested_result, "suggested")
        except Exception as e:
            suggested_result = {"error": str(e)}
            suggested_analysis = analyze_result(suggested_result, "suggested")
            print(f"❌ Suggested failed: {e}")
        
        # Test enhanced implementation
        try:
            enhanced_result = enhanced_func(unit_string, price)
            enhanced_analysis = analyze_result(enhanced_result, "enhanced")
        except Exception as e:
            enhanced_result = {"error": str(e)}
            enhanced_analysis = analyze_result(enhanced_result, "enhanced")
            print(f"❌ Enhanced failed: {e}")
        
        # Quick comparison
        if current_analysis["failed_completely"] and not enhanced_analysis["failed_completely"]:
            print("🚀 Enhanced fixes a complete failure!")
        elif enhanced_analysis["has_price_calc"] and not current_analysis["has_price_calc"]:
            print("💰 Enhanced adds price calculation!")
        elif enhanced_analysis["has_quantity"] and not current_analysis["has_quantity"]:
            print("📏 Enhanced extracts quantity!")
        else:
            print("✅ Results similar")
        
        current_results.append(current_analysis)
        suggested_results.append(suggested_analysis)
        enhanced_results.append(enhanced_analysis)
    
    # Calculate scores
    current_scores = score_implementation(current_results)
    suggested_scores = score_implementation(suggested_results)
    enhanced_scores = score_implementation(enhanced_results)
    
    # Print comprehensive summary
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE RESULTS SUMMARY")
    print("=" * 70)
    
    print(f"{'Metric':<25} {'Current':<12} {'Suggested':<12} {'Enhanced':<12}")
    print("-" * 70)
    print(f"{'Success Rate':<25} {current_scores['success_rate']:>8.1f}%   {suggested_scores['success_rate']:>8.1f}%   {enhanced_scores['success_rate']:>8.1f}%")
    print(f"{'Complete Failures':<25} {current_scores['complete_failures']:>8d}     {suggested_scores['complete_failures']:>8d}     {enhanced_scores['complete_failures']:>8d}")
    print(f"{'Has Quantity':<25} {current_scores['has_quantity']:>8d}     {suggested_scores['has_quantity']:>8d}     {enhanced_scores['has_quantity']:>8d}")
    print(f"{'Has Price Calc':<25} {current_scores['has_price_calc']:>8d}     {suggested_scores['has_price_calc']:>8d}     {enhanced_scores['has_price_calc']:>8d}")
    print(f"{'Quantity Rate':<25} {current_scores['quantity_rate']:>8.1f}%   {suggested_scores['quantity_rate']:>8.1f}%   {enhanced_scores['quantity_rate']:>8.1f}%")
    print(f"{'Price Calc Rate':<25} {current_scores['price_calc_rate']:>8.1f}%   {suggested_scores['price_calc_rate']:>8.1f}%   {enhanced_scores['price_calc_rate']:>8.1f}%")
    
    # Recommendations
    print("\n" + "=" * 70)
    print("🎯 FINAL RECOMMENDATION")
    print("=" * 70)
    
    if enhanced_scores['success_rate'] > current_scores['success_rate']:
        improvement = enhanced_scores['success_rate'] - current_scores['success_rate']
        print(f"✅ DEPLOY ENHANCED VERSION!")
        print(f"   - Success rate improves by {improvement:.1f}%")
        print(f"   - Fixes {current_scores['complete_failures'] - enhanced_scores['complete_failures']} complete failures")
        print(f"   - Adds {enhanced_scores['has_price_calc'] - current_scores['has_price_calc']} price calculations")
        print(f"   - Maintains DB compatibility")
        print(f"   - Includes Danish-specific patterns")
    else:
        print("🤷 No significant improvement detected")
    
    return {
        "current": current_scores,
        "suggested": suggested_scores, 
        "enhanced": enhanced_scores
    }

if __name__ == "__main__":
    try:
        results = run_comprehensive_test()
        
        # Save detailed results
        with open("comprehensive_utils_test_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n💾 Detailed results saved to: comprehensive_utils_test_results.json")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
