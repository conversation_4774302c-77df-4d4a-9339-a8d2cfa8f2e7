from pydantic import BaseModel
from typing import Optional, List

# For creating a new user
class UserCreate(BaseModel):
    username: str
    password: str

# For reading user data (doesn't expose password)
class User(BaseModel):
    id: int
    username: str
    is_admin: bool = False
    preferred_model: Optional[str] = None
    query_history: Optional[List[str]] = None

    class Config:
        from_attributes = True # Replaces orm_mode = True

# For JWT token payload
class TokenData(BaseModel):
    username: Optional[str] = None

# For returning the token upon login
class Token(BaseModel):
    access_token: str
    refresh_token: str # Add refresh token
    token_type: str 