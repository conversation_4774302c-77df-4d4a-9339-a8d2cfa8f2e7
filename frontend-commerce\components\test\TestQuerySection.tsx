"use client";
import { useState, useEffect, useRef } from 'react';
import { queryOffers, getCatalogs, BackendCatalog, QueryResponse, BackendProduct } from '@/lib/backend';
import OfferList from '../landing/OfferList';
import { toast } from 'sonner';
import { buildThumbnailUrl, getStoreLogoUrl } from '@/lib/ui-helpers';
import { useSelectedCatalogsContext } from '@/lib/contexts/SelectedCatalogsContext';
import MarkdownAnswer from '@/components/ui/MarkdownAnswer';
import { gsap } from 'gsap';
import TestWrapper from './TestWrapper';

// Define the API base for image URLs
const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';

function TestQuerySectionContent() {
  const [question, setQuestion] = useState('');
  const [catalogs, setCatalogs] = useState<BackendCatalog[]>([]);
  const [catalogsLoading, setCatalogsLoading] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [answer, setAnswer] = useState<string | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<BackendProduct[]>([]);
  const [promptsShown, setPromptsShown] = useState(false);
  const [loadingStep, setLoadingStep] = useState(0);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [currentScanningStore, setCurrentScanningStore] = useState('');
  const [scannedStores, setScannedStores] = useState<string[]>([]);
  const progressRef = useRef<HTMLDivElement>(null);

  // Use selected catalogs from context
  const { selectedCatalogs, toggleCatalog, clearSelectedCatalogs } = useSelectedCatalogsContext();

  // Example prompts for the suggestion gallery
  const examplePrompts = [
    'Billige økologiske æg', 
    'Tilbud på Coca-Cola', 
    'Hvilke kataloger har jordbær?',
    'Bedste tilbud på kaffe denne uge',
    'Hvor finder jeg det billigste vaskepulver?'
  ];

  useEffect(() => {
    const fetchCatalogs = async () => {
      try {
        setCatalogsLoading(true);
        const data = await getCatalogs();
        setCatalogs(data);
      } catch (err) {
        console.error('Failed to load catalogs:', err);
        toast.error('Kunne ikke indlæse kataloger');
      } finally {
        setCatalogsLoading(false);
      }
    };
    
    fetchCatalogs();
  }, []);

  // Enhanced AI Processing with catalog scanning visualization
  const simulateAIProcessing = async () => {
    const stores = ['Netto', 'Rema 1000', 'Bilka', 'Føtex', 'Lidl', 'Spar', 'Meny'];

    const steps = [
      {
        message: "🔍 Analyserer dit spørgsmål...",
        detail: "Forstår hvad du leder efter",
        duration: 1200,
        icon: "🔍"
      },
      {
        message: "📚 Scanner kataloger...",
        detail: "Gennemgår alle tilgængelige tilbudsaviser",
        duration: 3000,
        icon: "📚",
        scanStores: true
      },
      {
        message: "🤖 AI behandler data...",
        detail: "Matcher produkter med dit spørgsmål",
        duration: 2200,
        icon: "🤖"
      },
      {
        message: "⚡ Finder bedste tilbud...",
        detail: "Rangerer resultater efter relevans og pris",
        duration: 1400,
        icon: "⚡"
      },
      {
        message: "✨ Færdiggør resultater...",
        detail: "Forbereder det perfekte svar til dig",
        duration: 1000,
        icon: "✨"
      }
    ];

    for (let i = 0; i < steps.length; i++) {
      const currentStep = steps[i];
      if (!currentStep) continue; // Failsafe: Skip if step is undefined

      setLoadingStep(i + 1);
      setLoadingMessage(currentStep.message);

      // Animate progress bar
      if (progressRef.current) {
        gsap.to(progressRef.current, {
          width: `${((i + 1) / steps.length) * 100}%`,
          duration: 0.5,
          ease: "power2.out"
        });
      }

      // Special handling for catalog scanning step
      if (currentStep.scanStores) {
        setScannedStores([]);
        for (let j = 0; j < stores.length; j++) {
          const currentStore = stores[j];
          if (!currentStore) continue; // Failsafe: Skip if store is undefined

          setCurrentScanningStore(currentStore);
          await new Promise(resolve => setTimeout(resolve, 300));
          setScannedStores(prev => [...prev, currentStore]);
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        setCurrentScanningStore('');
      } else {
        await new Promise(resolve => setTimeout(resolve, currentStep.duration || 1000));
      }
    }
  };

  // Use the selected catalogs from the shared context
  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!question.trim()) {
      toast.error('Indtast venligst et spørgsmål');
      return;
    }
    
    setLoading(true);
    setError(null);
    setAnswer(null);
    setRelatedProducts([]);
    setLoadingStep(0);
    setLoadingMessage('');

    try {
      // Start AI processing animation
      const processingPromise = simulateAIProcessing();

      // Pass the selectedCatalogs from context to the query
      const queryPromise = queryOffers(question.trim(), selectedCatalogs.length > 0 ? selectedCatalogs : undefined);

      // Wait for both animation and actual query to complete
      const [_, res] = await Promise.all([processingPromise, queryPromise]);
      
      // Check if the response contains an error from the AI model
      if (res.response && res.response.includes('Der opstod en fejl under behandling af din forespørgsel med AI-modellen')) {
        toast.error('AI-modellen kunne ikke gennemføre forespørgslen');
        setError('AI-modellen kunne ikke gennemføre forespørgslen. Prøv igen med et andet spørgsmål eller vælg flere kataloger.');
      } else {
        setAnswer(res.response);
        setRelatedProducts(res.products || []);
      }
    } catch (err) {
      toast.error('Kunne ikke hente svar');
      setError('Kunne ikke hente svar. Prøv igen senere.');
      console.error('Query error:', err);
    } finally {
      setLoading(false);
    }
  }

  return (
    <section className="bg-sky-100/50 backdrop-blur-md p-6 rounded-xl shadow-lg border border-sky-200/50">
      <h2 className="text-2xl font-bold text-gray-900">Spørg AI om tilbud</h2>
      
      {/* Example Prompts */}
      <div className="flex flex-wrap gap-2 text-sm my-4">
        {examplePrompts.map((prompt) => (
          <button 
            key={prompt} 
            type="button" 
            className="bg-white border border-gray-300 text-gray-900 px-4 py-3 rounded-full hover:bg-gray-50 hover:border-sky-400 hover:text-sky-700 hover:shadow-lg hover:shadow-sky-500/20 hover:scale-105 transition-all duration-200 active:scale-95"
            onClick={() => setQuestion(prompt)}
          >
            {prompt}
          </button>
        ))}
      </div>
      
      <form onSubmit={onSubmit} className="space-y-6">
        {/* Query Input */}
        <div className="flex gap-2">
          <input
            type="text"
            placeholder="Dit spørgsmål..."
            className="flex-1 bg-white/80 border border-gray-300 focus:border-sky-500 focus:ring-sky-500 rounded-lg px-4 py-3 shadow-inner text-gray-900 outline-none placeholder-gray-500"
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
          />
          <button
            type="submit"
            className="bg-white/60 text-gray-800 hover:bg-white/90 transition-colors px-3 py-1.5 rounded-full text-sm shadow-sm font-semibold shadow-md disabled:bg-sky-800/50 transition-all hover:shadow-lg hover:shadow-sky-500/30"
            disabled={loading}
          >
            {loading ? (
              <span className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-sky-500"></div>
                AI arbejder...
              </span>
            ) : (
              'Spørg'
            )}
          </button>
        </div>

        {/* Catalog Selection */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-800">Søg i kataloger:</h3>
            {selectedCatalogs.length > 0 && (
              <button
                type="button"
                onClick={clearSelectedCatalogs}
                className="text-sm text-sky-600 hover:text-sky-800 underline"
              >
                Ryd alle ({selectedCatalogs.length})
              </button>
            )}
          </div>
            
            {catalogsLoading ? (
            <div className="flex justify-center items-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-sky-500"></div>
            </div>
          ) : catalogs.length === 0 ? (
            <p className="text-sm text-gray-600">Ingen kataloger tilgængelige</p>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-3">
              {catalogs.map((catalog) => {
                // Use selectedCatalogs from context
                const isSelected = selectedCatalogs.includes(catalog.id);
                return (
                  <div 
                    key={catalog.id} 
                    // Use toggleCatalog from context
                    onClick={() => toggleCatalog(catalog.id)}
                    className={`cursor-pointer transition-all duration-300 ${isSelected ? 'scale-105' : 'scale-100 hover:scale-102'}`}
                  >
                    <div className={`h-20 w-full overflow-hidden rounded-lg border-2 shadow-md ${isSelected ? 'border-sky-400 shadow-lg shadow-sky-500/30 bg-sky-50/10' : 'border-gray-600/50 hover:border-sky-300 hover:shadow-lg hover:shadow-sky-500/10 bg-white/5 hover:bg-white/10'} active:scale-95 transition-all duration-200`}>
                      <div className="relative h-full w-full">
                        <img
                          src={catalog.store_logo_url || catalog.first_page_image_url || '/placeholder.png'}
                          alt={catalog.title}
                          className={`h-full w-full object-contain ${isSelected ? '' : 'opacity-80 group-hover:opacity-100'}`}
                        />
                        {isSelected && (
                          <div className="absolute inset-0 bg-sky-900/50 flex items-center justify-center">
                            <div className="bg-sky-500 rounded-full p-1">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </form>

      {/* Enhanced AI Processing Display */}
      {loading && (
        <div className="mt-6 p-6 bg-gradient-to-r from-sky-50 to-blue-50 border border-sky-200 rounded-lg shadow-lg">
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-sky-500"></div>
              <h3 className="text-lg font-semibold text-sky-800">AI Behandler Din Forespørgsel</h3>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-sky-100 rounded-full h-4 overflow-hidden shadow-inner">
              <div
                ref={progressRef}
                className="h-full bg-gradient-to-r from-sky-400 to-blue-500 rounded-full transition-all duration-500 shadow-sm"
                style={{ width: '0%' }}
              ></div>
            </div>

            {/* Current Step Display */}
            <div className="bg-white/60 rounded-lg p-4 border border-sky-100">
              <div className="flex items-start gap-3">
                <div className="text-2xl animate-pulse">{loadingMessage.split(' ')[0] || '🔍'}</div>
                <div className="flex-1">
                  <div className="text-sky-700 font-medium">{loadingMessage}</div>
                  <div className="text-sky-600 text-sm mt-1">
                    {loadingStep === 1 && "Forstår hvad du leder efter"}
                    {loadingStep === 2 && "Gennemgår alle tilgængelige tilbudsaviser"}
                    {loadingStep === 3 && "Matcher produkter med dit spørgsmål"}
                    {loadingStep === 4 && "Rangerer resultater efter relevans og pris"}
                    {loadingStep === 5 && "Forbereder det perfekte svar til dig"}
                  </div>
                </div>
              </div>
            </div>

            {/* Step Progress Indicators */}
            <div className="flex items-center justify-between">
              {[1, 2, 3, 4, 5].map((step) => (
                <div key={step} className="flex flex-col items-center gap-1">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                    step < loadingStep ? 'bg-green-500 text-white' :
                    step === loadingStep ? 'bg-sky-500 text-white animate-pulse' :
                    'bg-gray-200 text-gray-400'
                  }`}>
                    {step < loadingStep ? '✓' : step}
                  </div>
                  <div className={`text-xs transition-colors duration-300 ${
                    step <= loadingStep ? 'text-sky-600' : 'text-gray-400'
                  }`}>
                    {step === 1 && 'Analyse'}
                    {step === 2 && 'Scanner'}
                    {step === 3 && 'AI Match'}
                    {step === 4 && 'Rangering'}
                    {step === 5 && 'Resultat'}
                  </div>
                </div>
              ))}
            </div>

            {/* Catalog Scanning Visualization */}
            {loadingStep === 2 && (
              <div className="bg-white/80 rounded-lg p-4 border border-sky-100">
                <h4 className="text-sm font-semibold text-sky-700 mb-3">📚 Scanner butikker:</h4>
                <div className="grid grid-cols-4 gap-2">
                  {['Netto', 'Rema 1000', 'Bilka', 'Føtex', 'Lidl', 'Spar', 'Meny'].map((store) => (
                    <div
                      key={store}
                      className={`text-xs p-2 rounded border text-center transition-all duration-300 ${
                        currentScanningStore === store
                          ? 'bg-yellow-100 border-yellow-400 text-yellow-800 animate-pulse'
                          : scannedStores.includes(store)
                          ? 'bg-green-100 border-green-400 text-green-800'
                          : 'bg-gray-50 border-gray-200 text-gray-500'
                      }`}
                    >
                      {scannedStores.includes(store) ? '✓' : currentScanningStore === store ? '🔍' : '⏳'} {store}
                    </div>
                  ))}
                </div>
                {currentScanningStore && (
                  <div className="mt-2 text-xs text-sky-600 text-center">
                    Scanner {currentScanningStore}...
                  </div>
                )}
              </div>
            )}

            {/* Overall Progress */}
            <div className="text-center text-sm text-sky-600">
              Trin {loadingStep} af 5 • {Math.round((loadingStep / 5) * 100)}% færdig
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="mt-6 p-4 bg-red-100 border border-red-300 rounded-lg">
          <p className="text-red-700">{error}</p>
        </div>
      )}
      
      {/* Results Section (Answer & Products) */}
      {answer && (
        <div className="mt-6 space-y-6">
          {/* AI Answer */}
          <div className="p-5 bg-white/95 backdrop-blur-sm border border-sky-200 rounded-lg shadow-lg">
            <h3 className="text-xl font-bold mb-3 text-sky-600 flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
              AI Svar
            </h3>
            <MarkdownAnswer content={answer} />
          </div>

          {/* Related Products */}
          {relatedProducts.length > 0 && (
            <div className="rounded-lg p-5 bg-black/20 backdrop-blur-sm border border-gray-700/80 shadow-lg">
              <h3 className="text-xl font-bold mb-3 text-sky-400 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" />
                </svg>
                Relaterede Produkter
              </h3>
              <OfferList offers={relatedProducts} catalogs={catalogs} loading={loading} />
            </div>
          )}
        </div>
      )}
    </section>
  );
}

export default function TestQuerySection() {
  return (
    <TestWrapper
      fallbackTitle="Test Query Section"
      fallbackMessage="The query test component is disabled in production"
    >
      <TestQuerySectionContent />
    </TestWrapper>
  );
}
