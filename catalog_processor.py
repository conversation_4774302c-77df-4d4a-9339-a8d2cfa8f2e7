# File: catalog_processor.py

import os
from dotenv import load_dotenv
load_dotenv() # Load .env before other imports
import config
import re
import sys
import unicodedata
from pathlib import Path
from datetime import datetime, date, timedelta
from typing import Optional, Tuple, Dict, Any, List, Union
import json
import logging
import hashlib
import shutil
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import func
import subprocess
from pathlib import Path
import sys

# Add project root to path to allow absolute imports
project_root = Path(__file__).resolve().parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from database import SessionLocal, Store, Catalog, Base, engine, ProcessedCatalogHashes, CatalogProcessingQueue, Product, CatalogPage
from scrapers.scraper_core import sanitize_filename
from pdf_extractor import process_pdf_catalog_from_cloud

# Configure logging with optimized format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)-20s - %(levelname)-8s - %(message)s',
    datefmt='%d-%m %H:%M:%S',  # Optimized: dd-mm hh:mm:ss format saves tokens
    handlers=[
        logging.StreamHandler(),
    ]
)
logger = logging.getLogger('catalog_processor')

def print_processing_summary(summary_list):
    """
    Prints a clean, user-facing summary of catalog processing results.
    Each entry is a single readable line: [start, end] description
    Handles non-serializable objects like datetime/date.
    """
    def to_str(obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return str(obj)

    if not summary_list:
        print("No processing summary to display.")
        logger.info("No processing summary to display.")
        return
    print("\n=== Catalog Processing Summary ===")
    logger.info("=== Catalog Processing Summary ===")
    for entry in summary_list:
        # Handle entries that are lists or dicts
        if isinstance(entry, dict):
            line = ", ".join(f"{k}: {to_str(v)}" for k, v in entry.items())
        elif isinstance(entry, (list, tuple)) and len(entry) == 2:
            # e.g. ([start, end], description)
            date_part = entry[0]
            desc_part = entry[1]
            if isinstance(date_part, (list, tuple)) and len(date_part) == 2:
                line = f"[{to_str(date_part[0])}, {to_str(date_part[1])}] {to_str(desc_part)}"
            else:
                line = f"{to_str(entry)}"
        else:
            line = to_str(entry)
        print(line)
        logger.info(line)
    print("=== End of Summary ===\n")
    logger.info("=== End of Summary ===")

def normalize_danish_string_for_date_parsing(text: str) -> str:
    """
    Normalizes a Danish string for reliable date parsing.
    Handles mojibake, diacritics, and common variations in date strings.
    
    Args:
        text: The input string to normalize
        
    Returns:
        str: The normalized string with consistent formatting
    """
    if not text:
        return ""
    
    # Ensure text is a string and convert to lowercase
    text = str(text).lower()
    
    # Specific common mojibake or misinterpretations
    text = text.replace('gælder', 'gaelder')  # Normalize to ASCII
    text = text.replace('søn', 'son')         # Normalize to ASCII
    text = text.replace('lør', 'lor')         # Normalize to ASCII
    text = text.replace('ø', 'o')             # Replace remaining ø
    text = text.replace('æ', 'ae')            # Replace æ
    text = text.replace('å', 'aa')            # Replace å
    text = text.replace('é', 'e')             # Common accented e
    text = text.replace('á', 'a')             # Common accented a
    text = text.replace('í', 'i')             # Common accented i
    text = text.replace('ó', 'o')             # Common accented o
    text = text.replace('ú', 'u')             # Common accented u
    text = text.replace('ý', 'y')             # Common accented y
    
    # Normalize remaining Unicode characters to their closest ASCII equivalent
    try:
        text = unicodedata.normalize('NFKD', text).encode('ascii', 'ignore').decode('ascii')
    except Exception as e:
        logger.warning(f"Unicodedata normalization failed for '{text}': {e}")
    
    # General cleaning
    text = text.strip()
    text = re.sub(r'\s+', ' ', text)  # Normalize multiple spaces
    text = re.sub(r'[^\w\s.-]', '', text)  # Remove special chars except spaces, dots, and hyphens
    text = text.rstrip('*')  # Remove trailing asterisks
    
    logger.debug(f"Normalized string: '{text}'")
    return text
import subprocess
import json
import logging
from pathlib import Path
import shutil
import itertools
from datetime import datetime, date, timedelta
import re

# Ensure the project root is in the Python path FOR THIS SCRIPT'S CONTEXT
# This helps if catalog_processor is run from a different CWD, though less critical
# now that we set PYTHONPATH for the subprocess.
project_root = Path(__file__).resolve().parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    # Assuming config.py is in the project_root
    from config import CLOUD_CATALOG_PREFIX, CLOUD_IMAGE_PREFIX, BASE_DIR # Updated for cloud storage
    from cloud_storage import cloud_storage, upload_catalog_pdf
    # Assuming database.py is in project_root
    from database import SessionLocal, Store, Catalog, Product, CatalogPage, ProcessedCatalogHashes, CatalogProcessingQueue # Added new models
    from sqlalchemy.orm import Session # For type hinting
except ImportError as e:
    # This initial print might go to a log if logging is configured before this point
    # but critical for early failure diagnosis.
    print(f"CRITICAL IMPORT ERROR in catalog_processor.py: {e}. Check PYTHONPATH and file locations.", file=sys.stderr)
    # Attempt to set up basic logging for further errors if above imports failed
    logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%d-%m %H:%M:%S')
    logging.getLogger("catalog_processor_bootstrap_error").error(f"ImportError: {e}", exc_info=True)
    sys.exit(1)

# --- Logging Setup ---
# Use a specific logger for this module.
logger = logging.getLogger("catalog_processor")
# Ensure logging level is set (it might be inherited or set by basicConfig elsewhere)
if not logger.handlers: # Basic config if no handlers are attached yet
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%d-%m %H:%M:%S')
logger.setLevel(logging.INFO) # Or DEBUG for more verbosity

import hashlib # For SHA256 calculation


# --- Configuration ---
# BASE_DIR should be correctly imported from config.py (project root)
SCRAPER_CONFIG_DIR = BASE_DIR / "scrapers" / "configs"
RUN_SCRAPER_FILE_PATH = BASE_DIR / "scrapers" / "run_scraper.py"
VENV_PYTHON_EXECUTABLE = BASE_DIR / ".venv" / "Scripts" / "python.exe" # Adjust if your venv is elsewhere or named differently


def calculate_sha256(file_path: Union[str, Path]) -> Optional[str]:
    """Calculate SHA256 hash of file with enhanced validation and error handling."""
    file_path = Path(file_path)
    if not file_path.exists():
        logger.error(f"File not found for hash calculation: {file_path}")
        return None
    
    # Verify file is readable and not empty
    try:
        file_size = file_path.stat().st_size
        if file_size == 0:
            logger.error(f"Cannot calculate hash of empty file: {file_path} (0 bytes)")
            return None
        
        if file_size < 100:  # Very small files are suspicious
            logger.warning(f"File is suspiciously small for PDF: {file_path} ({file_size} bytes)")
            # Continue anyway, but log the warning
    except Exception as e:
        logger.warning(f"Could not check file size for {file_path}: {e}")
        # Continue with hash calculation attempt

    try:
        h = hashlib.sha256()
        with open(file_path, 'rb') as f:
            # Read and update hash in chunks to avoid loading large files into memory
            bytes_read = 0
            for chunk in iter(lambda: f.read(4096), b""):
                bytes_read += len(chunk)
                h.update(chunk)
                
        if bytes_read == 0:
            logger.error(f"File opened but no bytes could be read: {file_path}")
            return None
            
        logger.info(f"Successfully calculated hash for {file_path} ({bytes_read} bytes read)")
        return h.hexdigest()
    except PermissionError:
        logger.error(f"Permission denied when reading file for hash: {file_path}")
        return None
    except IOError as io_err:
        logger.error(f"IO Error calculating hash for {file_path}: {io_err}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error calculating hash for {file_path}: {str(e)}", exc_info=True)
        return None



def get_or_create_catalog_safe(db_session: Session, store_id: int, title: str, valid_from: date, valid_to: date, pdf_path: str, is_active_today: bool) -> Catalog | None:
    """
    Safely get or create a catalog with proper duplicate prevention.
    Uses date-only comparison to prevent time-based duplicates.

    Returns:
        Catalog object if successful, None if error
    """
    try:
        # Check if catalog already exists using date-only comparison
        existing_catalog = db_session.query(Catalog).filter(
            Catalog.store_id == store_id,
            func.date(Catalog.valid_from) == valid_from,
            func.date(Catalog.valid_to) == valid_to
        ).first()

        if existing_catalog:
            logger.info(f"Found existing catalog (ID: {existing_catalog.id}) for store {store_id} for date range {valid_from} - {valid_to}")
            # Update path and active status if they have changed
            if existing_catalog.pdf_path != str(pdf_path):
                existing_catalog.pdf_path = str(pdf_path)
                logger.info(f"Updated PDF path for catalog {existing_catalog.id}.")
            existing_catalog.is_latest_for_today = is_active_today
            return existing_catalog
        else:
            logger.info(f"Creating new catalog entry for store {store_id} ({valid_from} - {valid_to}).")
            new_catalog = Catalog(
                store_id=store_id,
                title=title,
                valid_from=valid_from,
                valid_to=valid_to,
                pdf_path=str(pdf_path),
                created_at=datetime.now(),
                is_latest_for_today=is_active_today
            )
            db_session.add(new_catalog)
            db_session.flush()  # Flush to get the new ID
            logger.info(f"Created new catalog with ID: {new_catalog.id}")
            return new_catalog

    except Exception as e:
        logger.error(f"Error in get_or_create_catalog_safe: {e}", exc_info=True)
        return None


def get_or_create_store(db_session: Session, store_name: str, store_logo_url: str | None) -> Store | None:
    """Gets an existing store by name or creates a new one, returning the Store object."""
    store_entry = db_session.query(Store).filter(Store.name == store_name).first()
    if not store_entry:
        logger.info(f"Store '{store_name}' not found, creating new entry.")
        store_entry = Store(name=store_name, logo_url=store_logo_url)
        db_session.add(store_entry)
        try:
            db_session.flush()  # Flush to get store_entry.id without full commit yet
            logger.info(f"Successfully created and flushed new store: {store_name} with ID: {store_entry.id}")
        except Exception as e_flush:
            logger.error(f"Error flushing session for new store {store_name}: {e_flush}", exc_info=True)
            db_session.rollback() # Rollback the add operation
            return None
    return store_entry

# --- Danish Month Mapping ---
DANISH_MONTHS = {
    "januar": 1, "februar": 2, "marts": 3, "april": 4, "maj": 5, "juni": 6,
    "juli": 7, "august": 8, "september": 9, "oktober": 10, "november": 11, "december": 12
}

def parse_catalog_date_range(raw_date_str: str, legacy_date_range_str: str = None, store_name: str = None):
    """
    Unified date parsing function for catalog entries.
    Tries all available strategies in order, logs attempts, and returns (start_date, end_date) and the successfully parsed string.
    Args:
        raw_date_str: The main date string from the catalog (e.g. from page title or date element)
        legacy_date_range_str: Optional legacy/fallback date string
        store_name: Store name (for store-specific logic)
    Returns:
        ((start_date, end_date), used_string) or (None, last_tried_string)
    """
    date_strings_to_try = []
    if raw_date_str and str(raw_date_str).strip():
        normalized_raw_date = normalize_danish_string_for_date_parsing(raw_date_str)
        date_strings_to_try.append(normalized_raw_date)
        logger.debug(f"Normalized raw_date_str: '{raw_date_str}' -> '{normalized_raw_date}'")
    if legacy_date_range_str and str(legacy_date_range_str).strip() and legacy_date_range_str != raw_date_str:
        normalized_legacy_date = normalize_danish_string_for_date_parsing(legacy_date_range_str)
        date_strings_to_try.append(normalized_legacy_date)
        logger.debug(f"Normalized legacy_date_range_str: '{legacy_date_range_str}' -> '{normalized_legacy_date}'")

    store_name_for_logic = (store_name or '').lower() if store_name else ''
    last_attempted_string = None
    for date_str in date_strings_to_try:
        last_attempted_string = date_str
        # 1. Store-specific: Dagrofa week title
        if store_name_for_logic in ["meny", "spar", "min købmand", "min kobmand", "min kobmænd"]:
            logger.debug(f"Trying Dagrofa week title parsing for '{date_str}'")
            parsed = parse_dagrofa_retail_week_title(date_str)
            if parsed:
                return parsed, date_str
        # 2. Verbose Danish date range
        logger.debug(f"Trying verbose Danish date range parsing for '{date_str}'")
        parsed = parse_verbose_danish_date_range(date_str)
        if parsed:
            return parsed, date_str
        # 3. Standard Danish date range
        logger.debug(f"Trying standard Danish date range parsing for '{date_str}'")
        parsed = parse_danish_date_range(date_str)
        if parsed:
            return parsed, date_str
        # 4. Week number title (legacy/fallback)
        logger.debug(f"Trying week number title parsing for '{date_str}'")
        parsed = parse_week_number_title(date_str)
        if parsed:
            return parsed, date_str
        # 5. Single date to range (edge/fallback)
        logger.debug(f"Trying single date to range parsing for '{date_str}'")
        parsed = parse_single_date_to_range(date_str)
        if parsed:
            return parsed, date_str
    logger.warning(f"All date parsing strategies failed for: '{last_attempted_string}'")
    return None, last_attempted_string

# --- Danish Month Mapping ---
DANISH_MONTHS = {
    "januar": 1, "februar": 2, "marts": 3, "april": 4, "maj": 5, "juni": 6,
    "juli": 7, "august": 8, "september": 9, "oktober": 10, "november": 11, "december": 12
}


def parse_danish_date_range(date_range_str: str) -> tuple[date, date] | None:
    """
    Parses a Danish date range string like "24. - 30. maj" or "24. maj - 30. juni".
    Handles various separators and year rollover for ranges like "28. december - 3. januar".
    
    Note: Input is expected to be pre-normalized by normalize_danish_string_for_date_parsing().
    
    Args:
        date_range_str: Pre-normalized date range string
        
    Returns:
        tuple[date, date] | None: (start_date, end_date) or None if parsing fails
    """
    logger.debug(f"parse_danish_date_range input (pre-normalized): '{date_range_str}'")
    
    try:
        # Split into parts using the normalized separator
        parts = [p.strip() for p in date_range_str.split(' - ', 1)]
        if len(parts) != 2:
            logger.debug(f"Could not split into two parts: '{date_range_str}'")
            return None

        start_str_raw, end_str_raw = parts[0].strip(), parts[1].strip()
        current_year = datetime.now().year
        current_month = datetime.now().month
        logger.debug(f"Parsing date range: start='{start_str_raw}', end='{end_str_raw}'")

        def parse_part(part_str: str, known_month: int | None = None, known_year: int | None = None) -> date | None:
            """Helper to parse a single date part (start or end of range)."""
            logger.debug(f"  Parsing date part: '{part_str}' (known_month={known_month}, known_year={known_year})")
            
            # Handle pure numeric formats like "24.05.2024" or "24/05/2024" (dots and slashes normalized)
            numeric_date_match = re.match(r'^(\d{1,2})[.](\d{1,2})(?:[.](\d{2,4}))?$', part_str)
            if numeric_date_match:
                day, month, year = numeric_date_match.groups()
                try:
                    day_int = int(day)
                    month_int = int(month)
                    year_int = int(year) if year else current_year
                    if year_int < 100:  # Handle 2-digit years
                        year_int += 2000 if year_int < 50 else 1900
                    return date(year_int, month_int, day_int)
                except (ValueError, TypeError) as e:
                    logger.debug(f"  Failed to parse numeric date '{part_str}': {e}")
            
            # Handle text month formats like "24. maj" or "24 maj"
            part_str = part_str.lower()
            
            # Extract day (must be at start of string)
            day_match = re.match(r'^(\d{1,2})\.?\s*', part_str)
            if not day_match:
                logger.debug(f"  Could not find day number in '{part_str}'")
                return None
                
            day = int(day_match.group(1))
            remaining = part_str[day_match.end():].strip()
            logger.debug(f"  Extracted day: {day}, remaining: '{remaining}'")
            
            # Extract month name (if present)
            month = None
            for month_name, month_num in DANISH_MONTHS.items():
                if month_name in remaining:
                    month = month_num
                    # Remove the month name from remaining
                    remaining = remaining.replace(month_name, '').strip()
                    logger.debug(f"  Found month: {month_name} ({month})")
                    break
            
            # If month not found but we have a known month from the other part, use that
            if month is None and known_month is not None:
                month = known_month
                logger.debug(f"  Using provided month: {month}")
            
            # If still no month, try to guess based on current date
            if month is None:
                month = current_month
                logger.debug(f"  Assuming current month: {month}")
            
            # Handle year (if present) - look for 4-digit number (2-digit years already handled in normalization)
            year_match = re.search(r'(20\d{2})', remaining)
            if year_match:
                year = int(year_match.group(0))
                if year < 100:  # Handle 2-digit years
                    year += 2000 if year < 50 else 1900
                logger.debug(f"  Found year in string: {year}")
            else:
                year = current_year
                # Adjust year if month suggests we've rolled over to next year
                if month < current_month and known_month is not None and known_month > month:
                    year += 1
                    logger.debug(f"  Adjusted year to next year: {year}")
            
            try:
                result = date(year, month, day)
                logger.debug(f"  Parsed date: {result}")
                return result
            except ValueError as e:
                logger.error(f"  Invalid date components: year={year}, month={month}, day={day} for input '{part_str}': {e}")
                return None

        # First parse the end date (second part of the range)
        end_date_obj = parse_part(end_str_raw)
        if not end_date_obj:
            logger.debug(f"Could not parse end date from: '{end_str_raw}' in range '{date_range_str}'")
            return None
            
        # Then parse the start date, using the end date's month/year as hints if needed
        start_date_obj = parse_part(
            start_str_raw, 
            known_month=end_date_obj.month,
            known_year=end_date_obj.year
        )
        if not start_date_obj:
            logger.error(f"Could not parse start date from: '{start_str_raw}' in range '{date_range_str}'")
            return None
            
        logger.debug(f"Initial parse - Start: {start_date_obj}, End: {end_date_obj}")
        
        # Handle year rollover cases
        # Case 1: Start month > End month (e.g., Dec - Jan) - end should be next year
        if start_date_obj.month > end_date_obj.month:
            # If start is in a later month than end, assume end is in the next year
            # unless the dates are already properly ordered (e.g., Jan 1 - Dec 31)
            if start_date_obj <= end_date_obj.replace(year=start_date_obj.year):
                # Already properly ordered (e.g., Jan 1 - Dec 31)
                pass
            else:
                # Move end date to next year (e.g., Dec 15 - Jan 15)
                end_date_obj = end_date_obj.replace(year=start_date_obj.year + 1)
                logger.debug(f"Adjusted end date to next year: {end_date_obj}")
        # Case 2: Start month < End month but start date > end date (e.g., Dec 15 - Jan 10)
        elif start_date_obj.month < end_date_obj.month and start_date_obj > end_date_obj:
            # Start date is after end date in the same year, so end date must be next year
            end_date_obj = end_date_obj.replace(year=start_date_obj.year + 1)
            logger.debug(f"Adjusted end date to next year: {end_date_obj}")
        # Case 3: Same month but start day > end day (e.g., Jan 25 - Jan 10)
        elif start_date_obj.month == end_date_obj.month and start_date_obj.day > end_date_obj.day:
            # If days are out of order in the same month, assume end is next month
            # But since we don't know the month, we'll assume it's a year rollover
            end_date_obj = end_date_obj.replace(year=start_date_obj.year + 1)
            logger.debug(f"Adjusted end date to next year (same month case): {end_date_obj}")
            
        # Final validation
        if start_date_obj > end_date_obj:
            logger.error(f"Date range is invalid after all adjustments: {start_date_obj} to {end_date_obj}")
            return None
            
        logger.debug(f"Final date range: {start_date_obj} to {end_date_obj}")
        return start_date_obj, end_date_obj

    except Exception as e:
        logger.error(f"Unexpected error parsing Danish date string '{date_range_str}': {e}")
        return None


def parse_week_number_title(title_str: str) -> tuple[date, date] | None:
    """
    Parses a title string like "mk uge 2225" or "spar uge 22-2025" to a Friday-Thursday date range.
    Assumes the week number corresponds to a 7-day period starting on Friday of that week.
    
    Note: Input is expected to be pre-normalized by normalize_danish_string_for_date_parsing().
    """
    logger.debug(f"parse_week_number_title input: '{title_str}'")
    # Input is already normalized to lowercase, so we can simplify the pattern
    match = re.search(r"(?:uge|ug)\s*(\d{1,2})[\s._-]*(\d{2}(\d{2})?)?", title_str)
    if not match:
        logger.debug(f"No week/year pattern found in title: '{title_str}'")
        return None

    week_str = match.group(1)
    year_full_str = match.group(2) # This might be YYYY or YY
    # year_short_str = match.group(3) # This is the optional second YY part if year was YYYY (unused here)

    try:
        week_number = int(week_str)
        if not (1 <= week_number <= 53): # Week 53 is possible
            logger.error(f"Invalid week number {week_number} in title: '{title_str}'")
            return None

        current_century = (datetime.now().year // 100) * 100
        if year_full_str:
            if len(year_full_str) == 4:
                year = int(year_full_str)
            elif len(year_full_str) == 2:
                year_val = int(year_full_str)
                # Heuristic for 2-digit years: if > current_year_last_two_digits + 5, assume last century
                # This is very rough. Prefer YYYY. For now, assume current century.
                year = current_century + year_val
            else:
                logger.error(f"Unexpected year format '{year_full_str}' in title: '{title_str}'")
                return None
        else:
            # If no year is explicitly mentioned, this parser cannot determine the year.
            logger.debug(f"No year found alongside week in title: '{title_str}'. This parser requires explicit year with week.")
            return None
        
        # ISO week date: year, week, weekday (1=Monday, 7=Sunday)
        # We want Friday of that week as start_date
        start_date = datetime.fromisocalendar(year, week_number, 5).date() # 5 for Friday
        end_date = start_date + timedelta(days=6) # End date is Thursday (6 days after Friday)
        
        logger.info(f"Parsed week number title '{title_str}' to range: {start_date} - {end_date}")
        return start_date, end_date

    except ValueError as ve: # Catches errors from int() or fromisocalendar()
        logger.error(f"Error converting week/year in title '{title_str}': {ve}")
        return None
    except Exception as e:
        logger.exception(f"Unexpected error in parse_week_number_title for '{title_str}': {e}")
        return None


def parse_single_date_to_range(date_str: str, default_duration_days: int = 6) -> tuple[date, date] | None:
    """
    Parses a single date string in YYYY-MM-DD, DD.MM.YYYY, or DD/MM/YYYY formats.
    Returns a (start_date, end_date) tuple, where end_date is start_date + default_duration_days.
    
    Note: Input is expected to be pre-normalized by normalize_danish_string_for_date_parsing().
    """
    logger.debug(f"parse_single_date_to_range input: '{date_str}'")
    
    if not date_str:
        logger.debug("Empty date string provided")
        return None

    parsed_date = None
    formats_to_try = ["%Y-%m-%d", "%d.%m.%Y", "%d/%m/%Y"] # Common date formats

    for fmt in formats_to_try:
        try:
            parsed_date = datetime.strptime(date_str.strip(), fmt).date()
            break # Found a parsable format
        except ValueError:
            continue # Try next format

    if parsed_date:
        start_date = parsed_date
        end_date = start_date + timedelta(days=default_duration_days)
        logger.info(f"Parsed single date string '{date_str}' to range: {start_date} - {end_date}")
        return start_date, end_date
    else:
        logger.debug(f"Could not parse single date string '{date_str}' with available formats.")
        return None


def _clean_and_normalize_date_text(text: str) -> str:
    """
    Cleans and normalizes date text by fixing common mojibake and normalizing input.
    Returns the cleaned text.
    
    Handles various encodings, mojibake, and normalizes date strings to a consistent format.
    """
    if not text or not isinstance(text, str):
        return text or ""
    
    # --- Stage 1: Fix common mojibake and encoding issues ---
    # Map of corrupted characters to their correct Danish equivalents
    mojibake_map = {
        # Common mojibake patterns for Danish characters
        '�': 'æ', '�': 'ø', '�': 'å', '�': 'Æ', '�': 'Ø', '�': 'Å',
        'Ã¦': 'æ', 'Ã¸': 'ø', 'Ã¥': 'å', 'Ã†': 'Æ', 'Ã˜': 'Ø', 'Ã…': 'Å',
        'â€"': '-', 'â€”': '-', 'â€"': '-', 'â€�': '"', 'â€™': "'",
        'â€˜': "'", 'â€˜': "'", 'â€œ': '"', 'â€�': '"',
        'Ã¼': 'ü', 'Ã«': 'ë', 'Ã¯': 'ï', 'Ã«': 'ë', 'Ã«': 'ë',
        'Ã©': 'é', 'Ã¨': 'è', 'Ãª': 'ê', 'Ã±': 'ñ', 'Ã¡': 'á', 'Ã ': 'à',
        
        # Common corrupted words in Danish
        'gÃ¦lder': 'gælder', 'GÃ¦lder': 'Gælder',
        'sÃ¸n': 'søn', 'SÃ¸n': 'Søn',
        'lÃ¸r': 'lør', 'LÃ¸r': 'Lør',
        'torsdag': 'torsdag', 'fredag': 'fredag', 'lÃ¸rdag': 'lørdag',
        'sÃ¸ndag': 'søndag', 'mandag': 'mandag', 'tirsdag': 'tirsdag',
        'onsdag': 'onsdag', 'torsdag': 'torsdag',
        
        # Common corrupted month names
        'januar': 'januar', 'februar': 'februar', 'marts': 'marts',
        'april': 'april', 'maj': 'maj', 'juni': 'juni', 'juli': 'juli',
        'august': 'august', 'september': 'september', 'oktober': 'oktober',
        'november': 'november', 'december': 'december',
    }
    
    # Replace mojibake characters
    for bad, good in mojibake_map.items():
        text = text.replace(bad, good)
    
    # --- Stage 2: Normalize text ---
    # Convert to lowercase for consistent processing
    text = text.lower()
    
    # Replace HTML entities and newlines
    text = text.replace('&lt;br&gt;', ' ').replace('\n', ' ').replace('\r', ' ').strip()
    
    # Normalize various date range separators to a standard format
    range_separators = [
        r'til\s+og\s+med',  # 'til og med'
        r't\.?o\.?m\.?',    # 't.o.m', 't.o.m.', 'tom', etc.
        r'frem\s+til',       # 'frem til'
        r'frem\s+til\s+og\s+med',  # 'frem til og med'
        r'-',                 # simple dash
        r'–',                 # en dash
        r'—',                 # em dash
        r'\s+til\s+',       # ' til ' with spaces
    ]
    
    # Replace all variations with a standard separator
    for sep in range_separators:
        text = re.sub(sep, ' - ', text, flags=re.IGNORECASE)
    
    # --- Stage 3: Clean up common prefixes and suffixes ---
    # Common date prefixes to remove (case insensitive)
    prefix_patterns = [
        r'^gælder\s*(?:fra)?\s*',
        r'^fra\s*(?:d\.?\s*)?',
        r'^d\.?\s*',
        r'^den\s*',
        r'^i?\s*perioden?\s*',
        r'^ugens?\s*tilbud\s*',
        r'^tilbuds?\s*',
        r'^denne\s*uge\s*',
        r'^i?\s*dag(?:en)?\s*',
        r'^i?\s*morgen\s*',
        r'^i?\s*aften\s*',
        r'^i?\s*nat(?:ten)?\s*',
    ]
    
    # Remove prefixes iteratively until no more matches
    changed = True
    while changed:
        changed = False
        for pattern in prefix_patterns:
            new_text = re.sub(pattern, '', text, flags=re.IGNORECASE).strip()
            if new_text != text:
                changed = True
                text = new_text
    
    # Remove common suffixes
    suffix_patterns = [
        r'[\.\*,\s]+$',  # Trailing punctuation and spaces
        r'(?:\s+tilbud)?\s*$',
        r'\s+til\s+og\s+med\s*$',
        r'\s+t\.?o\.?m\.?\s*$',
    ]
    
    for pattern in suffix_patterns:
        text = re.sub(pattern, '', text, flags=re.IGNORECASE).strip()
    
    # --- Stage 4: Final normalization ---
    # Normalize various dashes and spaces around them
    text = re.sub(r'\s*[-–—]\s*', ' - ', text)
    
    # Remove any remaining special characters except those used in dates
    text = re.sub(r'[^\w\s\.\-/]', ' ', text)
    
    # Normalize multiple spaces to single space
    text = re.sub(r'\s+', ' ', text).strip()
    
    # Special case: Remove 'd.' before dates if it's not part of a word
    text = re.sub(r'(^|\s)d\.\s+(\d)', r'\1\2', text)
    
    # Ensure proper spacing around dots in dates (e.g., '1.jan' -> '1. jan')
    text = re.sub(r'(\d)\.([a-zæøå])', r'\1. \2', text)
    
    # Final whitespace cleanup
    text = re.sub(r'\s+', ' ', text).strip()
    
    logger.debug(f"Cleaned date text: '{text}'")
    return text


def parse_verbose_danish_date_range(text_blob: str) -> tuple[date, date] | None:
    """
    Parses verbose Danish date range strings by finding date components.
    This approach is robust against varied wording and formatting.
    Examples:
    "Avisen gælder fra fredag den 23. maj til og med torsdag den 5. juni 2025"
    "Søn. d. 1. juni - lør. d. 7. juni"
    "Gælder fra d. 30. maj til og med d. 5. juni*"
    "Gælder fra fredag d. 30. maj t.o.m. torsdag d. 12. juni"
    "10.06.2025 - 14.06.2025"
    """
    if not text_blob or not isinstance(text_blob, str):
        logger.debug(f"Invalid input to parse_verbose_danish_date_range: '{text_blob}' is None or not a string.")
        return None

    original_text_blob = str(text_blob)
    logger.debug(f"Parsing verbose date range from: '{original_text_blob}'")

    # --- Stage 1: Basic Cleaning ---
    # Lowercase and fix the most common mojibake without being destructive.
    text = text_blob.lower()
    mojibake_map = {
        'g�lder': 'gælder', 's�n.': 'søn.', 'l�r.': 'lør.',
        'Ã¦': 'æ', 'Ã¸': 'ø', 'Ã¥': 'å',
        'tilbudsavis': '', # remove this word as it can interfere
    }
    for bad, good in mojibake_map.items():
        text = text.replace(bad, good)

    # First, try to extract dates with weekday names for better accuracy
    # Pattern: "torsdag den 10. juli til og med onsdag den 16. juli 2025"
    weekday_pattern = r'(?:man(?:dag)?|tirs(?:dag)?|ons(?:dag)?|tors(?:dag)?|fre(?:dag)?|lør(?:dag)?|søn(?:dag)?)\s+den\s+(\d{1,2})\.\s+([a-zæøå]+)(?:\s+(\d{4}))?'
    weekday_matches = re.findall(weekday_pattern, text, re.IGNORECASE)

    if len(weekday_matches) >= 2:
        logger.debug(f"Found weekday pattern matches: {weekday_matches}")
        # Convert weekday matches to standard format for processing
        matches = []
        for day, month, year in weekday_matches[:2]:  # Take first two matches
            matches.append((day, month, year if year else None))
        logger.debug(f"Converted weekday matches to standard format: {matches}")
    else:
        # Remove day names as they are not needed for parsing and can contain noise
        day_names = r'\b(?:man(?:dag)?|tirs(?:dag)?|ons(?:dag)?|tors(?:dag)?|fre(?:dag)?|lør(?:dag)?|søn(?:dag)?)\.?'
        text = re.sub(day_names, '', text, flags=re.IGNORECASE)

        # Normalize whitespace and remove some noise characters
        text = re.sub(r'[\s\*\.,]+', ' ', text).strip()
        logger.debug(f"Cleaned text for regex search: '{text}'")

        # --- Stage 2: Find all date-like components ---
        # This regex is designed to find individual dates in various formats.
        # It captures (day, month, year) for each date found.
        # CG1: Day (1-2 digits)
        # CG2: Month (Danish name or 1-2 digits)
        # CG3: Year (optional, 2 or 4 digits)
        date_pattern = re.compile(
            r"(\d{1,2})\.?"  # CG1: Day
            r"\s*[./-]?\s*"
            r"([a-zæøå]+|\d{1,2})"  # CG2: Month (name or number)
            r"(?:\s*[./-]?\s*(\d{4}|\d{2}))?",  # CG3: Year (optional)
            re.IGNORECASE
        )

        matches = date_pattern.findall(text)
        logger.debug(f"Found {len(matches)} potential date components: {matches}")

        if len(matches) < 2:
            logger.debug(f"Could not find at least two date components in '{text}'")
            # Fallback to a simpler DD.MM-DD.MM pattern if needed
            simple_match = re.search(r'(\d{1,2})\.(\d{1,2})\s*-\s*(\d{1,2})\.(\d{1,2})', text)
            if simple_match:
                sd, sm, ed, em = simple_match.groups()
                matches = [(sd, sm, None), (ed, em, None)]
                logger.debug(f"Found simple DD.MM-DD.MM pattern, using matches: {matches}")
            else:
                return None


    # --- Stage 3: Construct date objects from the first two matches ---
    def _create_date_from_match(match_tuple, default_year):
        day_str, month_raw, year_str = match_tuple
        
        day = int(day_str)
        
        month: int | None
        if month_raw.isdigit():
            month = int(month_raw)
        else:
            month = DANISH_MONTHS.get(month_raw.lower().strip('.'))

        if not month or not (1 <= month <= 12):
            logger.warning(f"Invalid month value: '{month_raw}'")
            return None

        year = default_year
        if year_str:
            if len(year_str) == 2:
                year = 2000 + int(year_str)
            elif len(year_str) == 4:
                year = int(year_str)
        
        try:
            return date(year, month, day)
        except ValueError as e:
            logger.error(f"Failed to create date from (y={year}, m={month}, d={day}): {e}")
            return None

    current_year = datetime.now().year
    current_month = datetime.now().month

    # Smart year inference: if we're in December and the date is in January-March, assume next year
    # if we're in January-March and the date is in October-December, assume previous year
    def smart_year_inference(month_num, default_year):
        if current_month >= 10 and month_num <= 3:  # Oct-Dec -> Jan-Mar next year
            return default_year + 1
        elif current_month <= 3 and month_num >= 10:  # Jan-Mar -> Oct-Dec previous year
            return default_year - 1
        return default_year

    # Helper function to create date with smart year inference
    def create_date_with_smart_year(day, month, year):
        if year:
            return _create_date_from_match((day, month, year), current_year)
        else:
            # No year provided, use smart inference
            temp_date = _create_date_from_match((day, month, None), current_year)
            if temp_date:
                inferred_year = smart_year_inference(temp_date.month, current_year)
                if inferred_year != current_year:
                    temp_date = temp_date.replace(year=inferred_year)
            return temp_date

    # Parse start date with smart year inference
    start_date_obj = create_date_with_smart_year(matches[0][0], matches[0][1], matches[0][2])

    # For the end date, if its year is missing, it should default to the start date's year
    if start_date_obj and not matches[1][2]:
        # Use start date's year for end date when no year specified
        end_date_obj = _create_date_from_match(matches[1], start_date_obj.year)
    else:
        # End date has year or start date failed
        end_date_obj = create_date_with_smart_year(matches[1][0], matches[1][1], matches[1][2])

    if not start_date_obj or not end_date_obj:
        logger.error(f"Failed to create valid date objects from matches: {matches}")
        return None

    # --- Stage 4: Handle Year Rollover ---
    # If end date has no explicit year and is earlier than start date, it's next year.
    if not matches[1][2] and end_date_obj < start_date_obj:
        end_date_obj = end_date_obj.replace(year=end_date_obj.year + 1)
        
    # A final sanity check
    if start_date_obj > end_date_obj:
        # This could happen if start date has no year, but end date does, and they cross a year boundary
        # e.g. "december 2024 - januar 2025". If current year is 2025, start might become 2025-12...
        if not matches[0][2]: # If start year was inferred
             start_date_obj = start_date_obj.replace(year=start_date_obj.year - 1)

    if start_date_obj > end_date_obj:
         logger.warning(f"Final parsed start date {start_date_obj} is after end date {end_date_obj}. Input: '{original_text_blob}'")
         # Don't return, let it be handled downstream if needed, but log it.

    logger.info(f"Successfully parsed '{original_text_blob}' -> Start: {start_date_obj}, End: {end_date_obj}")
    return start_date_obj, end_date_obj


def parse_dagrofa_retail_week_title(title_str: str) -> tuple[date, date] | None:
    """
    Parses a Dagrofa group (Meny, Spar, Min Købmand) title string like "meny uge 2425".
    The promotional week starts on Friday of (ISO Week WW - 1).
    
    Note: Input is expected to be pre-normalized by normalize_danish_string_for_date_parsing().
    """
    logger.debug(f"parse_dagrofa_retail_week_title input: '{title_str}'")
    
    # Input is already normalized to lowercase, so we can simplify the pattern
    # and remove the IGNORECASE flag
    # First try to match "uge 2925" format (week 29, year 25)
    match = re.search(r"(?:uge|ug)\s*(\d{2})(\d{2})(?:\D|$)", title_str)
    if match:
        logger.debug(f"Dagrofa: Found 4-digit week+year pattern: week {match.group(1)}, year {match.group(2)}")
    else:
        # Fallback to original pattern for other formats
        match = re.search(r"(?:uge|ug)\s*(\d{1,2})[\s._-]*(\d{2}(\d{2})?)?", title_str)
    if not match:
        logger.debug(f"Dagrofa: No week/year pattern found in title: '{title_str}'")
        return None

    week_str = match.group(1)
    year_str = match.group(2) # This might be YYYY or YY

    try:
        week_number = int(week_str)
        if not (1 <= week_number <= 53):
            logger.error(f"Dagrofa: Invalid week number {week_number} in title: '{title_str}'")
            return None

        current_year = datetime.now().year
        current_century = (current_year // 100) * 100

        # Handle different year formats
        if year_str:
            if len(year_str) == 4:
                year = int(year_str)
            elif len(year_str) == 2:
                year_val = int(year_str)
                # For 2-digit years, assume 20XX for values 00-50, 19XX for 51-99
                if year_val <= 50:
                    year = 2000 + year_val
                else:
                    year = 1900 + year_val
            else:
                logger.error(f"Dagrofa: Unexpected year format '{year_str}' in title: '{title_str}'")
                return None
        else:
            # If no year specified, assume current year
            year = current_year
            logger.debug(f"Dagrofa: No year found in title '{title_str}', assuming current year: {year}")

        effective_year = year
        effective_week = week_number - 1

        if effective_week == 0:
            effective_year -= 1
            # Determine the last week number of the previous year
            # December 28th is always in the last ISO week of its year.
            effective_week = date(effective_year, 12, 28).isocalendar()[1]
            logger.info(f"Dagrofa: Week {week_number} for {year} adjusted to week {effective_week} of {effective_year}")
        
        # ISO week date: year, week, weekday (1=Monday, 7=Sunday)
        # We want Friday (5) of that (effective_week - 1) as start_date
        start_date_obj = datetime.fromisocalendar(effective_year, effective_week, 5).date() # 5 for Friday
        end_date_obj = start_date_obj + timedelta(days=6) # End date is Thursday (6 days after Friday)
        
        logger.info(f"Dagrofa: Parsed week title '{title_str}' (original week {week_number}/{year}) to range: {start_date_obj} - {end_date_obj}")
        return start_date_obj, end_date_obj

    except ValueError as ve:
        logger.error(f"Dagrofa: Error converting week/year in title '{title_str}': {ve}")
        return None
    except Exception as e:
        logger.exception(f"Dagrofa: Unexpected error in parse_dagrofa_retail_week_title for '{title_str}': {e}")
        return None


def upload_pdf_to_cloud_storage(local_pdf_path_str: str, store_name: str, start_date_obj: date, end_date_obj: date) -> str | None:
    """Uploads the scraped PDF to Google Cloud Storage and returns the cloud path."""
    local_pdf_path = Path(local_pdf_path_str)

    # Enhanced input validation
    if not local_pdf_path.exists():
        logger.error(f"Local PDF not found: {local_pdf_path}")
        return None

    if not local_pdf_path.is_file():
        logger.error(f"Path exists but is not a file: {local_pdf_path}")
        return None

    # Check if file is empty or too small
    try:
        file_size = local_pdf_path.stat().st_size
        if file_size == 0:
            logger.error(f"PDF file is empty (0 bytes): {local_pdf_path}")
            return None

        if file_size < 100:  # Very small files are likely not valid PDFs
            logger.warning(f"PDF file is suspiciously small ({file_size} bytes): {local_pdf_path}")
            # Continue anyway, but log the warning
    except Exception as e:
        logger.warning(f"Couldn't check file size: {e}")
        # Continue with upload attempt

    try:
        # Check if cloud storage is available
        if not cloud_storage.is_available():
            logger.error("Cloud storage not available")
            return None

        # Generate cloud storage filename using StoragePaths
        from storage_factory import StoragePaths

        sanitized_store_name = sanitize_filename(store_name)
        new_filename = f"{sanitized_store_name}_{start_date_obj.strftime('%Y%m%d')}_{end_date_obj.strftime('%Y%m%d')}.pdf"

        try:
            # Use StoragePaths for consistent path construction
            country_code = StoragePaths.detect_country_code(store_name)
            date_period = StoragePaths.generate_date_period(start_date_obj, end_date_obj)
            cloud_path = StoragePaths.catalog_path(
                filename=new_filename,
                country_code=country_code,
                store_name=store_name,
                date_period=date_period
            )
        except Exception as e:
            logger.warning(f"Failed to generate StoragePaths for {new_filename}, falling back to legacy: {e}")
            cloud_path = f"{CLOUD_CATALOG_PREFIX}{new_filename}"

        # Upload to cloud storage
        if cloud_storage.upload_file(str(local_pdf_path), cloud_path):
            logger.info(f"✅ Successfully uploaded PDF to cloud storage: {cloud_path}")

            # Clean up local file after successful upload
            try:
                local_pdf_path.unlink()
                logger.debug(f"🧹 Cleaned up local PDF: {local_pdf_path}")
            except Exception as cleanup_error:
                logger.warning(f"⚠️ Failed to clean up local PDF: {cleanup_error}")

            return cloud_path
        else:
            logger.error(f"❌ Failed to upload PDF to cloud storage: {cloud_path}")
            return None

    except Exception as e:
        logger.exception(f"Unexpected error uploading PDF to cloud storage: {e}")
        return None


def update_database(db_session: Session, scraper_data: dict, parsed_start_date: date, parsed_end_date: date, cloud_pdf_path: str, is_active_today: bool, pdf_hash: str) -> dict:
    """Updates database with catalog info, handles PDF hash checking, and manages processing queue."""
    store_name = scraper_data.get("store_name")
    store_logo_url = scraper_data.get("store_logo_url")
    catalog_title_from_scraper = scraper_data.get("catalog_title", f"{store_name} Catalog")

    summary = {
        "store": store_name,
        "title": catalog_title_from_scraper,
        "pdf_path": str(cloud_pdf_path),
        "valid_from": parsed_start_date.isoformat(),
        "valid_to": parsed_end_date.isoformat(),
        "db_status": "unchanged",
        "queue_status": "not_queued",
        "pdf_hash": None
    }

    store_entry = get_or_create_store(db_session, store_name, store_logo_url)
    if not store_entry:
        logger.error(f"Failed to get or create store: {store_name}")
        summary["db_status"] = "error_store_creation_failed"
        return summary
    db_session.flush()  # Ensure store_entry.id is available

    # Use the pre-calculated hash passed as parameter
    new_pdf_hash = pdf_hash
    summary["pdf_hash"] = new_pdf_hash

    # 🚨 ATOMIC: Catalog creation now happens inside PDF processing
    # This ensures catalog + pages are created together or not at all

    # Check for an existing ProcessedCatalogHashes entry based on the unique constraint fields
    ph_entry = db_session.query(ProcessedCatalogHashes).filter(
        ProcessedCatalogHashes.store_id == store_entry.id,
        ProcessedCatalogHashes.valid_from_date == parsed_start_date,
        ProcessedCatalogHashes.valid_to_date == parsed_end_date,
        ProcessedCatalogHashes.pdf_content_hash == new_pdf_hash
    ).first()

    should_queue = False
    if ph_entry:
        logger.info(f"Found existing ProcessedCatalogHashes entry (ID: {ph_entry.id}) with status '{ph_entry.status}'.")
        # If it wasn't successful, we should re-process it.
        if ph_entry.status != 'SUCCESS':
            logger.info(f"Re-marking hash entry as PENDING for re-processing.")
            ph_entry.status = 'PENDING'
            ph_entry.processed_at = datetime.now()
            # catalog_db_id will be set after successful PDF processing
            should_queue = True
        else:
            logger.info("This exact PDF has already been processed successfully. No action needed.")
            summary["queue_status"] = "not_requeued_successful_match"
    else:
        logger.info(f"Creating new ProcessedCatalogHashes for hash {new_pdf_hash}.")
        new_ph_entry = ProcessedCatalogHashes(
            store_id=store_entry.id,
            catalog_title_from_scraper=catalog_title_from_scraper,
            valid_from_date=parsed_start_date,
            valid_to_date=parsed_end_date,
            pdf_content_hash=new_pdf_hash,
            status='PENDING',
            processed_at=datetime.now()
            # catalog_db_id will be set after successful PDF processing
        )
        db_session.add(new_ph_entry)
        should_queue = True

    # Process PDF immediately to create CatalogPage entries
    if should_queue:
        logger.info(f"Processing PDF immediately: {cloud_pdf_path}")

        try:
            # Convert dates to datetime objects for pdf_extractor
            valid_from_dt = datetime.combine(parsed_start_date, datetime.min.time())
            valid_to_dt = datetime.combine(parsed_end_date, datetime.min.time())

            # 🚨 CRITICAL: Process the PDF from cloud storage to create CatalogPage entries
            # This MUST succeed or we'll have orphaned catalogs
            try:
                processed_catalog, image_paths = process_pdf_catalog_from_cloud(
                    cloud_pdf_path=cloud_pdf_path,  # This is the cloud path
                    store_name=store_name,
                    title=catalog_title_from_scraper,
                    valid_from=valid_from_dt,
                    valid_to=valid_to_dt,
                    attempt_date_extraction=False,  # We already have the dates
                    db=db_session
                )
            except Exception as pdf_error:
                logger.error(f"❌ CRITICAL PDF PROCESSING FAILURE for {store_name}: {pdf_error}")
                # Don't create catalog entry if PDF processing fails
                summary["queue_status"] = f"pdf_processing_failed: {str(pdf_error)[:200]}"
                return summary

            if processed_catalog and image_paths:
                catalog_db_id = processed_catalog.id
                logger.info(f"✅ Successfully processed PDF for catalog {catalog_db_id}: {len(image_paths)} pages extracted")

                # Add to CatalogProcessingQueue for AI processing
                existing_queue_entry = db_session.query(CatalogProcessingQueue).filter(
                    CatalogProcessingQueue.catalog_db_id == catalog_db_id,
                    CatalogProcessingQueue.pdf_content_hash == new_pdf_hash
                ).first()

                if not existing_queue_entry:
                    logger.info(f"Adding catalog {catalog_db_id} to processing queue for AI analysis")
                    queue_entry = CatalogProcessingQueue(
                        catalog_db_id=catalog_db_id,
                        pdf_path_to_process=str(cloud_pdf_path),
                        pdf_content_hash=new_pdf_hash,
                        status='PENDING',
                        added_at=datetime.now()
                    )
                    db_session.add(queue_entry)
                    summary["queue_status"] = f"queued_for_ai_processing_{len(image_paths)}_pages"
                else:
                    logger.info(f"Catalog {catalog_db_id} already in processing queue with status: {existing_queue_entry.status}")
                    summary["queue_status"] = f"already_queued_{existing_queue_entry.status}_{len(image_paths)}_pages"

                # Update ProcessedCatalogHashes status to indicate PDF processing complete
                if ph_entry:
                    ph_entry.status = 'PENDING'  # PDF processed, now pending AI analysis
                    ph_entry.processed_at = datetime.now()
                    ph_entry.catalog_db_id = catalog_db_id
                else:
                    # Find the entry we just created
                    ph_entry = db_session.query(ProcessedCatalogHashes).filter(
                        ProcessedCatalogHashes.catalog_db_id == catalog_db_id,
                        ProcessedCatalogHashes.pdf_content_hash == new_pdf_hash
                    ).first()
                    if ph_entry:
                        ph_entry.status = 'PENDING'  # PDF processed, now pending AI analysis
                        ph_entry.processed_at = datetime.now()

                summary["db_status"] = "success_with_pages"

            else:
                logger.error(f"❌ Failed to process PDF - no catalog created")

                # Update ProcessedCatalogHashes status to FAILED
                if ph_entry:
                    ph_entry.status = 'FAILED'
                    ph_entry.processed_at = datetime.now()

                summary["queue_status"] = "processing_failed"

        except Exception as e:
            logger.error(f"❌ Error processing PDF: {str(e)}", exc_info=True)

            # Update ProcessedCatalogHashes status to FAILED
            if ph_entry:
                ph_entry.status = 'FAILED'
                ph_entry.processed_at = datetime.now()

            summary["queue_status"] = f"processing_error_{str(e)[:100]}"

    return summary


def cleanup_expired_catalogs(db_session: SessionLocal, today_date: date):
    logger.info(f"--- Starting cleanup of expired catalogs (older than {today_date}) ---")
    try:
        expired_catalogs = db_session.query(Catalog).filter(Catalog.valid_to < today_date).all()
        
        if not expired_catalogs:
            logger.info("No expired catalogs found to clean up.")
            return

        logger.info(f"Found {len(expired_catalogs)} expired catalogs to process for deletion.")
        catalogs_deleted_count = 0

        for catalog in expired_catalogs:
            logger.info(f"Processing expired catalog ID: {catalog.id}, Title: '{catalog.title}', Valid to: {catalog.valid_to}")
            try:
                # ✅ NEW: Use cloud storage for file deletion instead of local paths
                from cloud_storage import delete_catalog_files

                logger.info(f"🗑️ Deleting cloud files for catalog ID {catalog.id}...")
                deletion_results = delete_catalog_files(catalog.pdf_path, catalog.id)

                if deletion_results['pdf_deleted']:
                    logger.info(f"✅ Deleted PDF from cloud storage: {catalog.pdf_path}")
                else:
                    logger.warning(f"⚠️ PDF not found or failed to delete: {catalog.pdf_path}")

                if deletion_results['images_deleted'] > 0:
                    logger.info(f"✅ Deleted {deletion_results['images_deleted']} images from cloud storage for catalog {catalog.id}")
                else:
                    logger.warning(f"⚠️ No images found to delete for catalog {catalog.id}")

                # Log any errors
                for error in deletion_results['errors']:
                    logger.error(f"❌ File deletion error for catalog {catalog.id}: {error}")

                # 3. Delete database records (in correct order)
                logger.debug(f"Deleting database records for catalog ID {catalog.id}...")

                # (FIX) Delete rows from dependent tables to avoid foreign key violations.
                # These tables have a foreign key pointing to the catalogs table.
                db_session.query(CatalogProcessingQueue).filter(CatalogProcessingQueue.catalog_db_id == catalog.id).delete(synchronize_session=False)
                db_session.query(ProcessedCatalogHashes).filter(ProcessedCatalogHashes.catalog_db_id == catalog.id).delete(synchronize_session=False)

                db_session.query(Product).filter(Product.catalog_id == catalog.id).delete(synchronize_session=False)
                db_session.query(CatalogPage).filter(CatalogPage.catalog_id == catalog.id).delete(synchronize_session=False)
                db_session.delete(catalog)
                logger.info(f"Successfully marked catalog ID {catalog.id} and its related Product/CatalogPage/ProcessedCatalogHashes entries for DB deletion.")
                catalogs_deleted_count += 1

            except Exception as e_item:
                logger.error(f"Error processing catalog ID {catalog.id} for deletion: {e_item}", exc_info=True)
                # Log and continue, attempt a single commit for successful ones.

        if catalogs_deleted_count > 0:
            db_session.commit()
            logger.info(f"Committed deletions for {catalogs_deleted_count} expired catalogs.")
        else:
            logger.info("No actual deletions were made to commit (either no expired catalogs or errors prevented processing).")

    except Exception as e_main_cleanup:
        logger.error(f"An error occurred during the main cleanup_expired_catalogs process: {e_main_cleanup}", exc_info=True)
        db_session.rollback() # Rollback any pending changes if the overall process fails badly


def cleanup_duplicate_catalogs(db_session: SessionLocal):
    """
    Clean up duplicate catalogs for the same store with overlapping date ranges.
    Keeps the most recent catalog and removes older duplicates.
    """
    logger.info("--- Starting cleanup of duplicate catalogs ---")

    try:
        stores = db_session.query(Store).all()
        total_duplicates_removed = 0

        for store in stores:
            logger.info(f"🔍 Checking for duplicates in store: {store.name}")

            # Get all catalogs for this store, ordered by creation date (newest first)
            catalogs = db_session.query(Catalog).filter(
                Catalog.store_id == store.id
            ).order_by(Catalog.created_at.desc()).all()

            if len(catalogs) <= 1:
                logger.info(f"✅ Store {store.name} has {len(catalogs)} catalog(s), no duplicates possible")
                continue

            # Group catalogs by date range overlap
            duplicates_to_remove = []

            for i, catalog_a in enumerate(catalogs):
                if catalog_a in duplicates_to_remove:
                    continue

                for j, catalog_b in enumerate(catalogs[i+1:], i+1):
                    if catalog_b in duplicates_to_remove:
                        continue

                    # Check if date ranges overlap
                    if (catalog_a.valid_from <= catalog_b.valid_to and
                        catalog_a.valid_to >= catalog_b.valid_from):

                        # Keep the newer catalog (catalog_a since list is sorted by creation date desc)
                        logger.info(f"📅 Found overlapping catalogs for {store.name}:")
                        logger.info(f"  - Keeping: ID {catalog_a.id} ({catalog_a.valid_from} to {catalog_a.valid_to}) - Created: {catalog_a.created_at}")
                        logger.info(f"  - Removing: ID {catalog_b.id} ({catalog_b.valid_from} to {catalog_b.valid_to}) - Created: {catalog_b.created_at}")
                        duplicates_to_remove.append(catalog_b)

            # Also check for malformed filenames (like "bilka__.pdf")
            for catalog in catalogs:
                if catalog.pdf_path and ('__.' in catalog.pdf_path or catalog.pdf_path.endswith('__.pdf')):
                    logger.info(f"🗑️ Found malformed filename: {catalog.pdf_path} (Catalog ID: {catalog.id})")
                    if catalog not in duplicates_to_remove:
                        duplicates_to_remove.append(catalog)

            # Remove duplicates
            for duplicate_catalog in duplicates_to_remove:
                try:
                    logger.info(f"🗑️ Removing duplicate catalog ID {duplicate_catalog.id} for {store.name}")

                    # Delete cloud files
                    from cloud_storage import delete_catalog_files
                    deletion_results = delete_catalog_files(duplicate_catalog.pdf_path, duplicate_catalog.id)

                    if deletion_results['pdf_deleted']:
                        logger.info(f"✅ Deleted duplicate PDF: {duplicate_catalog.pdf_path}")
                    if deletion_results['images_deleted'] > 0:
                        logger.info(f"✅ Deleted {deletion_results['images_deleted']} duplicate images")

                    # Delete database records
                    db_session.query(CatalogProcessingQueue).filter(CatalogProcessingQueue.catalog_db_id == duplicate_catalog.id).delete(synchronize_session=False)
                    db_session.query(ProcessedCatalogHashes).filter(ProcessedCatalogHashes.catalog_db_id == duplicate_catalog.id).delete(synchronize_session=False)
                    db_session.query(Product).filter(Product.catalog_id == duplicate_catalog.id).delete(synchronize_session=False)
                    db_session.query(CatalogPage).filter(CatalogPage.catalog_id == duplicate_catalog.id).delete(synchronize_session=False)
                    db_session.delete(duplicate_catalog)

                    total_duplicates_removed += 1

                except Exception as e:
                    logger.error(f"❌ Error removing duplicate catalog ID {duplicate_catalog.id}: {e}")

        if total_duplicates_removed > 0:
            db_session.commit()
            logger.info(f"✅ Successfully removed {total_duplicates_removed} duplicate catalogs")
        else:
            logger.info("✅ No duplicate catalogs found to remove")

    except Exception as e:
        logger.error(f"❌ Error during duplicate cleanup: {e}", exc_info=True)
        db_session.rollback()


def cleanup_orphaned_files(db_session: SessionLocal):
    """
    Clean up files in cloud storage that don't exist in database.
    This handles orphaned files and malformed filenames.
    """
    logger.info("--- Starting cleanup of orphaned cloud files ---")

    try:
        from cloud_storage import cloud_storage

        if not cloud_storage.is_available():
            logger.warning("⚠️ Cloud storage not available, skipping orphaned file cleanup")
            return

        # Get all valid PDF paths from database
        db_pdf_paths = set()
        catalogs = db_session.query(Catalog).all()
        for catalog in catalogs:
            if catalog.pdf_path:
                db_pdf_paths.add(catalog.pdf_path)

        # Get all valid image paths from database
        db_image_paths = set()
        pages = db_session.query(CatalogPage).all()
        for page in pages:
            if page.image_path:
                db_image_paths.add(page.image_path)

        logger.info(f"📊 Database contains {len(db_pdf_paths)} PDF paths and {len(db_image_paths)} image paths")

        # Check cloud storage for orphaned/malformed PDFs
        cloud_pdfs = cloud_storage.list_files(prefix="catalogs/")
        orphaned_count = 0
        malformed_count = 0

        for pdf_path in cloud_pdfs:
            if not pdf_path.endswith('.pdf'):
                continue

            # Check for malformed filenames
            if ('__.' in pdf_path or pdf_path.endswith('__.pdf') or
                'gitkeep' in pdf_path.lower()):
                logger.info(f"🗑️ Deleting malformed file: {pdf_path}")
                if cloud_storage.delete_file(pdf_path):
                    malformed_count += 1
                else:
                    logger.error(f"❌ Failed to delete malformed file: {pdf_path}")

            # Check for orphaned files (in cloud but not in database)
            elif pdf_path not in db_pdf_paths:
                logger.info(f"🗑️ Deleting orphaned PDF: {pdf_path}")
                if cloud_storage.delete_file(pdf_path):
                    orphaned_count += 1
                else:
                    logger.error(f"❌ Failed to delete orphaned PDF: {pdf_path}")

        # Check cloud storage for orphaned images
        cloud_images = cloud_storage.list_files(prefix="images/")
        orphaned_images = 0

        for image_path in cloud_images:
            if not image_path.endswith('.png'):
                continue

            if image_path not in db_image_paths:
                logger.info(f"🗑️ Deleting orphaned image: {image_path}")
                if cloud_storage.delete_file(image_path):
                    orphaned_images += 1
                else:
                    logger.error(f"❌ Failed to delete orphaned image: {image_path}")

        logger.info(f"✅ Orphaned file cleanup completed: {orphaned_count} PDFs, {orphaned_images} images, {malformed_count} malformed files deleted")

    except Exception as e:
        logger.error(f"❌ Error during orphaned file cleanup: {e}", exc_info=True)


def main():
    """Main function to process all scraper configurations and update the database."""
    # Verify that the database URL is loaded correctly and consistently
    db_url_from_env = os.getenv('DATABASE_URL')
    db_url_from_config = config.DATABASE_URL
    logger.info(f"DB URL from environment: {db_url_from_env}")
    logger.info(f"DB URL from config module: {db_url_from_config}")
    if db_url_from_env != db_url_from_config:
        logger.error("CRITICAL: Mismatch between database URL in environment and config module! Check .env loading order.")
    else:
        logger.info("Database URL is consistent.")
    logger.info("--- Starting catalog processing workflow ---")

    # To process only specific scrapers, list their config filenames here.
    # Example: TARGET_SCRAPER_CONFIGS = ["netto.json", "bilka.json"]
    # Leave empty to process all scrapers.
    TARGET_SCRAPER_CONFIGS = []

    # Set to True to disable processing all but the target scrapers.
    # If TARGET_SCRAPER_CONFIGS is empty, this has no effect.
    DEBUG_MODE = True if TARGET_SCRAPER_CONFIGS else False

    today = date.today()
    processing_summary = []
    db = None
    try:
        db = SessionLocal()
        cleanup_expired_catalogs(db, today)
        cleanup_duplicate_catalogs(db)  # ✅ Clean up duplicates after expired cleanup
        cleanup_orphaned_files(db)     # ✅ Clean up orphaned files after duplicates
        logger.info("Marking all existing catalogs with is_latest_for_today = False.")
        db.query(Catalog).update({"is_latest_for_today": False})
        db.commit()
        logger.info("Initial marking complete.")

        all_configs = [f for f in SCRAPER_CONFIG_DIR.iterdir() if f.is_file() and f.name.endswith('.json')]
        
        if DEBUG_MODE and TARGET_SCRAPER_CONFIGS:
            scraper_configs_to_run = [f for f in all_configs if f.name in TARGET_SCRAPER_CONFIGS]
            logger.warning(f"DEBUG MODE: Processing only {len(scraper_configs_to_run)} target scraper(s): {TARGET_SCRAPER_CONFIGS}")
        else:
            scraper_configs_to_run = all_configs
        
        if not scraper_configs_to_run:
            logger.warning(f"No scraper JSON configuration files found to process in {SCRAPER_CONFIG_DIR}. Exiting.")
            return
        
        logger.info(f"Found {len(scraper_configs_to_run)} scraper configurations to process: {[f.name for f in scraper_configs_to_run]}")

        for i, config_file_path in enumerate(scraper_configs_to_run, 1):
            config_filename = config_file_path.name
            logger.info(f"--- Starting scraper {i}/{len(scraper_configs_to_run)}: {config_filename} ---")

            try:
                # run_scraper now returns a list, which is empty on failure or if no catalogs are found.
                scraped_catalogs_data = run_scraper(config_file_path)

                if not scraped_catalogs_data:
                    logger.warning(f"--- Finished scraper {i}/{len(scraper_configs_to_run)}: {config_filename}. Found 0 catalog(s). ---")
                    processing_summary.append({
                        "scraper_config": config_filename,
                        "status": "No Catalogs Returned or Scraper Failed"
                    })
                    # A single scraper failing shouldn't stop the whole process.
                    # We commit any changes from previous successful scrapers before continuing.
                    db.commit()
                    # Free memory early
                    scraped_catalogs_data = None
                    import gc; gc.collect()
                    db.expunge_all()
                    continue

                logger.info(f"Scraper '{config_filename}' returned {len(scraped_catalogs_data)} catalog entries.")
                for catalog_data in scraped_catalogs_data:
                    summary = process_catalog_entry(db, catalog_data, config_filename, today)
                    processing_summary.append(summary)

                # Commit after all catalogs from a single scraper have been processed.
                logger.info(f"Committing database changes for scraper {config_filename}.")
                db.commit()

                logger.info(f"--- Finished scraper {i}/{len(scraper_configs_to_run)}: {config_filename}. Found {len(scraped_catalogs_data)} catalog(s). ---")

            except Exception as scraper_error:
                logger.error(f"--- ERROR in scraper {i}/{len(scraper_configs_to_run)}: {config_filename}. Error: {scraper_error} ---", exc_info=True)
                processing_summary.append({
                    "scraper_config": config_filename,
                    "status": f"Error: {str(scraper_error)}"
                })
                # Rollback any uncommitted changes from this scraper
                db.rollback()
                # Continue with next scraper
                continue
            finally:
                # Aggressive cleanup after each scraper
                import gc
                gc.collect()
                db.expunge_all()

                # Add a small delay between scrapers to prevent resource conflicts
                if i < len(scraper_configs_to_run):  # Don't delay after the last scraper
                    import time
                    logger.info(f"Waiting 3 seconds before next scraper...")
                    time.sleep(3)

        print_processing_summary(processing_summary)
    except Exception as e:
        logger.exception("A critical error occurred in the main processing loop. Rolling back any uncommitted changes.")
        if db:
            db.rollback()
    finally:
        if db:
            db.close()
            logger.debug("Database session closed.")
    logger.info("--- Catalog processing workflow finished. ---")


def run_single_scraper(store_name: str) -> dict:
    """
    Runs a scraper for a single store by name and processes the results.

    Args:
        store_name: The name of the store to scrape (e.g., "meny", "365discount")

    Returns:
        A dict with status and details about the scraping operation
    """
    # Map store names to config files
    store_config_map = {
        "meny": "meny.json",
        "365discount": "discount365.json",
        "discount365": "discount365.json",  # Alternative name
        "brugsen": "brugsen.json",
        "min købmand": "minkobmand.json",
        "min kobmand": "minkobmand.json",
        "minkobmand": "minkobmand.json",  # Direct config name
        "netto": "netto.json",
        "lidl": "lidl.json",
        "spar": "spar.json",
        "superbrugsen": "superbrugsen.json",
        "rema1000": "rema1000.json",
        "rema 1000": "rema1000.json",
        "fotex": "fotex.json",
        "foetex": "fotex.json",  # Common misspelling
        "føtex": "fotex.json",   # Danish spelling
        "bilka": "bilka.json"
    }

    store_name_lower = store_name.lower()
    config_filename = store_config_map.get(store_name_lower)

    if not config_filename:
        logger.error(f"No scraper config found for store: {store_name}")
        return {"status": "Failed", "message": f"No scraper config found for store: {store_name}"}

    config_file_path = SCRAPER_CONFIG_DIR / config_filename
    if not config_file_path.exists():
        logger.error(f"Scraper config file not found: {config_file_path}")
        return {"status": "Failed", "message": f"Config file not found: {config_filename}"}

    logger.info(f"Running single scraper for {store_name} using config: {config_filename}")

    try:
        # Get database session
        db = SessionLocal()
        today = date.today()

        # Run the scraper
        scraped_catalogs_data = run_scraper(config_file_path)

        if not scraped_catalogs_data:
            logger.warning(f"No catalogs returned from scraper for {store_name}")
            return {"status": "Failed", "message": f"No catalogs returned from scraper for {store_name}"}

        # Process each catalog from the scraper
        processing_results = []
        for catalog_data in scraped_catalogs_data:
            try:
                result = process_catalog_entry(db, catalog_data, config_filename, today)
                processing_results.append(result)
                logger.info(f"Processed catalog: {result}")
            except Exception as e:
                logger.error(f"Error processing catalog entry: {e}", exc_info=True)
                processing_results.append({
                    "scraper_config": config_filename,
                    "status": "Failed",
                    "error": str(e)
                })

        # Commit all changes
        db.commit()
        db.close()

        successful_count = sum(1 for r in processing_results if r.get("status") == "Queued")

        return {
            "status": "Queued" if successful_count > 0 else "Failed",
            "message": f"Processed {len(processing_results)} catalogs, {successful_count} queued for parsing",
            "results": processing_results
        }

    except Exception as e:
        logger.error(f"Error in run_single_scraper for {store_name}: {e}", exc_info=True)
        if 'db' in locals():
            db.rollback()
            db.close()
        return {"status": "Failed", "message": f"Error running scraper: {str(e)}"}


def run_scraper(config_file_path: Path) -> list:
    """
    Runs a scraper subprocess and returns its JSON output as a list.

    The scraper script is expected to always output a JSON list to stdout.
    If the scraper fails, it will output an empty list '[]'.
    All scraper logs and errors are written to stderr.

    Args:
        config_file_path: The path to the scraper's JSON configuration file.

    Returns:
        A list of scraped catalog data, which may be empty.
    """
    command = [
        "python",
        "-m",
        "scrapers.run_scraper",
        config_file_path.name
    ]
    logger.info(f"Executing command: {' '.join(command)}")

    scraper_env = {**os.environ, "PYTHONPATH": str(BASE_DIR)}

    # 🔍 DEBUG: Log subprocess execution details
    logger.info(f"🔍 SUBPROCESS DEBUG INFO:")
    logger.info(f"   Command: {command}")
    logger.info(f"   Working directory: {BASE_DIR}")
    logger.info(f"   Config file path: {config_file_path}")
    logger.info(f"   Config file exists: {config_file_path.exists()}")
    logger.info(f"   Python executable: {subprocess.run(['python', '--version'], capture_output=True, text=True).stdout.strip()}")
    logger.info(f"   PYTHONPATH: {scraper_env.get('PYTHONPATH', 'NOT SET')}")
    logger.info(f"   DATABASE_URL set: {'DATABASE_URL' in scraper_env}")
    logger.info(f"   GOOGLE_API_KEY set: {'GOOGLE_API_KEY' in scraper_env}")
    logger.info(f"   Current working dir: {os.getcwd()}")

    try:
        logger.info(f"Starting scraper subprocess for '{config_file_path.name}' with 5-minute timeout...")

        # 🔍 DEBUG: Start timing
        import time
        start_time = time.time()

        process = subprocess.run(
            command,
            capture_output=True,
            text=False,  # capture binary
            cwd=str(BASE_DIR),
            env=scraper_env,
            check=False,
            timeout=300  # 5-minute timeout per scraper
        )

        # 🔍 DEBUG: Log execution results
        end_time = time.time()
        execution_time = end_time - start_time
        logger.info(f"🔍 SUBPROCESS EXECUTION RESULTS:")
        logger.info(f"   Execution time: {execution_time:.2f} seconds")
        logger.info(f"   Return code: {process.returncode}")
        logger.info(f"   STDOUT length: {len(process.stdout or b'')} bytes")
        logger.info(f"   STDERR length: {len(process.stderr or b'')} bytes")

        # Robust decode: try utf-8 then fallback to cp1252
        def _safe_decode(b: bytes) -> str:
            try:
                return b.decode('utf-8')
            except UnicodeDecodeError:
                return b.decode('cp1252', errors='replace')

        scraper_stdout = _safe_decode(process.stdout or b"").strip()
        scraper_stderr = _safe_decode(process.stderr or b"").strip()

        # 🔍 DEBUG: Log raw output before processing
        logger.info(f"🔍 RAW SUBPROCESS OUTPUT:")
        logger.info(f"   STDOUT (first 500 chars): {scraper_stdout[:500]}")
        logger.info(f"   STDERR (first 500 chars): {scraper_stderr[:500]}")

        # Free process buffers ASAP to release memory
        process.stdout = None
        process.stderr = None

        if scraper_stderr:
            # Always log stderr for diagnostic purposes
            logger.info(f"Scraper '{config_file_path.name}' STDERR output:\n{scraper_stderr}")

        if process.returncode != 0:
            logger.error(
                f"Scraper subprocess for '{config_file_path.name}' exited with non-zero status code {process.returncode}. "
                f"Check STDERR above for details. Expecting empty list from stdout."
            )
            # We still proceed to parse stdout, as the script should output '[]' on error.

        if not scraper_stdout:
            logger.warning(f"Scraper '{config_file_path.name}' produced no STDOUT. Returning empty list.")
            logger.warning(f"🔍 DEBUG: Empty STDOUT - this means the scraper script didn't output JSON")
            return []

        try:
            # Extract JSON from mixed output (handles log lines before JSON)
            json_content = scraper_stdout.strip()

            # Look for JSON array start - find the first '[' character
            json_start = json_content.find('[')
            if json_start != -1:
                # Extract everything from the first '[' to the end
                json_content = json_content[json_start:]
                logger.debug(f"Extracted JSON content starting from position {json_start}")

            # The scraper should always return a list.
            scraped_data = json.loads(json_content)
            if not isinstance(scraped_data, list):
                logger.error(f"Scraper '{config_file_path.name}' output was not a JSON list. Got {type(scraped_data)}. Raw output: {scraper_stdout}")
                return []

            # 🔍 DEBUG: Log successful parsing
            logger.info(f"🔍 SUCCESS: Parsed {len(scraped_data)} catalog(s) from subprocess output")
            return scraped_data

        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON from scraper '{config_file_path.name}'. JSON Error: {e}")
            logger.error(f"🔍 DEBUG: Raw STDOUT that failed to parse: {repr(scraper_stdout)}")

            # Try to extract JSON more aggressively
            try:
                # Look for JSON array pattern more carefully
                lines = scraper_stdout.split('\n')
                json_lines = []
                in_json = False

                for line in lines:
                    line = line.strip()
                    if line.startswith('['):
                        in_json = True
                    if in_json:
                        json_lines.append(line)
                    if line.endswith(']') and in_json:
                        break

                if json_lines:
                    json_content = '\n'.join(json_lines)
                    scraped_data = json.loads(json_content)
                    logger.info(f"🔍 RECOVERY: Successfully parsed JSON after line-by-line extraction")
                    return scraped_data if isinstance(scraped_data, list) else []

            except Exception as recovery_error:
                logger.error(f"JSON recovery attempt also failed: {recovery_error}")

            return []

    except subprocess.TimeoutExpired:
        logger.error(f"Scraper '{config_file_path.name}' timed out after 5 minutes. Terminating...")
        logger.error(f"🔍 DEBUG: Timeout occurred - scraper took longer than 300 seconds")
        return []
    except FileNotFoundError as e:
        logger.critical(f"Could not find 'python' executable. Ensure it's in the system's PATH. Error: {e}")
        logger.critical(f"🔍 DEBUG: Python executable not found - check PATH environment variable")
        return []
    except Exception as e:
        logger.exception(f"An unexpected error occurred while running the scraper subprocess for '{config_file_path.name}'. Error: {e}")
        logger.error(f"🔍 DEBUG: Unexpected subprocess error - see traceback above")
        return []


def process_catalog_from_cloud_storage(config_filename: str, catalog_data: dict, cloud_pdf_path: str, db: Session) -> dict:
    """
    Process a catalog that's already in cloud storage (no local file).
    Used when scrapers upload directly to cloud and clean up local files.
    """
    store_name = catalog_data["store_name"]
    title = catalog_data["title"]
    raw_date_info = catalog_data.get("raw_date_info", "")

    logger.info(f"Processing catalog from cloud storage: {cloud_pdf_path}")

    # Parse dates from raw_date_info using comprehensive parsing
    date_result, used_string = parse_catalog_date_range(raw_date_info, store_name=store_name)
    if date_result is None:
        logger.error(f"Failed to parse dates from '{raw_date_info}' for catalog '{title}' from {store_name}")
        return {
            "scraper_config": config_filename,
            "catalog_title": title,
            "raw_date_info": raw_date_info,
            "status": "Failed (Date Parse)"
        }

    start_date, end_date = date_result
    if not start_date or not end_date:
        logger.error(f"Parsed dates are invalid from '{raw_date_info}' for catalog '{title}' from {store_name}")
        return {
            "scraper_config": config_filename,
            "catalog_title": title,
            "raw_date_info": raw_date_info,
            "status": "Failed (Date Parse)"
        }

    # Check if catalog already exists in database (using date-only comparison)
    existing_catalog = db.query(Catalog).join(Store).filter(
        Store.name == store_name,
        func.date(Catalog.valid_from) == start_date,
        func.date(Catalog.valid_to) == end_date
    ).first()

    if existing_catalog:
        logger.info(f"Catalog already exists in database: {title} ({start_date} - {end_date})")
        return {
            "scraper_config": config_filename,
            "catalog_title": title,
            "valid_from": start_date.isoformat(),
            "valid_to": end_date.isoformat(),
            "status": "Already Exists"
        }

    # Determine if catalog is currently active
    today = date.today()
    is_active_today = start_date <= today <= end_date

    # Create catalog entry in database
    try:
        # Convert dates to datetime objects for pdf_extractor
        valid_from_dt = datetime.combine(start_date, datetime.min.time())
        valid_to_dt = datetime.combine(end_date, datetime.min.time())

        # 🚨 CRITICAL: Process the PDF from cloud storage to create CatalogPage entries
        # This MUST succeed or we'll have orphaned catalogs
        try:
            processed_catalog, image_paths = process_pdf_catalog_from_cloud(
                cloud_pdf_path=cloud_pdf_path,
                store_name=store_name,
                title=title,
                valid_from=valid_from_dt,
                valid_to=valid_to_dt,
                attempt_date_extraction=False,  # We already have the dates
                db=db
            )
        except Exception as pdf_error:
            logger.error(f"❌ CRITICAL PDF PROCESSING FAILURE for {store_name}: {pdf_error}")
            return {
                "scraper_config": config_filename,
                "catalog_title": title,
                "status": f"Failed (PDF Processing): {str(pdf_error)[:200]}"
            }

        if processed_catalog:
            # Add to processing queue for AI analysis
            queue_entry = CatalogProcessingQueue(
                catalog_db_id=processed_catalog.id,
                pdf_path_to_process=cloud_pdf_path,
                pdf_content_hash="",  # Will be filled by processing logic
                status='PENDING',
                retry_count=0
            )
            db.add(queue_entry)
            db.commit()

            logger.info(f"✅ Successfully processed catalog from cloud: {title}")
            return {
                "scraper_config": config_filename,
                "catalog_title": title,
                "valid_from": start_date.isoformat(),
                "valid_to": end_date.isoformat(),
                "status": "Queued"
            }
        else:
            logger.error(f"Failed to process PDF from cloud storage: {cloud_pdf_path}")
            return {
                "scraper_config": config_filename,
                "catalog_title": title,
                "status": "Failed (PDF Processing)"
            }

    except Exception as e:
        logger.error(f"Error processing catalog from cloud storage: {e}", exc_info=True)
        db.rollback()
        return {
            "scraper_config": config_filename,
            "catalog_title": title,
            "status": "Failed (Exception)",
            "details": str(e)
        }


def process_catalog_entry(db, catalog_data, config_filename, today):
    """Processes a single catalog entry."""
    required_keys = ["store_name", "title", "raw_date_info", "local_path", "pdf_url"]
    store_name = catalog_data['store_name']  # Fix: define store_name early for all uses
    if not all(key in catalog_data for key in required_keys):
        logger.error(f"A catalog entry from '{config_filename}' is missing required keys. Data: {catalog_data}")
        return {
            "scraper_config": config_filename,
            "catalog_title": catalog_data.get('title', 'N/A'),
            "status": "Failed (Missing Keys)",
            "details": str(catalog_data)
        }

    logger.info(f"Processing catalog entry: '{catalog_data['title']}' for store '{catalog_data['store_name']}'.")

    local_pdf_path_value = catalog_data.get("local_path")
    if not local_pdf_path_value:
        logger.warning(f"No local_path provided or it's empty for catalog '{catalog_data['title']}' from {catalog_data['store_name']}. Skipping.")
        return {
            "scraper_config": config_filename,
            "catalog_title": catalog_data['title'],
            "raw_date_info": catalog_data.get('raw_date_info', 'N/A'),
            "status": "Skipped - No PDF path from scraper"
        }

    raw_date_str = catalog_data.get('raw_date_info')
    legacy_date_range_str = catalog_data.get('date_range')

    parsed_dates = None
    date_strings_to_try = []
    
    # Normalize both date strings before adding to the list
    if raw_date_str and raw_date_str.strip():
        normalized_raw_date = normalize_danish_string_for_date_parsing(raw_date_str)
        date_strings_to_try.append(normalized_raw_date)
        logger.debug(f"Normalized raw_date_str: '{raw_date_str}' -> '{normalized_raw_date}'")
        
    if legacy_date_range_str and legacy_date_range_str.strip() and legacy_date_range_str != raw_date_str:
        normalized_legacy_date = normalize_danish_string_for_date_parsing(legacy_date_range_str)
        date_strings_to_try.append(normalized_legacy_date)
        logger.debug(f"Normalized legacy_date_range_str: '{legacy_date_range_str}' -> '{normalized_legacy_date}'")

    successfully_parsed_date_string = None
    parsed_dates = None
    for date_str_to_parse_attempt in date_strings_to_try:
        logger.debug(f"Attempting to parse date string: '{date_str_to_parse_attempt}' for catalog from {store_name}")
        
        if store_name.lower() in ["meny", "spar", "min købmand"]:
            logger.debug(f"Attempting Dagrofa-specific week title parsing for '{date_str_to_parse_attempt}'")
            parsed_dates = parse_dagrofa_retail_week_title(date_str_to_parse_attempt)
        
        if not parsed_dates:
            logger.debug(f"Attempting verbose Danish date range parsing for '{date_str_to_parse_attempt}'")
            parsed_dates = parse_verbose_danish_date_range(date_str_to_parse_attempt)
        
        if not parsed_dates:
            logger.debug(f"Attempting standard Danish date range parsing for '{date_str_to_parse_attempt}'")
            parsed_dates = parse_danish_date_range(date_str_to_parse_attempt)
                    
        if parsed_dates:
            successfully_parsed_date_string = date_str_to_parse_attempt
            break
    
    return parsed_dates, successfully_parsed_date_string
    TARGET_SCRAPER_CONFIG_FILENAME = "bilka.json"  # Change this to test different scrapers
    DEBUG_MODE = False # Set to False to process all scrapers
    
    # Enable debug logging for date parsing
    logger.setLevel(logging.DEBUG)
    today = date.today()
    processing_summary = []
    db = None
    try:
        db = SessionLocal()
        cleanup_expired_catalogs(db, today)
        logger.info("Marking all existing catalogs with is_latest_for_today = False.")
        db.query(Catalog).update({"is_latest_for_today": False})
        db.commit()
        logger.info("Initial marking complete.")

        scraper_configs = [f for f in SCRAPER_CONFIG_DIR.iterdir() if f.is_file() and f.name.endswith('.json')]
        if not scraper_configs:
            logger.warning(f"No scraper JSON configuration files found in {SCRAPER_CONFIG_DIR}. Exiting.")
            return
        logger.info(f"Found {len(scraper_configs)} scraper configurations: {[f.name for f in scraper_configs]}")

        for config_file_path in scraper_configs:
            config_filename = config_file_path.name
            logger.info(f"--- Processing scraper config: {config_filename} ---")
            scraped_catalogs_data, scraper_error = run_scraper(config_file_path)
            if scraper_error:
                processing_summary.append({
                    "scraper_config": config_filename,
                    "status": "Scraper Execution Failed",
                    "details": scraper_error[:500]
                })
                continue
            if not scraped_catalogs_data:
                logger.info(f"No catalog entries returned from {config_filename}.")
                processing_summary.append({
                    "scraper_config": config_filename,
                    "status": "No Catalogs Returned"
                })
                continue
            logger.info(f"Scraper '{config_filename}' returned {len(scraped_catalogs_data)} catalog entries.")
            for catalog_data in scraped_catalogs_data:
                summary = process_catalog_entry(db, catalog_data, config_filename, today)
                processing_summary.append(summary)
            logger.info(f"Committing database changes for scraper {config_filename}.")
            db.commit()
        print_processing_summary(processing_summary)
    finally:
        if db:
            db.close()
            logger.debug("Database session closed.")
    logger.info("--- Catalog processing workflow (v2) finished. ---")



# ---------------- Corrected implementation ----------------

def process_catalog_entry(db: Session, catalog_data: dict, config_filename: str, today: date):
    """Processes a single catalog entry end-to-end and returns a human-friendly summary dict."""
    required_keys = ["store_name", "title", "raw_date_info", "local_path", "pdf_url"]
    if not all(key in catalog_data for key in required_keys):
        logger.error(
            "A catalog entry from '%s' is missing required keys: %s", config_filename, catalog_data
        )
        return {
            "scraper_config": config_filename,
            "catalog_title": catalog_data.get("title", "N/A"),
            "status": "Failed (Missing Keys)",
            "details": str(catalog_data),
        }

    store_name: str = catalog_data["store_name"]
    title: str = catalog_data["title"]

    # 🌥️ CLOUD-FIRST: Check for cloud path first
    cloud_pdf_path = catalog_data.get("cloud_pdf_path")
    if cloud_pdf_path:
        logger.info("Cloud PDF path available: %s. Processing from cloud storage.", cloud_pdf_path)
        # Process directly from cloud storage
        return process_catalog_from_cloud_storage(
            config_filename=config_filename,
            catalog_data=catalog_data,
            cloud_pdf_path=cloud_pdf_path,
            db=db
        )

    # 📁 FALLBACK: Handle legacy local files (upload to cloud first)
    local_pdf_path: str = catalog_data.get("local_path")
    if not local_pdf_path:
        logger.warning("No cloud_pdf_path or local_path for catalog '%s' from %s – skipping.", title, store_name)
        return {
            "scraper_config": config_filename,
            "catalog_title": title,
            "status": "Skipped – No PDF path",
        }

    # Check if local PDF file actually exists
    local_pdf_file = Path(local_pdf_path)
    if not local_pdf_file.exists():
        logger.error("Local PDF file does not exist: %s for catalog '%s' from %s", local_pdf_path, title, store_name)
        return {
            "scraper_config": config_filename,
            "catalog_title": title,
            "status": "Failed (Local PDF Not Found)",
            "details": f"File not found: {local_pdf_path}",
        }

    # 2️⃣  Calculate SHA256 hash from the valid local file
    pdf_hash = calculate_sha256(local_pdf_file)
    if not pdf_hash:
        logger.error("Failed to calculate hash for local PDF: %s for catalog '%s'", local_pdf_path, title)
        return {
            "scraper_config": config_filename,
            "catalog_title": title,
            "status": "Failed (Hash Calculation)",
        }
    logger.info("Calculated PDF hash %s for catalog '%s'", pdf_hash[:12] + "...", title)

    # 3️⃣  Parse catalog validity period
    raw_date_str = catalog_data.get("raw_date_info")
    legacy_date_range = catalog_data.get("date_range")
    parsed, used_date_string = parse_catalog_date_range(raw_date_str, legacy_date_range, store_name)
    if not parsed:
        logger.error("Could not parse date range for '%s' (store: %s) – raw='%s' legacy='%s'", title, store_name, raw_date_str, legacy_date_range)
        return {
            "scraper_config": config_filename,
            "catalog_title": title,
            "raw_date_info": raw_date_str,
            "status": "Failed (Date Parse)",
        }
    start_date, end_date = parsed

    # 4️⃣  Upload PDF to cloud storage with canonical name
    try:
        cloud_pdf_path = upload_pdf_to_cloud_storage(local_pdf_path, store_name, start_date, end_date)
    except Exception as e:
        logger.error("Failed to upload PDF for '%s': %s", title, e, exc_info=True)
        return {
            "scraper_config": config_filename,
            "catalog_title": title,
            "status": "Failed (Move PDF)",
        }

    if not cloud_pdf_path:
        logger.error("PDF upload returned None – upload failed for '%s'. Skipping DB/queue update.", title)
        return {
            "scraper_config": config_filename,
            "catalog_title": title,
            "status": "Failed (Upload PDF)",
        }

    # 5️⃣  Determine if catalog is currently active
    is_active_today: bool = start_date <= today <= end_date

    # 6️⃣  Write/update DB rows & enqueue for parsing
    catalog_entry = update_database(
        db_session=db,
        scraper_data=catalog_data,
        parsed_start_date=start_date,
        parsed_end_date=end_date,
        cloud_pdf_path=cloud_pdf_path,  # Using cloud path
        is_active_today=is_active_today,
        pdf_hash=pdf_hash,  # Pass the pre-calculated hash
    )

    summary_status = "Queued" if catalog_entry else "Skipped/Error"
    return {
        "scraper_config": config_filename,
        "catalog_title": title,
        "valid_from": start_date.isoformat(),
        "valid_to": end_date.isoformat(),
        "status": summary_status,
    }

# -----------------------------------------------------------

if __name__ == "__main__":
    # This allows running catalog_processor.py directly.
    # Ensure project root is in path for config/database imports.
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
        logger.debug(f"Added {project_root} to sys.path for __main__ execution.")
    main()

# --- Legacy compatibility aliases for old date parsing functions ---
# These aliases are for backward compatibility only. They are wrapped in try/except