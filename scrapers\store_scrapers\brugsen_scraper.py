# File: scrapers/store_scrapers/brugsen_scraper.py

import asyncio
import re
from pathlib import Path
from typing import List, Dict, Any, Optional


from ..scraper_core import Base<PERSON><PERSON>raper, PlaywrightTimeoutError, PlaywrightError, Page


class BrugsenScraper(BaseScraper):
    """
    Scraper for Brugsen catalogs (Dagli'Brugsen/LokalBrugsen).
    - Navigates to the main ShopGun-style viewer page (/avis/).
    - Extracts date from this viewer page.
    - Clicks a trigger (often an SVG icon) to navigate to/reveal PDF download options.
    - Clicks the final download button/image.
    """

    def __init__(self, config: dict):
        super().__init__(config)
        self.logger.info(f"B<PERSON>senScraper initialized for store: {self.store_name}")

    async def scrape_catalogs(self) -> List[Dict[str, Any]]:
        if not self.page or not self.context or not self.catalog_list_url:
            self.logger.error("Page, context, or catalog_list_url not available for <PERSON>rugsenScraper.")
            return []

        # Set timeouts
        try:
            default_nav_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            default_el_timeout = self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 30000)
            if self.page:
                self.page.set_default_navigation_timeout(default_nav_timeout)
                self.page.set_default_timeout(default_el_timeout)
                self.logger.info(f"{self.store_name}: Page timeouts set.")
        except Exception as e_timeout_set:
            self.logger.error(f"BrugsenScraper: Error setting default timeouts: {e_timeout_set}")
            return []

        # 1. Navigate to Brugsen /avis/ page (ShopGun viewer)
        self.logger.debug(f"Navigating to Brugsen /avis/ page: {self.catalog_list_url}.")
        nav_success = await self._navigate_to_url(self.page, self.catalog_list_url)
        if not nav_success:
            return []

        
        # 2. Handle Cookies
        cookie_selectors = self.config.get('selectors', {}).get('cookie_accept_selectors', [])
        if cookie_selectors:
            await self._handle_cookies(self.page, cookie_selectors)


        # 3. Extract Date from this /avis/ page
        raw_date_info = "Brugsen_Unknown_Date"
        display_title = f"{self.store_name} Catalog" # Default title

        date_on_viewer_selector = self.config.get('selectors', {}).get('date_element_on_viewer_page')
        if date_on_viewer_selector:
            try:
                date_el = self.page.locator(date_on_viewer_selector).first
                await date_el.wait_for(state="visible", timeout=10000)
                text_content = await date_el.text_content()
                if text_content: raw_date_info = text_content.strip()
                self.logger.info(f"Brugsen: Date from viewer page: '{raw_date_info}'")
                # Update display_title if date is found
                if "Unknown_Date" not in raw_date_info:
                    display_title = f"{self.store_name} Catalog ({raw_date_info.split('-')[0].strip()})"
            except Exception as e: 
                self.logger.warning(f"Brugsen: Error extracting date from viewer page: {e}")
                await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "brugsen_date_extract_fail.png"))

        # 4. Click the ShopGun PDF trigger (e.g., SVG icon) on the /avis/ page
        # This click should lead to the page/state where the final download button is.
        shopgun_pdf_trigger_selector = self.config.get('selectors', {}).get('shopgun_viewer_pdf_trigger_selector')
        final_download_button_selector = self.config.get('selectors', {}).get('final_pdf_download_button_selector')

        if not shopgun_pdf_trigger_selector:
            self.logger.warning("Brugsen: 'shopgun_viewer_pdf_trigger_selector' not configured. Will try to find final download button directly.")
        else:
            try:
                self.logger.info(f"Brugsen Viewer: Clicking PDF trigger element: '{shopgun_pdf_trigger_selector}'")
                pdf_trigger_el = self.page.locator(shopgun_pdf_trigger_selector).first
                await pdf_trigger_el.wait_for(state="visible", timeout=15000)
                
                await pdf_trigger_el.click(timeout=5000)
                # This navigation should update self.page to the /avis-pdf/ like page
                await self.page.wait_for_load_state('domcontentloaded', timeout=self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000))
                
                # Additional wait time after ShopGun PDF trigger click
                wait_after_trigger = self.config.get('behavior_flags', {}).get('wait_after_viewer_pdf_trigger_click_ms', 2000)
                self.logger.info(f"Brugsen: Waiting additional {wait_after_trigger}ms after ShopGun trigger click")
                await asyncio.sleep(wait_after_trigger/1000)  # Convert ms to seconds
                
                self.logger.info(f"Brugsen: Clicked ShopGun PDF trigger. New URL for download: {self.page.url}")

            except Exception as e:
                self.logger.error(f"Brugsen Viewer: Error clicking ShopGun PDF trigger '{shopgun_pdf_trigger_selector}': {e}", exc_info=True)
                await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "brugsen_shopgun_trigger_fail.png"))
                # Continue, as the final download button might be on the current page if trigger failed or wasn't needed
        
        # 5. Locate ShopGun Iframe and click the final download button
        if not final_download_button_selector:
            self.logger.error("Brugsen: 'final_pdf_download_button_selector' not configured.")
            return []

        download_url: Optional[str] = None
        try:
            # Check if coordinate-based clicking is enabled
            use_coordinate_click = self.config.get('selectors', {}).get('use_coordinate_click', False)
            coordinate_position = self.config.get('selectors', {}).get('coordinate_click_position', [640, 360])

            navigation_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            click_timeout = self.config.get('behavior_flags', {}).get('element_click_timeout_ms', 10000)

            try:
                if use_coordinate_click:
                    self.logger.info(f"Brugsen: Using coordinate-based click at position {coordinate_position}")
                    await self.page.click(coordinate_position[0], coordinate_position[1])
                    # Wait for navigation to PDF URL after coordinate click
                    await self.page.wait_for_load_state('load', timeout=navigation_timeout)
                    download_url = self.page.url
                    self.logger.info(f"Brugsen: Navigated to PDF URL via coordinate click: {download_url}")
                else:
                    # The PDF viewer is not in an iframe. Locate the download button directly on the page.
                    self.logger.info(f"Brugsen: Locating final PDF download button directly on page using selector: '{final_download_button_selector}'")
                    download_button = self.page.locator(final_download_button_selector).first
                    await download_button.wait_for(state="visible", timeout=20000)

                    # Try to use direct href if available to avoid flaky popups
                    direct_href = await download_button.get_attribute("href")
                    if direct_href and direct_href.endswith(".pdf"):
                        download_url = direct_href
                        self.logger.info(f"Brugsen: Using direct href for PDF: {download_url}")
                    else:
                        self.logger.info("Brugsen: Clicking download button and waiting for download...")
                        # Wait for download to start
                        async with self.page.expect_download(timeout=navigation_timeout) as download_info:
                            await download_button.click(timeout=click_timeout)

                        download = await download_info.value
                        download_url = download.url
                        self.logger.info(f"Brugsen: Successfully captured PDF download URL: {download_url}")

                        # Cancel the download since we only need the URL
                        await download.cancel()

            except PlaywrightTimeoutError:
                # Fallback: try popup approach for older sites
                try:
                    async with self.page.context.expect_page(timeout=5000) as popup_info:
                        await download_button.click(timeout=click_timeout)
                    popup_page = await popup_info.value
                    await popup_page.wait_for_load_state('load', timeout=navigation_timeout)
                    download_url = popup_page.url
                except PlaywrightTimeoutError:
                    raise PlaywrightTimeoutError("Failed to capture PDF URL via navigation or popup")

            # For COOP sites, PDF URLs are AWS S3 URLs that don't end with .pdf
            # Validate that we have a valid HTTP URL
            if not download_url or not download_url.startswith('http'):
                raise ValueError(f"Invalid or non-HTTP PDF URL obtained: {download_url}")

            # Check if it's an AWS S3 URL (common for COOP sites) or ends with .pdf
            is_s3_url = 's3.eu-west-1.amazonaws.com' in download_url or 'sgn-prd-assets' in download_url
            is_pdf_url = download_url.lower().endswith('.pdf')

            if not (is_s3_url or is_pdf_url):
                self.logger.warning(f"Brugsen: Captured URL does not appear to be a PDF or S3 URL: {download_url}")
                await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "brugsen_non_pdf_download.png"))

            self.logger.info(f"Brugsen: Captured PDF URL: {download_url}")

        except PlaywrightTimeoutError as pe:
            self.logger.error(f"Brugsen: Timeout error during PDF navigation or iframe interaction: {pe}", exc_info=True)
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "brugsen_pdf_nav_timeout_fail.png"))
            return []
        except Exception as e:
            self.logger.error(f"Brugsen: Failed to navigate to PDF or capture its URL. Error: {e}", exc_info=True)
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "brugsen_pdf_navigation_fail.png"))
            return []

        # 6. Save PDF from URL
        if not download_url:
            self.logger.error("Brugsen: download_url was not captured, cannot save PDF.")
            return []

        file_title_part = self._sanitize_filename(raw_date_info.split('-')[0].strip() if "Unknown_Date" not in raw_date_info else display_title)
        suggested_filename = f"{file_title_part}.pdf"
        
        final_pdf_filename = self._sanitize_filename(suggested_filename)
        if not final_pdf_filename.lower().endswith(".pdf"): final_pdf_filename += ".pdf"
            
        download_dir = self._ensure_download_dir()
        local_pdf_path = download_dir / final_pdf_filename

        try:
            pdf_content = await self._download_file_content(download_url)
            if not pdf_content:
                self.logger.error(f"Brugsen: Failed to download PDF content from {download_url}.")
                return []

            with open(local_pdf_path, 'wb') as f:
                f.write(pdf_content)
            self.logger.info(f"Brugsen: Successfully saved PDF to: {local_pdf_path}")
            
            self.collected_catalogs.append({
                "store_name": self.store_name, "title": display_title, 
                "raw_date_info": raw_date_info, "pdf_url": download_url, 
                "local_path": str(local_pdf_path)
            })
        except Exception as e:
            self.logger.error(f"Brugsen: Error saving PDF from URL {download_url}: {e}", exc_info=True)

        return self.collected_catalogs