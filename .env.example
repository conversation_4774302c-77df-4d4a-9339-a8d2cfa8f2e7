# .env.example - Copy this file to .env and fill in your values

# --- Essential Environment Variables ---
# These MUST be set, especially for deployment (e.g., in Render dashboard)
GOOGLE_API_KEY=YOUR_GEMINI_API_KEY_HERE
DATABASE_URL=postgresql://user:password@host:port/dbname # Replace with your PostgreSQL connection string
SECRET_KEY=YOUR_STRONG_RANDOM_SECRET_KEY_HERE # Used for signing JWT tokens (e.g., generate with openssl rand -hex 32)

# --- Configuration Variables (Managed via UI/Database) ---
# These settings are PRIMARILY managed via the /settings page in the UI,
# which saves them to the database for persistence.
# Setting them here acts as an initial default if the DB value is missing,
# but changes made via the UI will override these .env values.
# Uncomment and set here ONLY for initial setup or if bypassing the DB.

# Authentication Algorithm (Default: HS256)
# ALGORITHM=HS256

# Gemini Model Selection (Defaults: flash / 1.5-pro)
# GEMINI_PARSING_MODEL=gemini-1.5-flash
# GEMINI_QUERY_MODEL=gemini-1.5-pro

# Gemini System Prompts (Defaults defined in code)
# GEMINI_QUERY_SYSTEM_PROMPT="You are a helpful shopping assistant..."
# GEMINI_PARSER_SYSTEM_PROMPT="You are a professional product data extractor..."

# Gemini Rate Limiting (RPM - Defaults: 10 / 30 / 1)
# GEMINI_INITIAL_RATE=10
# GEMINI_MAX_RATE=30
# GEMINI_MIN_RATE=1

# --- Other Optional Variables ---
# LOG_LEVEL=INFO

# --- Google Cloud Storage Configuration ---
GCS_BUCKET_NAME=tilbudsjaegeren
GOOGLE_CLOUD_CREDENTIALS_JSON={"type":"service_account","project_id":"your-project-id",...}

# --- Authentication Token Expiry --- 
# (This is NOT managed via the UI settings)
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Custom Query System Prompt (Optional, overrides default)
# Wrap in quotes if it contains special characters or multiple lines
QUERY_SYSTEM_PROMPT_OVERRIDE= 