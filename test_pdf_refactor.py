#!/usr/bin/env python3
"""
Test script for the refactored PDF processing system.
This script tests the new unified PDF processing function to ensure it works correctly.
"""

import os
import sys
import logging
import tempfile
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)-20s - %(levelname)-8s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger('test_pdf_refactor')

def test_imports():
    """Test that all required modules can be imported"""
    logger.info("🧪 Testing imports...")
    
    try:
        from database import SessionLocal, Store, Catalog, CatalogPage
        from pdf_extractor import _process_pdf_unified, process_pdf_catalog_from_cloud, process_pdf_catalog
        from cloud_storage import cloud_storage
        from storage_factory import StoragePaths
        logger.info("✅ All imports successful")
        return True
    except ImportError as e:
        logger.error(f"❌ Import failed: {e}")
        return False

def test_cloud_storage_connection():
    """Test cloud storage connectivity"""
    logger.info("🧪 Testing cloud storage connection...")
    
    try:
        from cloud_storage import cloud_storage
        if cloud_storage.is_available():
            logger.info("✅ Cloud storage is available")
            return True
        else:
            logger.warning("⚠️ Cloud storage is not available")
            return False
    except Exception as e:
        logger.error(f"❌ Cloud storage test failed: {e}")
        return False

def test_database_connection():
    """Test database connectivity"""
    logger.info("🧪 Testing database connection...")
    
    try:
        from database import SessionLocal, Store
        db = SessionLocal()
        
        # Try a simple query
        store_count = db.query(Store).count()
        logger.info(f"✅ Database connected - found {store_count} stores")
        
        db.close()
        return True
    except Exception as e:
        logger.error(f"❌ Database test failed: {e}")
        return False

def test_function_signatures():
    """Test that function signatures are preserved"""
    logger.info("🧪 Testing function signatures...")
    
    try:
        from pdf_extractor import process_pdf_catalog_from_cloud, process_pdf_catalog
        import inspect
        
        # Test process_pdf_catalog_from_cloud signature
        sig = inspect.signature(process_pdf_catalog_from_cloud)
        expected_params = [
            'cloud_pdf_path', 'store_name', 'title', 'valid_from', 'valid_to',
            'attempt_date_extraction', 'parsing_model', 'dpi', 'db'
        ]
        
        actual_params = list(sig.parameters.keys())
        if actual_params == expected_params:
            logger.info("✅ process_pdf_catalog_from_cloud signature preserved")
        else:
            logger.error(f"❌ Signature mismatch: expected {expected_params}, got {actual_params}")
            return False
        
        # Test process_pdf_catalog signature
        sig = inspect.signature(process_pdf_catalog)
        expected_params = [
            'temp_pdf_path', 'store_name', 'original_filename', 'title', 'valid_from', 'valid_to',
            'attempt_date_extraction', 'parsing_model', 'dpi', 'db'
        ]
        
        actual_params = list(sig.parameters.keys())
        if actual_params == expected_params:
            logger.info("✅ process_pdf_catalog signature preserved")
        else:
            logger.error(f"❌ Signature mismatch: expected {expected_params}, got {actual_params}")
            return False
        
        return True
    except Exception as e:
        logger.error(f"❌ Function signature test failed: {e}")
        return False

def test_helper_functions():
    """Test that helper functions are available"""
    logger.info("🧪 Testing helper functions...")
    
    try:
        from pdf_extractor import choose_dpi, extract_dates_from_image
        from storage_factory import StoragePaths
        
        # Test choose_dpi
        dpi = choose_dpi(10 * 1024 * 1024)  # 10MB file
        if isinstance(dpi, int) and dpi > 0:
            logger.info(f"✅ choose_dpi works: 10MB → {dpi} DPI")
        else:
            logger.error(f"❌ choose_dpi returned invalid value: {dpi}")
            return False
        
        # Test StoragePaths
        catalog_path = StoragePaths.catalog_path("test.pdf")
        image_path = StoragePaths.image_path("test.png")
        
        if catalog_path.startswith("catalogs/") and image_path.startswith("images/"):
            logger.info("✅ StoragePaths abstraction works")
        else:
            logger.error(f"❌ StoragePaths failed: catalog={catalog_path}, image={image_path}")
            return False
        
        return True
    except Exception as e:
        logger.error(f"❌ Helper function test failed: {e}")
        return False

def create_test_pdf():
    """Create a simple test PDF for testing"""
    logger.info("🧪 Creating test PDF...")
    
    try:
        import fitz  # PyMuPDF
        
        # Create a simple PDF with 2 pages
        doc = fitz.open()
        
        # Page 1
        page1 = doc.new_page()
        page1.insert_text((50, 50), "Test Catalog Page 1\nGælder fra: 2024-01-01\nTil: 2024-01-07", fontsize=12)
        
        # Page 2
        page2 = doc.new_page()
        page2.insert_text((50, 50), "Test Catalog Page 2\nSpecial Offers", fontsize=12)
        
        # Save to temp file
        temp_pdf = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
        doc.save(temp_pdf.name)
        doc.close()
        
        logger.info(f"✅ Created test PDF: {temp_pdf.name}")
        return temp_pdf.name
    except Exception as e:
        logger.error(f"❌ Failed to create test PDF: {e}")
        return None

def test_dry_run():
    """Test the unified function with a dry run (no actual processing)"""
    logger.info("🧪 Testing unified function (dry run)...")
    
    try:
        from pdf_extractor import _process_pdf_unified
        from database import SessionLocal
        
        # Test with non-existent file to check error handling
        db = SessionLocal()
        try:
            result = _process_pdf_unified(
                pdf_source="non_existent_file.pdf",
                store_name="Test Store",
                title="Test Catalog",
                attempt_date_extraction=False,  # Skip AI to avoid API calls
                db=db
            )
            
            # Should return None, [] for non-existent file
            if result == (None, []):
                logger.info("✅ Error handling works correctly")
                return True
            else:
                logger.error(f"❌ Unexpected result for non-existent file: {result}")
                return False
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ Dry run test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results"""
    logger.info("🚀 Starting PDF refactor tests...")
    logger.info("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Cloud Storage Test", test_cloud_storage_connection),
        ("Database Test", test_database_connection),
        ("Function Signatures", test_function_signatures),
        ("Helper Functions", test_helper_functions),
        ("Dry Run Test", test_dry_run),
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! Refactor looks good to deploy.")
        return True
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. Review issues before deploying.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
