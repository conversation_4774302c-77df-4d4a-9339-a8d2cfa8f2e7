#!/usr/bin/env python3
"""
Diagnostic script to debug Render environment issues with scrapers
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
from dotenv import load_dotenv

# Load environment
load_dotenv()

def check_system_info():
    """Check basic system information"""
    print("🖥️  SYSTEM INFORMATION")
    print("=" * 50)
    print(f"Platform: {platform.platform()}")
    print(f"Python version: {sys.version}")
    print(f"Architecture: {platform.architecture()}")
    print(f"CPU count: {os.cpu_count()}")
    
    # Memory info (basic check without psutil)
    try:
        # Try to get memory info from /proc/meminfo on Linux
        if os.path.exists('/proc/meminfo'):
            with open('/proc/meminfo', 'r') as f:
                meminfo = f.read()
                for line in meminfo.split('\n'):
                    if 'MemTotal:' in line:
                        total_kb = int(line.split()[1])
                        print(f"Total memory: {total_kb / (1024**2):.2f} GB")
                    elif 'MemAvailable:' in line:
                        avail_kb = int(line.split()[1])
                        print(f"Available memory: {avail_kb / (1024**2):.2f} GB")
        else:
            print("Memory info: Not available (no /proc/meminfo)")
    except Exception as e:
        print(f"Memory info error: {e}")
    
    print()

def check_environment_variables():
    """Check critical environment variables"""
    print("🔧 ENVIRONMENT VARIABLES")
    print("=" * 50)
    
    critical_vars = [
        'GOOGLE_CLOUD_CREDENTIALS_JSON',
        'GCS_BUCKET_NAME', 
        'DATABASE_URL',
        'RENDER',
        'RENDER_SERVICE_NAME',
        'PYTHONPATH'
    ]
    
    for var in critical_vars:
        value = os.getenv(var)
        if value:
            if 'CREDENTIALS' in var:
                print(f"✅ {var}: Set ({len(value)} characters)")
            elif 'DATABASE_URL' in var:
                print(f"✅ {var}: Set (postgresql://...)")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: Not set")
    
    print()

def check_playwright_installation():
    """Check if Playwright and browsers are installed"""
    print("🎭 PLAYWRIGHT INSTALLATION")
    print("=" * 50)
    
    try:
        import playwright
        print(f"✅ Playwright installed")

        # Check if browsers are installed
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            try:
                browser = p.chromium.launch(headless=True)
                print("✅ Chromium browser: Available")
                browser.close()
            except Exception as e:
                print(f"❌ Chromium browser: Failed - {e}")
                
    except ImportError as e:
        print(f"❌ Playwright not installed: {e}")
    
    print()

def check_subprocess_execution():
    """Test subprocess execution like catalog processor does"""
    print("🔄 SUBPROCESS EXECUTION TEST")
    print("=" * 50)
    
    # Test basic Python subprocess
    try:
        result = subprocess.run(
            ["python", "-c", "print('Hello from subprocess')"],
            capture_output=True,
            text=True,
            timeout=10
        )
        print(f"✅ Basic subprocess: {result.stdout.strip()}")
        print(f"   Return code: {result.returncode}")
    except Exception as e:
        print(f"❌ Basic subprocess failed: {e}")
    
    # Test scraper module import
    try:
        result = subprocess.run(
            ["python", "-c", "import scrapers.run_scraper; print('Scraper module imported')"],
            capture_output=True,
            text=True,
            timeout=10
        )
        print(f"✅ Scraper import: {result.stdout.strip()}")
        if result.stderr:
            print(f"   Stderr: {result.stderr.strip()}")
    except Exception as e:
        print(f"❌ Scraper import failed: {e}")
    
    print()

def check_file_permissions():
    """Check file system permissions"""
    print("📁 FILE SYSTEM PERMISSIONS")
    print("=" * 50)
    
    # Check current directory
    cwd = Path.cwd()
    print(f"Current directory: {cwd}")
    print(f"Directory writable: {os.access(cwd, os.W_OK)}")
    
    # Check scrapers directory
    scrapers_dir = cwd / "scrapers"
    if scrapers_dir.exists():
        print(f"✅ Scrapers directory exists: {scrapers_dir}")
        print(f"   Readable: {os.access(scrapers_dir, os.R_OK)}")
        print(f"   Executable: {os.access(scrapers_dir, os.X_OK)}")
    else:
        print(f"❌ Scrapers directory missing: {scrapers_dir}")
    
    # Check temp directory
    temp_dir = Path("/tmp")
    if temp_dir.exists():
        print(f"✅ Temp directory: {temp_dir}")
        print(f"   Writable: {os.access(temp_dir, os.W_OK)}")
    
    print()

def test_minimal_scraper():
    """Test a minimal scraper execution"""
    print("🧪 MINIMAL SCRAPER TEST")
    print("=" * 50)
    
    try:
        # Create a minimal test script
        test_script = """
import sys
import os
from dotenv import load_dotenv
load_dotenv()

print("Test script started", file=sys.stderr)
print("Environment loaded", file=sys.stderr)

try:
    from playwright.sync_api import sync_playwright
    print("Playwright imported", file=sys.stderr)
    
    with sync_playwright() as p:
        print("Playwright context created", file=sys.stderr)
        browser = p.chromium.launch(headless=True)
        print("Browser launched", file=sys.stderr)
        browser.close()
        print("Browser closed", file=sys.stderr)
    
    print("SUCCESS: Minimal browser test passed", file=sys.stderr)
    print('["test_success"]')
    
except Exception as e:
    print(f"ERROR: {e}", file=sys.stderr)
    print('[]')
"""
        
        result = subprocess.run(
            ["python", "-c", test_script],
            capture_output=True,
            text=True,
            timeout=30,
            cwd=str(Path.cwd())
        )
        
        print(f"Return code: {result.returncode}")
        print(f"Stdout: {result.stdout}")
        print(f"Stderr: {result.stderr}")
        
    except subprocess.TimeoutExpired:
        print("❌ Test timed out after 30 seconds")
    except Exception as e:
        print(f"❌ Test failed: {e}")
    
    print()

def main():
    print("🔍 RENDER ENVIRONMENT DIAGNOSTIC")
    print("=" * 60)
    print()
    
    check_system_info()
    check_environment_variables()
    check_playwright_installation()
    check_subprocess_execution()
    check_file_permissions()
    test_minimal_scraper()
    
    print("🏁 DIAGNOSTIC COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
