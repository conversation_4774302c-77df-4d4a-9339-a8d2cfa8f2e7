"""Add unique constraint to prevent duplicate catalogs

Revision ID: add_catalog_unique_constraint
Revises: 
Create Date: 2025-07-12 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a1b2c3d4e5f6'
down_revision: Union[str, None] = '769079bbbb19'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add unique constraint to prevent duplicate catalogs for same store and date range."""

    # Based on your CSV data, these are the exact duplicate IDs to remove:
    # Keep: 67, 66, 78, 68, 64, 81, 65, 72, 62 (newest ones)
    # Remove: 88, 87, 63, 70, 84, 82, 86, 74, 76 (older duplicates)

    duplicate_ids = "88,87,63,70,84,82,86,74,76"

    # Step 1: Clean up foreign key references first
    op.execute(f"""
        DELETE FROM processed_catalog_hashes
        WHERE catalog_db_id IN ({duplicate_ids})
    """)

    op.execute(f"""
        DELETE FROM catalog_processing_queue
        WHERE catalog_db_id IN ({duplicate_ids})
    """)

    # Step 2: Delete products and pages that reference these catalogs
    op.execute(f"""
        DELETE FROM products
        WHERE catalog_id IN ({duplicate_ids})
    """)

    op.execute(f"""
        DELETE FROM catalog_pages
        WHERE catalog_id IN ({duplicate_ids})
    """)

    # Step 3: Finally delete the duplicate catalogs
    op.execute(f"""
        DELETE FROM catalogs
        WHERE id IN ({duplicate_ids})
    """)

    # Step 4: Create the unique index to prevent future duplicates
    op.execute("""
        CREATE UNIQUE INDEX uq_catalog_store_date_range
        ON catalogs (store_id, DATE(valid_from), DATE(valid_to))
    """)


def downgrade() -> None:
    """Remove the unique constraint."""
    op.execute("DROP INDEX IF EXISTS uq_catalog_store_date_range")
