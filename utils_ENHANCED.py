# utils.py - ENHANCED VERSION
# Drop-in replacement with dramatically improved parsing + Danish-specific patterns
# Maintains exact same DB schema compatibility as original

import re
import logging
from typing import Tuple, Optional, Dict, Any

logger = logging.getLogger(__name__)

# --- Enhanced Regex Patterns for Danish Supermarket Catalogs ---

# 1. Handles ranges like "180-320 g", "2-3 stk", "500-750 ml"
REGEX_RANGE = re.compile(
    r"""^\s*             # Optional leading space
    ([\d.,]+)         # Min quantity - Capture group 1
    \s*[-–]\s*        # Hyphen or en-dash separator
    ([\d.,]+)         # Max quantity - Capture group 2
    \s*               # Optional space
    ([a-zA-ZæøåÆØÅ]+)   # Unit - Capture group 3
    \.?s?             # Optional period and plural 's'
    \s*$              # End of string
    """,
    re.IGNORECASE | re.VERBOSE
)

# 2. Handles multipliers like "3x100g", "2 x 75 cl", "6x330ml dåser"
REGEX_MULTIPACK = re.compile(
    r"""^\s*             # Optional leading space
    (\d+)             # Multiplier (e.g., "3") - Capture group 1
    \s*[xX×]\s*       # 'x' separator (including × symbol)
    ([\d.,]+)         # Quantity (e.g., "100") - Capture group 2
    \s*               # Optional space
    ([a-zA-ZæøåÆØÅ]+)   # Unit (e.g., "g") - Capture group 3
    \.?s?             # Optional period and plural 's'
    (?:\s+\w+)*       # Optional additional words like "dåser", "flasker"
    \s*$              # End of string
    """,
    re.IGNORECASE | re.VERBOSE
)

# 3. Handles simple cases like "500 g", "1.5 kg", "1 stk", "750 ml."
REGEX_SIMPLE = re.compile(
    r"""^\s*             # Optional leading space
    ([\d.,]+)         # Quantity - Capture group 1
    \s*               # Optional space
    ([a-zA-ZæøåÆØÅ]+)   # Unit - Capture group 2
    \.?s?             # Optional period and plural 's'
    \s*$              # End of string
    """,
    re.IGNORECASE | re.VERBOSE
)

# 4. NEW: Handles Danish approximations like "ca. 500g", "min. 2 kg", "max 3 stk"
REGEX_APPROXIMATE = re.compile(
    r"""^\s*             # Optional leading space
    (?:ca\.?|min\.?|max\.?|cirka|minimum|maksimum)\s*  # Danish approximation words
    ([\d.,]+)         # Quantity - Capture group 1
    \s*               # Optional space
    ([a-zA-ZæøåÆØÅ]+)   # Unit - Capture group 2
    \.?s?             # Optional period and plural 's'
    \s*$              # End of string
    """,
    re.IGNORECASE | re.VERBOSE
)

# 5. NEW: Handles "pr. stk", "per stk", "pr styk" patterns
REGEX_PER_UNIT = re.compile(
    r"""^\s*             # Optional leading space
    (?:pr\.?|per)\s*  # "pr." or "per"
    ([a-zA-ZæøåÆØÅ]+)   # Unit - Capture group 1
    \.?s?             # Optional period and plural 's'
    \s*$              # End of string
    """,
    re.IGNORECASE | re.VERBOSE
)

# 6. NEW: Handles already-calculated prices like "10 kr/kg", "pr. 100g: 5 kr"
REGEX_PRICE_PER_UNIT = re.compile(
    r"""^\s*             # Optional leading space
    (?:pr\.?\s*)?     # Optional "pr."
    (?:([\d.,]+)\s*)?  # Optional quantity - Capture group 1
    ([a-zA-ZæøåÆØÅ]+)   # Unit - Capture group 2
    \s*[:]\s*         # Colon separator
    ([\d.,]+)\s*kr    # Price in kr - Capture group 3
    \s*$              # End of string
    """,
    re.IGNORECASE | re.VERBOSE
)

# Complete UNIT_CONVERSION map from current utils.py
UNIT_CONVERSION = {
    # Weight
    'g': ('kg', 0.001),
    'gram': ('kg', 0.001),
    'kg': ('kg', 1.0),
    'kilogram': ('kg', 1.0),
    # Volume
    'ml': ('l', 0.001),
    'milliliter': ('l', 0.001),
    'cl': ('l', 0.01),
    'centiliter': ('l', 0.01),
    'dl': ('l', 0.1),
    'deciliter': ('l', 0.1),
    'l': ('l', 1.0),
    'liter': ('l', 1.0),
    # Pieces/Packages
    'stk': ('stk', 1.0),
    'styk': ('stk', 1.0),
    'styks': ('stk', 1.0),
    'pk': ('pakke', 1.0),
    'pakke': ('pakke', 1.0),
    'ps': ('pose', 1.0),
    'pose': ('pose', 1.0),
    'rl': ('rulle', 1.0),
    'rulle': ('rulle', 1.0),
    'bk': ('bakke', 1.0),
    'bakke': ('bakke', 1.0),
    'ks': ('kasse', 1.0),
    'kasse': ('kasse', 1.0),
}

def normalize_number(num_str: str) -> float:
    """Convert Danish number format to float (handles both . and , as decimal)"""
    if not num_str:
        return 0.0
    
    # Handle Danish comma as decimal separator
    if ',' in num_str and '.' not in num_str:
        num_str = num_str.replace(',', '.')
    elif ',' in num_str and '.' in num_str:
        # Handle cases like "1.234,56" (thousands separator + decimal)
        if num_str.rfind(',') > num_str.rfind('.'):
            # Comma is decimal separator
            num_str = num_str.replace('.', '').replace(',', '.')
        else:
            # Period is decimal separator
            num_str = num_str.replace(',', '')
    
    try:
        return float(num_str)
    except ValueError:
        logger.warning(f"Could not convert '{num_str}' to float")
        return 0.0

def parse_unit_string_enhanced(unit_string: Optional[str]) -> Optional[Dict[str, Any]]:
    """
    Enhanced parsing with Danish-specific patterns.
    Returns internal format for processing.
    """
    if not unit_string:
        return None

    s = unit_string.strip()
    if not s:
        return None

    # --- Waterfall of Enhanced Parsers ---
    
    # 1. Check for already-calculated price per unit
    match = REGEX_PRICE_PER_UNIT.match(s)
    if match:
        quantity_str, unit_type, price_str = match.groups()
        quantity = normalize_number(quantity_str) if quantity_str else 1.0
        return {
            "quantity_min": quantity,
            "quantity_max": quantity,
            "quantity": quantity,
            "unit_type": unit_type.lower(),
            "is_price_per_unit": True,
            "price_per_unit_value": normalize_number(price_str)
        }
    
    # 2. Try to match a range first (most specific)
    match = REGEX_RANGE.match(s)
    if match:
        min_q_str, max_q_str, unit_type = match.groups()
        min_q = normalize_number(min_q_str)
        max_q = normalize_number(max_q_str)
        if min_q > 0 and max_q > 0:
            return {
                "quantity_min": min_q,
                "quantity_max": max_q,
                "quantity": (min_q + max_q) / 2,  # Average for DB compatibility
                "unit_type": unit_type.lower()
            }

    # 3. Try to match a multipack
    match = REGEX_MULTIPACK.match(s)
    if match:
        multiplier_str, quantity_str, unit_type = match.groups()
        multiplier = normalize_number(multiplier_str)
        quantity = normalize_number(quantity_str)
        if multiplier > 0 and quantity > 0:
            total_quantity = multiplier * quantity
            return {
                "quantity_min": total_quantity,
                "quantity_max": total_quantity,
                "quantity": total_quantity,
                "unit_type": unit_type.lower()
            }

    # 4. Try Danish approximations
    match = REGEX_APPROXIMATE.match(s)
    if match:
        quantity_str, unit_type = match.groups()
        quantity = normalize_number(quantity_str)
        if quantity > 0:
            return {
                "quantity_min": quantity,
                "quantity_max": quantity,
                "quantity": quantity,
                "unit_type": unit_type.lower()
            }

    # 5. Try simple case
    match = REGEX_SIMPLE.match(s)
    if match:
        quantity_str, unit_type = match.groups()
        quantity = normalize_number(quantity_str)
        if quantity > 0:
            return {
                "quantity_min": quantity,
                "quantity_max": quantity,
                "quantity": quantity,
                "unit_type": unit_type.lower()
            }

    # 6. Try "pr. stk" patterns
    match = REGEX_PER_UNIT.match(s)
    if match:
        unit_type = match.group(1)
        return {
            "quantity_min": 1.0,
            "quantity_max": 1.0,
            "quantity": 1.0,
            "unit_type": unit_type.lower()
        }

    # 7. Fallback: Check if it's just a unit name
    clean_unit = s.lower().rstrip('.s')
    if clean_unit in UNIT_CONVERSION:
        return {
            "quantity_min": 1.0,
            "quantity_max": 1.0,
            "quantity": 1.0,
            "unit_type": clean_unit
        }

    # 8. Final fallback: couldn't parse, but preserve the string
    logger.debug(f"Could not parse unit string with enhanced patterns: '{unit_string}'")
    return None

def calculate_price_per_base_unit_enhanced(parsed_unit: Optional[Dict[str, Any]], price: Optional[float]) -> Dict[str, Any]:
    """
    Enhanced price calculation with better handling of ranges and edge cases.
    """
    result = {
        'price_per_base_unit': None,
        'base_unit_type': None
    }
    
    if not parsed_unit or not price or price <= 0:
        return result

    # Handle already-calculated price per unit
    if parsed_unit.get("is_price_per_unit"):
        result['price_per_base_unit'] = parsed_unit.get("price_per_unit_value")
        unit_type = parsed_unit.get('unit_type')
        if unit_type and unit_type in UNIT_CONVERSION:
            result['base_unit_type'] = UNIT_CONVERSION[unit_type][0]
        return result

    unit_type = parsed_unit.get('unit_type')
    if not unit_type or unit_type not in UNIT_CONVERSION:
        return result

    base_unit, factor = UNIT_CONVERSION[unit_type]
    result['base_unit_type'] = base_unit

    # Only calculate for weight/volume units
    if base_unit not in ['kg', 'l']:
        return result

    # Use average quantity for ranges (DB compatibility)
    quantity = parsed_unit.get("quantity", 0)
    
    try:
        if quantity > 0:
            quantity_in_base = quantity * factor
            result['price_per_base_unit'] = round(price / quantity_in_base, 2)
    except (ZeroDivisionError, TypeError):
        logger.warning(f"Could not calculate unit price for parsed unit {parsed_unit}")

    return result

# --- Main Function: Drop-in Replacement for Current Utils ---
def parse_and_calculate_unit_price(unit_string: Optional[str], price: Optional[float]) -> Dict[str, Any]:
    """
    ENHANCED drop-in replacement for the current utils function.
    
    Dramatically improved parsing with Danish-specific patterns while maintaining
    exact same return structure for database compatibility.
    
    Returns same format as original:
    {
        "quantity": float or None,
        "unit_type": str or None, 
        "price_per_base_unit": float or None,
        "base_unit_type": str or None
    }
    """
    # Use enhanced parsing
    parsed_unit = parse_unit_string_enhanced(unit_string)
    
    if parsed_unit:
        # Calculate price using enhanced logic
        price_calcs = calculate_price_per_base_unit_enhanced(parsed_unit, price)
        
        # Return EXACT same structure as original for DB compatibility
        return {
            "quantity": parsed_unit.get("quantity"),
            "unit_type": parsed_unit.get("unit_type"),
            "price_per_base_unit": price_calcs.get("price_per_base_unit"),
            "base_unit_type": price_calcs.get("base_unit_type")
        }
    else:
        # Enhanced fallback - preserve original string instead of returning null
        return {
            "quantity": None,
            "unit_type": unit_string,  # Store raw string for manual review
            "price_per_base_unit": None,
            "base_unit_type": None
        }
