from google import genai
from google.genai import types
import os
import logging
import json
from dotenv import load_dotenv
from sqlalchemy import select, func
from database import SessionLocal, Product, Store, Catalog, get_setting
from datetime import datetime
from typing import Optional, List
import config

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration is handled externally by api.py calling reconfigure_gemini
# GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
# if not GOOGLE_API_KEY:
#     logger.error("GOOGLE_API_KEY not found in environment variables")
# else:
#     try:
#         genai.configure(api_key=GOOGLE_API_KEY)
#     except Exception as e:
#         logger.error(f"Error configuring Gemini API: {e}")

# Removed is_gemini_available check

def get_product_data(selected_catalog_ids: Optional[List[int]] = None):
    """
    Get product data from the database, optionally filtered by catalog IDs.
    
    Args:
        selected_catalog_ids: Optional list of catalog IDs to filter by.
        
    Returns:
        Dictionary with product data by store and category
    """
    db = SessionLocal()
    try:
        # Base query
        stmt = (
            select(
                Product.id,
                Product.name, 
                Product.description, 
                Product.price, 
                Product.original_price,
                Product.unit,
                Product.category,
                Product.brand,
                Product.image_path,
                Product.quantity,
                Product.unit_type,
                Product.price_per_base_unit,
                Product.base_unit_type,
                Catalog.valid_from,
                Catalog.valid_to,
                Store.name.label("store_name")
            )
            .join(Catalog, Product.catalog_id == Catalog.id)
            .join(Store, Catalog.store_id == Store.id)
            # Always filter for currently valid catalogs initially
            .where(Catalog.valid_to >= datetime.now())
        )
        
        # Add filter for selected catalog IDs if provided
        if selected_catalog_ids:
            logger.info(f"Filtering product data for catalog IDs: {selected_catalog_ids}")
            stmt = stmt.where(Product.catalog_id.in_(selected_catalog_ids))
        else:
            logger.info("No specific catalogs selected, fetching data from all valid catalogs.")
            
        
        results = db.execute(stmt).all()
        logger.info(f"Found {len(results)} relevant product entries.")
        
        # Organize data by store and category
        product_data = {}
        if not results:
            logger.warning("No product data found matching the criteria (valid date and selected catalogs).")
            return {} # Return empty dict if no results
            
        for product in results:
            store_name = product.store_name
            if store_name not in product_data:
                product_data[store_name] = {}
                
            category = product.category or "Uncategorized"
            if category not in product_data[store_name]:
                product_data[store_name][category] = []
                
            product_data[store_name][category].append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": product.price,
                "original_price": product.original_price,
                "unit": product.unit,
                "brand": product.brand,
                "image_path": product.image_path,
                "quantity": product.quantity,
                "unit_type": product.unit_type,
                "price_per_base_unit": product.price_per_base_unit,
                "base_unit_type": product.base_unit_type,
                "valid_from": product.valid_from.strftime("%Y-%m-%d") if product.valid_from else None,
                "valid_to": product.valid_to.strftime("%Y-%m-%d") if product.valid_to else None,
            })
            
        return product_data
    
    finally:
        db.close()

def get_cheapest_products_by_category(category):
    """
    Get the cheapest products in a specific category
    
    Args:
        category: Product category to search
        
    Returns:
        List of cheapest products by store
    """
    db = SessionLocal()
    try:
        # Find products in the specified category across all valid catalogs
        query = (
            select(
                Product.name,
                Product.description,
                Product.price,
                Product.unit,
                Store.name.label("store_name")
            )
            .join(Catalog, Product.catalog_id == Catalog.id)
            .join(Store, Catalog.store_id == Store.id)
            .where(
                Product.category.ilike(f"%{category}%"),
                Catalog.valid_to >= datetime.now()
            )
            .order_by(Product.price.asc())
        )
        
        results = db.execute(query).all()
        
        # Find cheapest product in each store
        cheapest_by_store = {}
        for product in results:
            store = product.store_name
            if store not in cheapest_by_store:
                cheapest_by_store[store] = {
                    "name": product.name,
                    "description": product.description,
                    "price": product.price,
                    "unit": product.unit,
                    "store": store
                }
                
        return list(cheapest_by_store.values())
    
    finally:
        db.close()

def get_available_categories():
    """
    Get all available product categories
    
    Returns:
        List of unique categories
    """
    db = SessionLocal()
    try:
        categories = db.query(Product.category).distinct().all()
        return [c[0] for c in categories if c[0]]
    finally:
        db.close()

# Define the default prompt constant (useful as fallback)
QUERY_SYSTEM_PROMPT = """
You are a helpful shopping assistant for Danish supermarkets.
Your task is to help users find information about products and offers based on supermarket catalog data.

You will be given a JSON object containing current offers from various supermarkets, 
organized by store name and product category.

Follow these instructions carefully:
1. You can suggest recipes and meal plans based on what's currently on offer.
2. You can identify the cheapest products in specific categories.
3. Always consider price and availability when making recommendations.
4. Respond in Danish or English, matching the language of the user's query.
5. Be concise and practical with your answers.
6. All prices are in Danish Kroner (DKK).

If you need to know what categories are available or need to search for specific products not directly
visible in the data, say so and explain what additional information would help.
"""

def process_natural_language_query(
    query_text: str, 
    model_name: Optional[str] = None, 
    selected_catalog_ids: Optional[List[int]] = None,
    temperature: float = 0.7, 
    top_p: float = 0.95, 
    top_k: int = 40, 
    max_tokens: Optional[int] = None, 
    system_prompt: Optional[str] = None # Keep allowing direct override
):
    """
    Process a natural language query using Gemini
    
    Args:
        query_text: User's natural language query
        model_name: Specific Gemini model name to use (optional)
        selected_catalog_ids: Optional list of catalog IDs to filter data by
        system_prompt: Direct override for the system prompt (optional)
        
    Returns:
        Response from Gemini or specific message if no data
    """
    # Check if Gemini API is configured (Removed - rely on downstream errors)
    # if not is_gemini_available():
    #     return {
    #         "answer": "Gemini API is not configured. Please add your GOOGLE_API_KEY in the settings.",
    #         "error": True
    #     }
        
    logger.info(f"Processing query: '{query_text}' for catalogs: {selected_catalog_ids or 'All'}")
    
    # Get product data, passing selected IDs
    product_data = get_product_data(selected_catalog_ids=selected_catalog_ids)
    
    # If no product data found after filtering, return a structured error
    if not product_data:
        logger.warning("No product data found for the selected catalogs or date range. Cannot query Gemini.")
        error_message = "Jeg kunne ikke finde nogen produkttilbud i de valgte kataloger (eller de er udløbet). Prøv at vælge andre kataloger eller upload nye."
        return {"answer": error_message, "products": [], "error": True} # Return structured error
    
    # Special case handling (might need adjustment to use filtered data)
    # For now, these helpers still query all data - potential future enhancement
    lower_query = query_text.lower()
    if "cheapest" in lower_query and any(word in lower_query for word in ["where", "which", "what"]):
        for category in get_available_categories(): # This uses all categories, not filtered ones
            if category.lower() in lower_query:
                # This gets cheapest from *all* stores, not just selected
                cheapest_products = get_cheapest_products_by_category(category) 
                if cheapest_products:
                    # Overwrite product_data with just cheapest (consider if this is desired)
                    product_data = {"cheapest_products": cheapest_products, "category": category}
                    logger.info(f"Query identified as 'cheapest {category}'. Using specific data structure.")
                    break # Stop after first category match
    
    # Convert the potentially modified product data to JSON string
    product_data_json = json.dumps(product_data, indent=2, ensure_ascii=False)
    
    # Get context limits from database settings
    MAX_CONTEXT_LENGTH = config.get_int_setting_from_db("QUERY_MAX_CONTEXT_LENGTH", 800000)  # Modern LLM can handle much more
    MAX_PRODUCTS_PER_CATEGORY = config.get_int_setting_from_db("QUERY_MAX_PRODUCTS_PER_CATEGORY", 100)  # Allow many more products

    if len(product_data_json) > MAX_CONTEXT_LENGTH:
        logger.warning(f"Product data JSON is very large ({len(product_data_json)} chars). Reducing data to prevent API limits.")

        # Reduce data by limiting products per store/category
        reduced_product_data = {}

        for store_name, categories in product_data.items():
            reduced_product_data[store_name] = {}
            for category_name, products in categories.items():
                # Take only the first N products per category
                reduced_products = products[:MAX_PRODUCTS_PER_CATEGORY]
                if reduced_products:  # Only include if there are products
                    reduced_product_data[store_name][category_name] = reduced_products

        # Update the JSON with reduced data
        product_data_json = json.dumps(reduced_product_data, indent=2, ensure_ascii=False)
        logger.info(f"Reduced product data to {len(product_data_json)} chars ({MAX_PRODUCTS_PER_CATEGORY} products per category)")

        # Update product_data for later use
        product_data = reduced_product_data

    try:
        # --- Get Settings from Database ---
        # Determine model name: Direct override > Database > Default
        configured_query_model = config.get_setting_from_db('GEMINI_QUERY_MODEL')
        
        # New model selection logic:
        # 1. Direct override (model_name argument)
        # 2. Configured query model (from settings)
        # 3. New hardcoded fallback model ('models/gemini-2.5-flash-preview-04-17')
        
        if model_name:
            effective_model_name = model_name
            logger.info(f"Using model name from direct argument: {effective_model_name}")
        elif configured_query_model:
            effective_model_name = configured_query_model
            logger.info(f"Using query model from app configuration: {effective_model_name}")
        else:
            effective_model_name = 'models/gemini-1.5-flash-latest' # Corrected fallback
            logger.info(f"Using fallback query model: {effective_model_name}")

        # Ensure effective_model_name is not None or empty after the logic above
        if not effective_model_name: # This check is now more of a safeguard
            logger.error("CRITICAL: Model name resolved to empty or None despite fallbacks. Defaulting to 'models/gemini-2.5-flash-preview-04-17'. This should not happen.")
            effective_model_name = 'models/gemini-2.5-flash-preview-04-17'
        
        # Determine system prompt: Direct override > Database > Default Constant
        effective_system_prompt = system_prompt or config.get_setting_from_db('GEMINI_QUERY_SYSTEM_PROMPT', QUERY_SYSTEM_PROMPT)
        # --- End Get Settings ---
        
        logger.info(f"Using query model: {effective_model_name}")

        # --- NEW WAY to use unified google-genai SDK ---
        # Initialize client
        client = genai.Client(api_key=os.getenv("GOOGLE_API_KEY"))

        # Get safety settings from database
        safety_settings = [
            types.SafetySetting(
                category="HARM_CATEGORY_HATE_SPEECH",
                threshold=config.get_setting_from_db("GEMINI_SAFETY_HATE_SPEECH", "BLOCK_ONLY_HIGH")
            ),
            types.SafetySetting(
                category="HARM_CATEGORY_DANGEROUS_CONTENT",
                threshold=config.get_setting_from_db("GEMINI_SAFETY_DANGEROUS_CONTENT", "BLOCK_ONLY_HIGH")
            ),
            types.SafetySetting(
                category="HARM_CATEGORY_HARASSMENT",
                threshold=config.get_setting_from_db("GEMINI_SAFETY_HARASSMENT", "BLOCK_ONLY_HIGH")
            ),
            types.SafetySetting(
                category="HARM_CATEGORY_SEXUALLY_EXPLICIT",
                threshold=config.get_setting_from_db("GEMINI_SAFETY_SEXUALLY_EXPLICIT", "BLOCK_ONLY_HIGH")
            )
        ]

        # Get additional generation parameters from database
        candidate_count = config.get_int_setting_from_db("GEMINI_CANDIDATE_COUNT", 1)
        seed = config.get_setting_from_db("GEMINI_SEED", "")
        stop_sequences_str = config.get_setting_from_db("GEMINI_STOP_SEQUENCES", "[]")
        presence_penalty = config.get_float_setting_from_db("GEMINI_PRESENCE_PENALTY", 0.0)
        frequency_penalty = config.get_float_setting_from_db("GEMINI_FREQUENCY_PENALTY", 0.0)

        # Parse stop sequences from JSON string
        try:
            stop_sequences = json.loads(stop_sequences_str) if stop_sequences_str else []
        except:
            stop_sequences = []

        # Prepare content with system instruction
        contents = [
            types.Content(
                role='user',
                parts=[
                    types.Part.from_text(text=f"System: {effective_system_prompt}"),
                    types.Part.from_text(text=f"User query: {query_text}"),
                    types.Part.from_text(text=f"Catalog Data:\n{product_data_json}")
                ]
            )
        ]

        # Build generation config
        generation_config = types.GenerateContentConfig(
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
            max_output_tokens=max_tokens if max_tokens else config.get_int_setting_from_db("GEMINI_MAX_OUTPUT_TOKENS", 2048),
            candidate_count=candidate_count,
            presence_penalty=presence_penalty,
            frequency_penalty=frequency_penalty,
            safety_settings=safety_settings
        )

        # Add seed if provided
        if seed and seed.strip():
            generation_config.seed = int(seed)

        # Add stop sequences if provided
        if stop_sequences:
            generation_config.stop_sequences = stop_sequences

        # Generate content with new API
        response = client.models.generate_content(
            model=effective_model_name,
            contents=contents,
            config=generation_config
        )
        # --- END NEW WAY ---
        
        # --- ADD LOGGING FOR RAW RESPONSE ---
        logger.info(f"Raw Gemini Response Object Type: {type(response)}")

        # Handle response with new API structure
        response_text = ""  # Default to empty string

        try:
            # New API has different structure - check for text directly first
            # But wrap in try-catch since response.text can throw exceptions
            try:
                if hasattr(response, 'text') and response.text:
                    response_text = response.text
                    logger.info(f"Gemini Response Text (first 500 chars): {response_text[:500]}...")

                    # Enhanced logging if marker is likely missing
                    if "RELEVANT_PRODUCTS" not in response_text:
                        logger.info(f"Gemini Response Text (first 2000 chars, marker not found): {response_text[:2000]}...")

                    # Successfully extracted text, skip candidate checking
                    logger.info("Successfully extracted text from response.text, skipping candidate processing")

                else:
                    # No text available, check candidates
                    raise ValueError("No text available from response.text")
            except Exception as text_err:
                logger.warning(f"Could not access response.text: {text_err}")
                # Fall back to checking candidates and finish reasons
                response_text = ""  # Reset to empty for candidate processing

            # Only check candidates if we don't have text yet
            if not response_text and hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                finish_reason = getattr(candidate, 'finish_reason', None)
                logger.info(f"Gemini Response finish_reason: {finish_reason} (type: {type(finish_reason)})")

                # Handle finish_reason values based on official Google GenAI documentation
                # From Context7: FinishReason enum includes SAFETY, MAX_TOKENS, STOP, RECITATION, OTHER, BLOCKLIST, PROHIBITED_CONTENT, etc.
                if finish_reason in ["SAFETY", 2]:
                    logger.warning("Gemini response was blocked by safety filters")
                    response_text = "Beklager, din forespørgsel blev blokeret af sikkerhedsfiltre. Dette kan skyldes for meget data eller indhold der udløser sikkerhedsregler. Prøv at omformulere din søgning eller vælg færre kataloger."

                elif finish_reason in ["MAX_TOKENS", 1]:
                    logger.warning("Gemini response was truncated due to max tokens")
                    response_text = "Svaret blev afbrudt på grund af længdebegrænsninger. Prøv at vælge færre kataloger eller en mere specifik søgning."

                elif finish_reason in ["RECITATION", 3]:
                    logger.warning("Gemini response was blocked due to recitation")
                    response_text = "Svaret blev blokeret på grund af potentiel kopiering af eksisterende indhold. Prøv at omformulere din søgning."

                elif finish_reason in ["BLOCKLIST", "PROHIBITED_CONTENT", "OTHER", 4]:
                    logger.warning(f"Gemini response was blocked for reason: {finish_reason}")
                    response_text = "Der opstod et problem med AI-modellen. Prøv igen eller kontakt support."

                elif finish_reason in ["MALFORMED_FUNCTION_CALL", "UNEXPECTED_TOOL_CALL"]:
                    logger.warning(f"Gemini response had function call issues: {finish_reason}")
                    response_text = "Der opstod et problem med funktionskald i AI-modellen. Prøv igen."

                elif finish_reason in ["IMAGE_SAFETY", "LANGUAGE", "SPII"]:
                    logger.warning(f"Gemini response was blocked for content reason: {finish_reason}")
                    response_text = "Indholdet blev blokeret af sikkerhedsfiltre. Prøv at omformulere din søgning."

                elif finish_reason in ["STOP", 0]:  # Successful completion
                    # Try to get text from candidate
                    if hasattr(candidate, 'content') and candidate.content:
                        # Extract text from content parts
                        text_parts = []
                        for part in candidate.content.parts:
                            if hasattr(part, 'text'):
                                text_parts.append(part.text)
                        response_text = ''.join(text_parts)
                        logger.info(f"Extracted text from candidate: {response_text[:500]}...")
                    else:
                        response_text = "Svar blev genereret men kunne ikke udtrækkes. Prøv igen."

                else:
                    logger.warning(f"Gemini response had unexpected finish_reason: {finish_reason}")
                    response_text = "Der opstod et uventet problem med AI-modellen. Prøv igen eller kontakt support."

            elif not response_text:  # Only log error if we truly have no text
                logger.error("Gemini response has no text or candidates")
                response_text = "Ingen svar blev genereret af AI-modellen. Prøv igen."

        except Exception as response_err:
            logger.error(f"Error processing Gemini response: {response_err}")
            response_text = "Der opstod en fejl under behandling af AI-svaret. Prøv igen."
        # --- END LOGGING ---

        # --- Return both the LLM answer AND the structured product data ---
        # Flatten the product_data dictionary structure for easier frontend use
        flat_product_list = []
        for store_name, categories in product_data.items():
            for category_name, products in categories.items():
                for product_item in products:
                    product_item['store_name'] = store_name # Add store name to each product
                    product_item['category'] = category_name # Ensure category is consistent
                    flat_product_list.append(product_item)

        # --- NEW: Attempt to parse structured product list and filter --- 
        final_product_list = [] # Initialize to EMPTY LIST by default
        marker_found = False
        try:
            # Use the marker observed in testing
            marker = "RELEVANT_PRODUCTS_V2_JSON_BLOCK:" # Updated marker string
            if marker in response_text:
                marker_found = True # Set flag
                # Extract text after the marker
                structured_part = response_text.split(marker, 1)[1].strip()
                # Find the start and end of the JSON list
                json_start = structured_part.find('[')
                json_end = structured_part.rfind(']') + 1
                if json_start != -1 and json_end != -1:
                    json_string = structured_part[json_start:json_end]
                    logger.info(f"Found relevant products marker. Extracted JSON string: {json_string}")
                    try:
                        relevant_data = json.loads(json_string)
                        # Expecting list of dicts like [{'id': 123}, ...]
                        if isinstance(relevant_data, list):
                            relevant_ids = set() # Use a set for efficient lookup
                            for item in relevant_data:
                                if isinstance(item, dict) and item.get('id') is not None: # Check for id
                                    try:
                                        # Ensure the id is an integer
                                        relevant_ids.add(int(item.get('id')))
                                    except (ValueError, TypeError):
                                        logger.warning(f"Could not parse product ID from AI response: {item.get('id')}")
                                        
                            if relevant_ids: # Only filter if we got valid IDs
                                logger.info(f"Successfully parsed {len(relevant_ids)} relevant product IDs. Filtering products...")
                                # Filter the original list based on product IDs
                                filtered_list = [p for p in flat_product_list if p.get('id') in relevant_ids]
                                
                                if filtered_list: # Check if filtering resulted in any products
                                    final_product_list = filtered_list
                                    logger.info(f"Filtering successful. Reduced list to {len(final_product_list)} products.")
                                else:
                                    logger.warning("Filtering based on AI IDs resulted in an empty list (IDs might not match DB). Defaulting to empty related offers.")
                                    final_product_list = [] # Ensure it's empty
                            else:
                                 logger.warning("Parsed JSON list, but it contained no valid 'id' entries. Using empty related offers.")
                                 final_product_list = [] # Ensure it's empty
                        else:
                            logger.warning(f"Parsed JSON after marker, but it was not a list: {type(relevant_data)}. Using empty related offers.")
                            final_product_list = [] # Ensure it's empty
                    except json.JSONDecodeError as json_err:
                        logger.warning(f"Failed to decode JSON after marker: {json_err}. Raw string: {json_string}. Using empty related offers.")
                        final_product_list = [] # Ensure it's empty
                else:
                     logger.warning(f"Found marker '{marker}', but no valid JSON list boundaries '[]' afterwards. Using empty related offers.")
                     final_product_list = [] # Ensure it's empty
            else: # No marker found 
               logger.info("No relevant products marker found in response. Using empty related offers.")
               final_product_list = [] # Ensure it's empty

        except Exception as filter_err: # Catch any unexpected error during parsing/filtering
            logger.error(f"Unexpected error during product list filtering: {filter_err}. Falling back to empty related offers.", exc_info=True)
            final_product_list = [] # Ensure fallback on any error
        # --- END FILTERING LOGIC ---

        # --- NEW: Strip debug output based on setting ---
        db_for_settings = SessionLocal()
        try:
            # Default to True if setting not found, so debug IS shown unless explicitly set to false
            show_debug_str = get_setting(db_for_settings, "SHOW_AI_DEBUG_OUTPUT", "true") 
            if show_debug_str.lower() != "true":
                if marker_found: # Only strip if marker was actually found
                    logger.info(f"SHOW_AI_DEBUG_OUTPUT is false. Stripping '{marker}' and subsequent JSON from response_text.")
                    response_text = response_text.split(marker, 1)[0].strip()
            else:
                if marker_found: # Log only if marker was present
                    logger.info(f"SHOW_AI_DEBUG_OUTPUT is true. Debug marker will be visible.")
        except Exception as e:
            logger.error(f"Error retrieving or processing SHOW_AI_DEBUG_OUTPUT setting: {e}. Debug output may be visible.", exc_info=True)
        finally:
            db_for_settings.close()
        # --- END STRIPPING LOGIC ---

        return {
            "answer": response_text,
            "products": final_product_list # Return the potentially filtered list (or empty)
        }
        # --- End modification ---

    except Exception as e:
        logger.error(f"Error querying Gemini: {e}", exc_info=True)
        error_message = f"Der opstod en fejl under behandling af din forespørgsel med AI-modellen ({effective_model_name}). Fejl: {e}"
        # Return structured error including the model name
        return {"answer": error_message, "products": [], "error": True} 

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        query = " ".join(sys.argv[1:])
    else:
        query = input("Enter your query about supermarket offers: ")
    
    response = process_natural_language_query(query)
    print(response)