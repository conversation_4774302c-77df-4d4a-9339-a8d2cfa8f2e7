from database import init_db, SessionLocal, Store, User
from auth_utils import hash_password
import os
import logging

# --- Configuration --- 
default_admin_username = "admin"
default_admin_password = "adminpassword" # Change this default password!

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%d-%m %H:%M:%S')
logger = logging.getLogger(__name__)

def initialize_database():
    """Initialize the database: create tables, add default stores, ensure admin user exists."""
    logger.info("Initializing database (creating tables if they don't exist)...")
    init_db() # Ensures all tables defined in Base are created
    logger.info("Table creation check complete.")

    db = SessionLocal()
    
    try:
        # --- Add Default Stores ---
        logger.info("Checking/adding default stores...")
        default_stores = [
            {"name": "Netto", "logo_url": "assets/netto_logo.png"},
            {"name": "Føtex", "logo_url": "assets/fotex_logo.png"},
            {"name": "Bilka", "logo_url": "assets/bilka_logo.png"},
            {"name": "Rema 1000", "logo_url": "assets/rema1000_logo.png"},
            {"name": "Lidl", "logo_url": "assets/lidl_logo.png"},
            {"name": "Aldi", "logo_url": "assets/aldi_logo.png"},
        ]
        for store_data in default_stores:
            existing = db.query(Store).filter(Store.name == store_data["name"]).first()
            if not existing:
                store = Store(**store_data)
                db.add(store)
                logger.info(f"Added store: {store_data['name']}")
            # else:
                # logger.info(f"Store {store_data['name']} already exists")
        db.commit() # Commit stores first
        logger.info("Default stores check complete.")

        # --- Ensure Admin User Exists ---
        logger.info(f"Checking for default admin user '{default_admin_username}'...")
        admin_user = db.query(User).filter(User.username == default_admin_username).first()
        
        if not admin_user:
            logger.info(f"Admin user '{default_admin_username}' not found, creating...")
            hashed_pw = hash_password(default_admin_password)
            new_admin = User(
                username=default_admin_username, 
                hashed_password=hashed_pw,
                is_admin=True # Set the admin flag
            )
            db.add(new_admin)
            db.commit() # Commit admin user separately
            logger.info(f"Default admin user '{default_admin_username}' created successfully.")
            logger.warning(f"IMPORTANT: Default admin password is '{default_admin_password}'. Change this if deploying publicly!")
        else:
            logger.info(f"Admin user '{default_admin_username}' already exists.")
            # Optionally ensure existing admin has the flag set:
            if not admin_user.is_admin:
                logger.warning(f"Existing user '{default_admin_username}' found but is not admin. Setting admin flag.")
                admin_user.is_admin = True
                db.commit()

        logger.info("Database initialization complete.")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error during database initialization: {e}")
    finally:
        db.close()
    
    # --- Create necessary directories ---
    # (Keep this part as is)
    os.makedirs("catalogs", exist_ok=True)
    os.makedirs("images", exist_ok=True)
    os.makedirs("assets", exist_ok=True)
    logger.info("Checked/created necessary directories.")

if __name__ == "__main__":
    initialize_database() 