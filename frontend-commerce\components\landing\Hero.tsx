"use client";

import Image from "next/image";
import { useState, useEffect } from "react";

// 🦉 CLIPPY-STYLE OWL TIPS IN DANISH
const danishTips = [
  "Hej! Jeg er din AI-assistent. Spørg mig om tilbud! 🛒",
  "Tip: Prøv at søge efter 'billig kaffe' eller 'økologiske æg' 🥚",
  "Vidste du? Jeg kan finde tilbud fra alle danske supermarkeder! 🏪",
  "Smart tip: Vælg butikker nedenfor før du søger 🎯",
  "Prøv at spørge: 'Hvor er jordbær billigst denne uge?' 🍓",
  "Jeg kan hjælpe dig med at spare penge på indkøb! 💰",
  "Tip: Jeg forstår både danske og engelske søgninger 🇩🇰",
  "Spørg mig om specifikke mærker eller produkttyper! 🔍",
];

export default function Hero() {
  const [currentTip, setCurrentTip] = useState(0);
  const [showTip, setShowTip] = useState(true);

  // 🔄 Rotate tips every 10 seconds (slower pace)
  useEffect(() => {
    const interval = setInterval(() => {
      setShowTip(false);
      setTimeout(() => {
        setCurrentTip((prev) => (prev + 1) % danishTips.length);
        setShowTip(true);
      }, 300); // Brief fade out before changing
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="text-center py-2 px-6">
      <div className="max-w-5xl mx-auto relative z-10 flex flex-col items-center">
        {/* 🦉 CLIPPY-STYLE OWL WITH SPEECH BUBBLE */}
        <div className="relative mb-4">
          {/* Speech Bubble */}
          <div
            className={`absolute -top-4 -left-8 md:-left-12 bg-white border-2 border-sky-200 rounded-xl px-4 py-3 shadow-lg max-w-xs z-20 transition-all duration-300 ${
              showTip ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
            }`}
          >
            <div className="text-sm text-gray-700 font-medium">
              {danishTips[currentTip]}
            </div>
            {/* Speech bubble tail - pointing to the right toward the owl */}
            <div className="absolute bottom-0 right-8 transform translate-y-full">
              <div className="w-0 h-0 border-l-12 border-r-12 border-t-12 border-l-transparent border-r-transparent border-t-sky-200"></div>
              <div className="w-0 h-0 border-l-10 border-r-10 border-t-10 border-l-transparent border-r-transparent border-t-white absolute -top-1 left-1"></div>
            </div>
          </div>

          {/* Owl Logo */}
          <div className="w-60 h-60 md:w-72 md:h-72 relative cursor-pointer hover:scale-105 transition-transform duration-200">
            <Image
              src="/logos/logo-samlet.png"
              alt="Tilbudsjægeren AI Owl Assistant"
              fill
              className="object-contain"
              priority
            />
          </div>
        </div>

        <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6 leading-tight text-center">
          Find de Bedste Tilbud med <span className="text-sky-500">AI</span>
        </h2>
        
        
      </div>
    </div>
  );
}
