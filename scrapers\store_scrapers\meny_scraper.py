# File: scrapers/store_scrapers/meny_scraper.py

import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional
import aiohttp
import re

# Assuming scraper_core.py is in the parent directory (scrapers/)
# Adjust import if your structure is different or use absolute imports if project is a package
from ..scraper_core import <PERSON><PERSON>craper, PlaywrightTimeoutError, PlaywrightError, Page


class MenyScraper(BaseScraper):
    """
    Scraper specifically for Meny catalogs.
    Meny typically has a direct link to the current catalog viewer,
    and the date information is often in the page title (e.g., "MENY uge 2225").
    """

    def __init__(self, config: dict):
        super().__init__(config)
        self.logger.info(f"MenyScraper initialized for store: {self.store_name}")

    async def _attempt_download_click(self, page: Page, selectors: List[str], timeout_ms: int) -> Optional[str]:
        """Helper to try clicking download buttons from a list of selectors."""
        for i, selector in enumerate(selectors):
            self.logger.debug(f"_attempt_download_click: Iterating selector '{selector}'. Page closed: {page.is_closed()}")
            try:
                # Check for coordinate click fallback
                if selector.startswith("COORDINATE_CLICK:"):
                    coords = selector.replace("COORDINATE_CLICK:", "").split(",")
                    x, y = int(coords[0]), int(coords[1])
                    self.logger.info(f"🎯 Using coordinate click at ({x}, {y}) - Jonas's exact coordinates")
                    async with page.expect_download(timeout=self.config.get('behavior_flags', {}).get('download_event_timeout_ms', 30000)) as download_info:
                        await page.click(f"css=body", position={"x": x, "y": y})
                        download = await download_info.value
                        pdf_url = download.url
                        self.logger.info(f"✅ Coordinate click successful! Download URL: {pdf_url}")
                        return pdf_url

                # Regular selector logic
                self.logger.info(f"Attempting to find download button with selector: '{selector}' (Attempt {i+1}/{len(selectors)})")
                self.logger.debug(f"_attempt_download_click: Before page.locator for '{selector}'. Page closed: {page.is_closed()}")
                download_button = page.locator(selector).first
                self.logger.debug(f"_attempt_download_click: Before download_button.wait_for for '{selector}'. Page closed: {page.is_closed()}")
                await download_button.wait_for(state="visible", timeout=timeout_ms / len(selectors)) # Split timeout
                self.logger.debug(f"_attempt_download_click: After download_button.wait_for for '{selector}'. Page closed: {page.is_closed()}")

                if await download_button.is_visible():
                    self.logger.info(f"Download button '{selector}' found and visible. Expecting download...")
                    self.logger.debug(f"_attempt_download_click: Before page.expect_download for '{selector}'. Page closed: {page.is_closed()}")
                    async with page.expect_download(timeout=self.config.get('behavior_flags', {}).get('download_event_timeout_ms', 30000)) as download_info:
                        await download_button.click()
                        self.logger.debug(f"_attempt_download_click: After download_button.click for '{selector}'. Page closed: {page.is_closed()}")
                        download = await download_info.value
                        pdf_url = download.url
                        self.logger.info(f"Download URL for '{selector}': {pdf_url}")
                        return pdf_url
            except PlaywrightTimeoutError:
                self.logger.debug(f"Timeout waiting for or clicking download button '{selector}'.")
            except Exception as e:
                self.logger.warning(f"Error with download button selector '{selector}': {e}")
        self.logger.error(f"Failed to click any download button using selectors: {selectors}")
        return None

    async def scrape_catalogs(self) -> List[Dict[str, Any]]:
        """
        Scrapes catalog data for Meny.
        """
        if not self.page or not self.context or not self.catalog_list_url: 
            self.logger.error("Page, context, or catalog_list_url not available for MenyScraper.")
            return []

        try:
            default_nav_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            default_el_timeout = self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 30000)
            
            if self.page:
                 self.page.set_default_navigation_timeout(default_nav_timeout)
                 self.page.set_default_timeout(default_el_timeout)
                 self.logger.info(f"MenyScraper: Page default navigation timeout: {default_nav_timeout}ms, element timeout: {default_el_timeout}ms SET ON PAGE.")
        except Exception as e_timeout_set:
            self.logger.error(f"MenyScraper: Error setting default timeouts: {e_timeout_set}")

        self.logger.debug(f"scrape_catalogs: Start. Page closed: {self.page.is_closed() if self.page else 'N/A'}")
        # 1. Navigate to the catalog list/viewer URL
        self.logger.debug(f"scrape_catalogs: Before _navigate_to_url. Page closed: {self.page.is_closed()}")
        nav_success = await self._navigate_to_url(self.page, self.catalog_list_url)
        self.logger.debug(f"scrape_catalogs: After _navigate_to_url. Success: {nav_success}. Page closed: {self.page.is_closed()}")
        if not nav_success:
            self.logger.error(f"Failed to navigate to Meny catalog page: {self.catalog_list_url}")
            return []

        # Adding a targeted delay for Meny as it seems to need it for stability.
        self.logger.info("Applying a 4-second delay for Meny page to settle...")
        await asyncio.sleep(4)



        # 2. Handle Cookies
        cookie_selectors = self.config.get('selectors', {}).get('cookie_accept_selectors', [])
        self.logger.debug(f"scrape_catalogs: Before _handle_cookies. Page closed: {self.page.is_closed()}")
        if cookie_selectors:
            await self._handle_cookies(self.page, cookie_selectors, 
                                       timeout_ms=self.config.get('behavior_flags', {}).get('cookie_consent_timeout_ms', 10000))
            self.logger.debug(f"scrape_catalogs: After _handle_cookies. Page closed: {self.page.is_closed()}")
        else:
            self.logger.info("No cookie selectors configured for Meny.")
            
        # Give page a moment to settle after potential cookie interaction



        # 3. Extract Date Info (from page title as per meny.json config)
        raw_date_info = "Meny_Unknown_Date" # Default
        catalog_title_for_file = "Meny_Catalog"
        
        if self.config.get('behavior_flags', {}).get('extract_catalog_date_from_title', False):
            self.logger.debug(f"scrape_catalogs: Before page.title(). Page closed: {self.page.is_closed()}")
            try:
                page_title = await self.page.title()
                self.logger.debug(f"scrape_catalogs: After page.title(). Title: '{page_title}'. Page closed: {self.page.is_closed()}")
                if page_title:
                    raw_date_info = page_title.strip()
                    catalog_title_for_file = self._sanitize_filename(raw_date_info) # Sanitize for filename
                    self.logger.info(f"Extracted raw_date_info from page title: '{raw_date_info}'")
                else:
                    self.logger.warning("Page title was empty, cannot extract date info from it.")
            except Exception as e:
                self.logger.error(f"Error extracting page title for date info: {e}")
        else:
            self.logger.warning("Configuration doesn't specify extracting date from title for Meny. Date info might be missing.")
        
        # The user-facing title can be the raw_date_info or a more generic one
        display_title = raw_date_info if raw_date_info != "Meny_Unknown_Date" else f"{self.store_name} Catalog"


        # 4. Locate and Click PDF Download Button
        pdf_download_selectors = self.config.get('selectors', {}).get('pdf_download_button_selectors', [])
        if not pdf_download_selectors:
            self.logger.error("No PDF download button selectors configured for Meny.")
            return []

        pdf_url = await self._attempt_download_click(self.page, pdf_download_selectors,
                                                     timeout_ms=self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 20000))

        if not pdf_url:
            self.logger.error("Failed to capture PDF URL for Meny.")
            return []

        # 5. Download the PDF via HTTP
        base_filename = catalog_title_for_file if catalog_title_for_file != "Meny_Catalog" else self._sanitize_filename(Path(pdf_url).name.split('?')[0] or "meny_catalog")
        if not base_filename.lower().endswith('.pdf'):
            base_filename += '.pdf'
        final_pdf_filename = base_filename
        download_dir = self._ensure_download_dir()
        local_pdf_path = download_dir / final_pdf_filename

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(pdf_url) as resp:
                    if resp.status != 200:
                        raise ValueError(f"Downloading PDF failed with status {resp.status}")
                    data = await resp.read()
                    local_pdf_path.write_bytes(data)
            self.logger.info(f"Successfully saved Meny PDF to: {local_pdf_path}")

            # 6. Collect catalog information
            catalog_data = {
                "store_name": self.store_name,
                "title": display_title, # User-facing title
                "raw_date_info": raw_date_info, # For date parsing by catalog_processor
                "pdf_url": pdf_url,
                "local_path": str(local_pdf_path) # Convert Path object to string for JSON serialization
            }

            # Upload to cloud storage
            cloud_path = self.upload_pdf_to_cloud(str(local_pdf_path), catalog_data)
            if cloud_path:
                catalog_data["cloud_pdf_path"] = cloud_path
                self.logger.info(f"Meny: PDF uploaded to cloud storage: {cloud_path}")
            else:
                self.logger.warning(f"Meny: Failed to upload PDF to cloud storage")

            self.collected_catalogs.append(catalog_data)

            # Clean up local file after cloud upload
            try:
                local_pdf_path.unlink()
                self.logger.debug(f"Meny: Cleaned up local PDF: {local_pdf_path}")
            except Exception as cleanup_error:
                self.logger.warning(f"Meny: Failed to clean up local PDF: {cleanup_error}")
            
        except Exception as e:
            self.logger.error(f"Error saving downloaded PDF for Meny: {e}", exc_info=True)
            # If save fails, don't add to collected_catalogs

        return self.collected_catalogs