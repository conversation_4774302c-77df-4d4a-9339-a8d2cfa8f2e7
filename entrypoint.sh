#!/bin/sh

# This script acts as a router for the Docker container's commands.
# It allows us to use a single Dockerfile for multiple services
# with different startup commands on Render.

# Exit immediately if a command exits with a non-zero status.
set -e

# The first argument ($1) determines which process to run.
case "$1" in
  web)
    echo "Starting web service..."
    # Start the FastAPI server
    exec uvicorn api:app --host 0.0.0.0 --port ${PORT:-10000}
    ;;
  catalog_processor)
    echo "Running Catalog Processor..."
    exec python catalog_processor.py
    ;;
  parser)
    echo "Parser functionality now integrated into web service..."
    exec uvicorn api:app --host 0.0.0.0 --port ${PORT:-10000}
    ;;
  *)
    # If no command is matched, execute the arguments as a command.
    # This allows for running arbitrary commands for debugging.
    exec "$@"
    ;;
esac
