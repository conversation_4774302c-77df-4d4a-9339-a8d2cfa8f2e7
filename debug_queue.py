#!/usr/bin/env python3
"""
Debug script to check what's in the CatalogProcessingQueue
"""

import os
from dotenv import load_dotenv
load_dotenv()

import sys
from pathlib import Path

# Ensure project root is in path for imports
project_root = Path(__file__).resolve().parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from database import SessionLocal, CatalogProcessingQueue, ProcessedCatalogHashes
from sqlalchemy import func

def debug_queue():
    """Check what's in the processing queue"""
    print("🔍 DEBUGGING CATALOG PROCESSING QUEUE")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Check total queue entries
        total_entries = db.query(CatalogProcessingQueue).count()
        print(f"📊 Total queue entries: {total_entries}")
        
        if total_entries == 0:
            print("❌ Queue is completely empty!")
            return
        
        # Check by status
        print("\n📋 Queue entries by status:")
        status_counts = db.query(
            CatalogProcessingQueue.status,
            func.count(CatalogProcessingQueue.id)
        ).group_by(CatalogProcessingQueue.status).all()
        
        for status, count in status_counts:
            print(f"   {status}: {count} entries")
        
        # Show recent entries
        print("\n📝 Recent queue entries (last 10):")
        recent_entries = db.query(CatalogProcessingQueue).order_by(
            CatalogProcessingQueue.added_at.desc()
        ).limit(10).all()
        
        for entry in recent_entries:
            print(f"   ID: {entry.id}")
            print(f"   Status: {entry.status}")
            print(f"   PDF Path: {entry.pdf_path_to_process}")
            print(f"   Hash: {entry.pdf_content_hash[:12]}...")
            print(f"   Added: {entry.added_at}")
            print(f"   Retry Count: {entry.retry_count}")
            print("   ---")
        
        # Check PENDING specifically
        pending_entries = db.query(CatalogProcessingQueue).filter(
            CatalogProcessingQueue.status == 'PENDING'
        ).all()
        
        print(f"\n🎯 PENDING entries: {len(pending_entries)}")
        for entry in pending_entries:
            print(f"   ID: {entry.id}, PDF: {entry.pdf_path_to_process}")
        
        # Check PENDING_RETRY specifically
        pending_retry_entries = db.query(CatalogProcessingQueue).filter(
            CatalogProcessingQueue.status == 'PENDING_RETRY'
        ).all()
        
        print(f"\n🔄 PENDING_RETRY entries: {len(pending_retry_entries)}")
        for entry in pending_retry_entries:
            print(f"   ID: {entry.id}, PDF: {entry.pdf_path_to_process}")
        
        # Check ProcessedCatalogHashes
        print(f"\n📋 ProcessedCatalogHashes entries:")
        hash_counts = db.query(
            ProcessedCatalogHashes.status,
            func.count(ProcessedCatalogHashes.id)
        ).group_by(ProcessedCatalogHashes.status).all()
        
        for status, count in hash_counts:
            print(f"   {status}: {count} entries")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    debug_queue()
