import OpengraphImage from 'components/opengraph-image';
import { getStores } from 'lib/backend';

export default async function Image({
  params
}: {
  params: Promise<{ collection: string }>;
}) {
  // Try to find the store by name
  const { collection } = await params;
  const stores = await getStores();
  const store = stores.find(s => s.name.toLowerCase() === collection.toLowerCase());
  const title = store?.name || `Search: ${collection}`;

  return await OpengraphImage({ title });
}
