"""Add is_latest_for_today to Catalog

Revision ID: 3562757e2767
Revises: 2bb2b9ed0575
Create Date: 2025-06-06 16:16:35.624461

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3562757e2767'
down_revision: Union[str, None] = '2bb2b9ed0575'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('catalogs', sa.Column('is_latest_for_today', sa.<PERSON>(), nullable=False, server_default=sa.text('false')))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('catalogs', 'is_latest_for_today')
    # ### end Alembic commands ###
