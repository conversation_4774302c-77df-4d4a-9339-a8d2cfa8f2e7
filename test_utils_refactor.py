#!/usr/bin/env python3
"""
Test script to compare the current utils.py with the suggested utilsSUGGESTED.py refactor.
This will run both implementations on the same test cases and compare results.
"""

import sys
import json
from typing import Dict, Any, List

# Import current implementation
from utils import parse_and_calculate_unit_price as current_func

# Import suggested implementation
from utilsSUGGESTED import process_unit_data_for_product as suggested_func

# Test cases from the logs and common Danish supermarket units
TEST_CASES = [
    # From the parser logs (these were failing)
    ("144 g / 6 stk.", 32.00),
    ("750 ml.", 15.00),
    ("125 g. 10 stk.", 25.00),
    ("175 g.", 12.00),
    ("900 ml.", 18.00),
    ("330-720 ml.", 20.00),
    ("stk.", 10.00),
    ("g", 5.00),
    ("42 stk.", 30.00),
    ("200-300 ml", 15.00),
    ("150-350 stk.", 25.00),
    ("500-750 ml", 22.00),
    ("40-70 stk.", 18.00),
    
    # Common cases that should work
    ("500g", 20.00),
    ("1 kg", 35.00),
    ("1.5 l", 12.00),
    ("2x100g", 15.00),
    ("3 x 75 cl", 25.00),
    ("pakke", 8.00),
    
    # Edge cases
    ("", 10.00),
    (None, 10.00),
    ("invalid unit", 10.00),
]

def compare_results(current_result: Dict[str, Any], suggested_result: Dict[str, Any]) -> Dict[str, Any]:
    """Compare the results from both implementations."""
    comparison = {
        "identical": True,
        "differences": [],
        "current": current_result,
        "suggested": suggested_result,
        "improvement_type": "none"
    }

    # Check if current implementation failed completely
    current_failed = (
        current_result.get("error") or
        (current_result.get("unit_type") is None and current_result.get("quantity") is None)
    )

    # Check if suggested implementation succeeded where current failed
    suggested_succeeded = (
        not suggested_result.get("error") and
        (suggested_result.get("unit_type") is not None or suggested_result.get("quantity") is not None)
    )

    # Check key fields that should be comparable
    key_fields = [
        "quantity", "unit_type", "price_per_base_unit",
        "base_unit_type", "price_per_base_unit_min", "price_per_base_unit_max"
    ]

    for field in key_fields:
        current_val = current_result.get(field)
        suggested_val = suggested_result.get(field)

        if current_val != suggested_val:
            comparison["identical"] = False
            comparison["differences"].append({
                "field": field,
                "current": current_val,
                "suggested": suggested_val
            })

    # Determine improvement type
    if comparison["identical"]:
        comparison["improvement_type"] = "identical"
    elif current_failed and suggested_succeeded:
        comparison["improvement_type"] = "major_improvement"
    elif not current_failed and not suggested_succeeded:
        comparison["improvement_type"] = "regression"
    elif suggested_result.get("price_per_base_unit") and not current_result.get("price_per_base_unit"):
        comparison["improvement_type"] = "price_calculation_improvement"
    elif suggested_result.get("unit_type") and not current_result.get("unit_type"):
        comparison["improvement_type"] = "unit_parsing_improvement"
    else:
        comparison["improvement_type"] = "different"

    return comparison

def run_test():
    """Run the comparison test."""
    print("🧪 Testing utils.py refactor comparison")
    print("=" * 60)
    
    results = []
    identical_count = 0
    major_improvements = 0
    price_improvements = 0
    unit_improvements = 0
    regressions = 0
    different_count = 0

    for unit_string, price in TEST_CASES:
        print(f"\n📝 Testing: '{unit_string}' with price {price}")

        try:
            # Run current implementation
            current_result = current_func(unit_string, price)
        except Exception as e:
            current_result = {"error": str(e)}
            print(f"❌ Current implementation failed: {e}")

        try:
            # Run suggested implementation
            suggested_result = suggested_func(unit_string, price)
        except Exception as e:
            suggested_result = {"error": str(e)}
            print(f"❌ Suggested implementation failed: {e}")

        # Compare results
        comparison = compare_results(current_result, suggested_result)
        improvement_type = comparison["improvement_type"]

        if improvement_type == "identical":
            print("✅ Results identical")
            identical_count += 1
        elif improvement_type == "major_improvement":
            print("🚀 MAJOR IMPROVEMENT: Suggested handles this case, current fails completely!")
            major_improvements += 1
        elif improvement_type == "price_calculation_improvement":
            print("💰 PRICE IMPROVEMENT: Suggested calculates price per unit, current doesn't!")
            price_improvements += 1
        elif improvement_type == "unit_parsing_improvement":
            print("📏 UNIT IMPROVEMENT: Suggested preserves unit info, current loses it!")
            unit_improvements += 1
        elif improvement_type == "regression":
            print("⚠️ REGRESSION: Current works, suggested fails!")
            regressions += 1
        else:
            print("🔄 Results differ:")
            for diff in comparison["differences"]:
                print(f"   {diff['field']}: {diff['current']} → {diff['suggested']}")
            different_count += 1

        results.append({
            "input": {"unit_string": unit_string, "price": price},
            "comparison": comparison
        })
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DETAILED SUMMARY:")
    print(f"Total test cases: {len(TEST_CASES)}")
    print(f"✅ Identical results: {identical_count}")
    print(f"🚀 Major improvements (current fails, suggested works): {major_improvements}")
    print(f"💰 Price calculation improvements: {price_improvements}")
    print(f"📏 Unit parsing improvements: {unit_improvements}")
    print(f"⚠️ Regressions (current works, suggested fails): {regressions}")
    print(f"🔄 Other differences: {different_count}")

    total_improvements = major_improvements + price_improvements + unit_improvements
    print(f"\n🎯 TOTAL IMPROVEMENTS: {total_improvements}")

    # Recommendation
    if regressions == 0 and total_improvements > 0:
        print("\n✅ RECOMMENDATION: The suggested refactor is a clear improvement!")
        print(f"   - Handles {major_improvements} cases that currently fail completely")
        print(f"   - Adds {price_improvements} price calculations that are missing")
        print(f"   - Preserves {unit_improvements} unit information that is currently lost")
        print("   - No regressions detected")
    elif regressions > 0:
        print(f"\n⚠️  RECOMMENDATION: Review carefully - {regressions} regressions detected!")
    elif total_improvements == 0:
        print("\n🤷 RECOMMENDATION: No clear improvements, but no regressions either.")
    else:
        print("\n🔍 RECOMMENDATION: Mixed results - review individual cases.")
    
    return results

if __name__ == "__main__":
    try:
        results = run_test()
        
        # Save detailed results to file for review
        with open("utils_refactor_test_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n💾 Detailed results saved to: utils_refactor_test_results.json")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
