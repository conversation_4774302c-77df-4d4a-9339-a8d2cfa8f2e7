import os
import logging
from pathlib import Path
from dotenv import load_dotenv
from pathlib import Path
# --- NEW Imports ---
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
# Avoid circular import by importing database components only when needed

# --- Environment Loading ---
# This is the single source of truth for loading the .env file.
# All other scripts should import 'config' at the top to ensure the environment is loaded before any other operations.
def load_environment():
    # Construct an absolute path to the .env file based on this file's location (project_root/config.py)
    project_root = Path(__file__).resolve().parent
    dotenv_path = project_root / '.env'

    if dotenv_path.exists():
        load_dotenv(dotenv_path=dotenv_path)
        print(f"INFO: Loaded environment from {dotenv_path}")
    else:
        # Fallback for other structures, though not ideal
        load_dotenv()
        print("INFO: .env file not found at project root, using default dotenv search.")

load_environment()

# --- Database Configuration ---
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    # This provides a clear error message if the .env file is missing the URL.
    # It's better to fail fast than to have obscure errors later.
    # We print this error directly because logging might not be set up yet.
    print("CRITICAL ERROR: DATABASE_URL is not set in the environment. Please check your .env file.")
    # Exit gracefully if the database URL is not set.
    exit(1)

# Determine base directory (assuming config.py is in the root)
BASE_DIR = Path(__file__).resolve().parent

# --- Optimized Logging Configuration (Token Efficient) ---
def setup_logging():
    log_file_path = BASE_DIR / 'avis_scanner.log'
    # Configure the root logger with optimized format
    logging.basicConfig(
        level=logging.DEBUG,  # Set to DEBUG to capture all levels of messages
        format='%(asctime)s - %(name)-22s - %(levelname)-8s - %(message)s',
        datefmt='%d-%m %H:%M:%S',  # Optimized: dd-mm hh:mm:ss format saves tokens
        handlers=[
            logging.FileHandler(log_file_path, mode='w', encoding='utf-8'), # Log to a file, overwrite on each run
            logging.StreamHandler() # Also log to console
        ]
    )
    # Get a logger for this specific module
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured. All logs will be written to {log_file_path}")
    return logger

# Set up logging and get the logger for the config module
logger = setup_logging()

# Updated: Use current working directory as base, assuming script runs from project root
# BASE_DIR = Path(os.getcwd())

# Determine base directory (assuming config.py is in the root)
BASE_DIR = Path(__file__).resolve().parent
# Updated: Use current working directory as base, assuming script runs from project root
# BASE_DIR = Path(os.getcwd())

# --- Cloud Storage Configuration (Replaces Local/Render Paths) ---
# All file storage now uses Google Cloud Storage exclusively
# No more local vs Render path logic needed

# Cloud storage paths (virtual paths, not local directories)
CLOUD_CATALOG_PREFIX = "catalogs/"
CLOUD_IMAGE_PREFIX = "images/"
CLOUD_LOGO_PREFIX = "logos/"
CLOUD_STATIC_PREFIX = "static/"
CLOUD_TEMP_PREFIX = "temp/"

# Local directories only for templates (not for file storage)
STATIC_DIR = BASE_DIR / 'static'  # For local development serving only
TEMPLATE_DIR = BASE_DIR / 'templates'

# Initialize modern storage abstraction
def init_storage():
    """Initialize storage abstraction layer"""
    try:
        from storage_factory import get_storage, StorageFactory
        storage = get_storage()
        storage_info = StorageFactory.get_storage_info()
        logger.info(f"✅ Storage initialized: {storage_info['type']}")
        return True
    except ImportError as e:
        logger.error(f"❌ Storage abstraction module not found: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Storage initialization failed: {e}")
        return False

# Legacy cloud storage initialization (deprecated)
def init_cloud_storage():
    """Deprecated: Use init_storage() instead"""
    logger.warning("init_cloud_storage() is deprecated, use init_storage() instead")
    return init_storage()

# Remove old directory creation function - no longer needed
# All file operations now use cloud storage

# --- NEW: Database-First Configuration System ---

# DEPRECATED: Global APP_CONFIG dictionary - DO NOT USE
# This will be removed to prevent stale configuration usage
APP_CONFIG: Dict[str, Any] = {}

def get_setting_from_db(key: str, default: Optional[str] = None) -> Optional[str]:
    """
    Get a setting value directly from the database - ALWAYS FRESH.
    This is the new single source of truth for all configuration.

    Args:
        key: Setting key to retrieve
        default: Default value if setting not found

    Returns:
        Setting value from database or default
    """
    from database import SessionLocal, AppSettings

    db = SessionLocal()
    try:
        setting = db.query(AppSettings).filter(AppSettings.key == key).first()
        return str(setting.value) if setting else default
    finally:
        db.close()

def get_int_setting_from_db(key: str, default: int = 0) -> int:
    """Get an integer setting from database with type conversion."""
    value = get_setting_from_db(key, str(default))
    if value is None:
        return default
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def get_bool_setting_from_db(key: str, default: bool = False) -> bool:
    """Get a boolean setting from database with type conversion."""
    value = get_setting_from_db(key, str(default).lower())
    if value is None:
        return default
    return str(value).lower() in ['true', '1', 'yes', 'on']

def get_float_setting_from_db(key: str, default: float = 0.0) -> float:
    """Get a float setting from database with type conversion."""
    value = get_setting_from_db(key, str(default))
    if value is None:
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

# Minimal fallback defaults - only used if database is completely unavailable
EMERGENCY_FALLBACK_SETTINGS = {
    'GEMINI_PARSING_MODEL': 'models/gemini-1.5-flash-latest',
    'GEMINI_QUERY_MODEL': 'models/gemini-1.5-flash-latest',
    'GEMINI_API_DELAY_SECONDS': '2',
    'PARSER_MAX_RETRIES': '3',
}


# DEPRECATED FUNCTIONS - DO NOT USE
# These functions are kept temporarily for backward compatibility
# but will be removed once all modules are updated

def reload_app_config(db: Session):
    """DEPRECATED: Configuration is now fetched directly from database."""
    logger.warning("reload_app_config() is deprecated - configuration is now fetched directly from database")
    pass

def init_app_config():
    """DEPRECATED: Configuration is now fetched directly from database."""
    logger.warning("init_app_config() is deprecated - configuration is now fetched directly from database")
    pass

# --- End Centralized App Configuration ---

# Log final configuration
logger.info(f"Config: Cloud catalog prefix: {CLOUD_CATALOG_PREFIX}")
logger.info(f"Config: Cloud image prefix: {CLOUD_IMAGE_PREFIX}")
logger.info(f"Config: Cloud logo prefix: {CLOUD_LOGO_PREFIX}")
logger.info(f"Config: Static directory set to: {STATIC_DIR}")
logger.info(f"Config: Template directory set to: {TEMPLATE_DIR}")

# Initialize cloud storage on module load
init_storage()