#!/usr/bin/env python3
"""
Direct script to make the Google Cloud Storage bucket publicly readable.
This bypasses the API and runs the IAM policy change directly.
"""

import logging
import requests

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def make_bucket_public_via_api():
    """Call the deployed API to make bucket public"""
    
    cron_secret = "ycf/TpseOMw6N4rlBkRyEO2OeMFMoYwftn+CZQ0sLaU="
    server_url = "https://avisscanner.onrender.com"
    
    logger.info("🚀 Calling deployed API to make bucket public...")
    
    try:
        response = requests.post(
            f"{server_url}/internal/make_bucket_public",
            headers={
                "Content-Type": "application/json",
                "x-cron-secret": cron_secret
            },
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            logger.info("✅ SUCCESS!")
            logger.info(f"Status: {data.get('status')}")
            logger.info(f"Message: {data.get('message')}")
            logger.info(f"Details: {data.get('details')}")
            return True
        else:
            logger.error(f"❌ API Error: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to call API: {e}")
        return False

def test_bucket_public():
    """Test if files are now publicly accessible"""
    logger.info("🧪 Testing public access...")
    
    test_urls = [
        "https://storage.googleapis.com/tilbudsjaegeren/logos/supermarkets/meny.png",
        "https://storage.googleapis.com/tilbudsjaegeren/logos/supermarkets/netto.svg",
        "https://storage.googleapis.com/tilbudsjaegeren/images/catalog_42/page_1.png"
    ]
    
    all_good = True
    for url in test_urls:
        try:
            response = requests.head(url, timeout=10)
            if response.status_code == 200:
                logger.info(f"✅ {url}")
            else:
                logger.warning(f"❌ {url} - Status: {response.status_code}")
                all_good = False
        except Exception as e:
            logger.error(f"❌ {url} - Error: {e}")
            all_good = False
    
    return all_good

if __name__ == "__main__":
    logger.info("🚀 Making Google Cloud Storage bucket publicly readable...")
    
    # Make bucket public
    if make_bucket_public_via_api():
        logger.info("✅ Bucket made public successfully!")
        
        # Test access
        if test_bucket_public():
            logger.info("🎉 ALL DONE! Catalog images and logos are now publicly accessible!")
        else:
            logger.warning("⚠️ Some files may still not be accessible. Check the URLs above.")
    else:
        logger.error("❌ Failed to make bucket public. Check the API logs.")
