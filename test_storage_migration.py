#!/usr/bin/env python3
"""
Test script to validate the storage abstraction migration
Tests both local and cloud storage backends
"""

import os
import sys
import tempfile
import logging
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_storage_factory():
    """Test the storage factory and backend creation"""
    logger.info("🧪 Testing Storage Factory...")
    
    try:
        from storage_factory import StorageFactory, get_storage, StoragePaths
        
        # Test factory info
        info = StorageFactory.get_storage_info()
        logger.info(f"✅ Storage info: {info}")
        
        # Test storage creation
        storage = get_storage()
        logger.info(f"✅ Storage backend created: {type(storage).__name__}")
        
        # Test path helpers
        catalog_path = StoragePaths.catalog_path("test.pdf")
        image_path = StoragePaths.image_path("test.png")
        logger.info(f"✅ Path helpers work: catalog={catalog_path}, image={image_path}")
        
        return storage
        
    except Exception as e:
        logger.error(f"❌ Storage factory test failed: {e}")
        return None

def test_storage_operations(storage):
    """Test basic storage operations"""
    logger.info("🧪 Testing Storage Operations...")
    
    try:
        # Create a temporary test file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
            temp_file.write("This is a test file for storage migration validation")
            temp_file_path = temp_file.name
        
        test_cloud_path = "test/migration_test.txt"
        
        # Test upload
        logger.info(f"📤 Testing upload: {temp_file_path} -> {test_cloud_path}")
        upload_success = storage.upload_file(temp_file_path, test_cloud_path)
        if upload_success:
            logger.info("✅ Upload successful")
        else:
            logger.error("❌ Upload failed")
            return False
        
        # Test file exists
        logger.info(f"🔍 Testing file exists: {test_cloud_path}")
        exists = storage.file_exists(test_cloud_path)
        if exists:
            logger.info("✅ File exists check successful")
        else:
            logger.error("❌ File exists check failed")
            return False
        
        # Test file size
        logger.info(f"📏 Testing file size: {test_cloud_path}")
        size = storage.get_file_size(test_cloud_path)
        if size and size > 0:
            logger.info(f"✅ File size check successful: {size} bytes")
        else:
            logger.error("❌ File size check failed")
        
        # Test download
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as download_file:
            download_path = download_file.name
        
        logger.info(f"📥 Testing download: {test_cloud_path} -> {download_path}")
        download_success = storage.download_file(test_cloud_path, download_path)
        if download_success:
            logger.info("✅ Download successful")
            
            # Verify content
            with open(download_path, 'r') as f:
                content = f.read()
            if "test file for storage migration" in content:
                logger.info("✅ Downloaded content verified")
            else:
                logger.error("❌ Downloaded content verification failed")
        else:
            logger.error("❌ Download failed")
        
        # Test list files
        logger.info("📋 Testing list files")
        files = storage.list_files("test/")
        if test_cloud_path in files:
            logger.info(f"✅ List files successful: found {len(files)} files")
        else:
            logger.error("❌ List files failed")
        
        # Test delete
        logger.info(f"🗑️ Testing delete: {test_cloud_path}")
        delete_success = storage.delete_file(test_cloud_path)
        if delete_success:
            logger.info("✅ Delete successful")
        else:
            logger.error("❌ Delete failed")
        
        # Cleanup temp files
        try:
            os.unlink(temp_file_path)
            os.unlink(download_path)
        except:
            pass
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Storage operations test failed: {e}")
        return False

def test_legacy_compatibility():
    """Test that legacy imports still work"""
    logger.info("🧪 Testing Legacy Compatibility...")
    
    try:
        # Test config imports
        import config
        logger.info("✅ Config import successful")
        
        # Test that legacy constants exist (even if deprecated)
        if hasattr(config, 'CLOUD_CATALOG_PREFIX'):
            logger.info("✅ Legacy CLOUD_CATALOG_PREFIX exists")
        else:
            logger.warning("⚠️ Legacy CLOUD_CATALOG_PREFIX missing")
        
        # Test storage initialization
        if hasattr(config, 'init_storage'):
            init_result = config.init_storage()
            if init_result:
                logger.info("✅ Storage initialization successful")
            else:
                logger.error("❌ Storage initialization failed")
        else:
            logger.error("❌ init_storage function missing")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Legacy compatibility test failed: {e}")
        return False

def test_environment_detection():
    """Test environment-based storage selection"""
    logger.info("🧪 Testing Environment Detection...")
    
    try:
        from storage_factory import StorageFactory
        
        # Test current environment
        should_use_cloud = StorageFactory._should_use_cloud_storage()
        logger.info(f"✅ Environment detection: use_cloud={should_use_cloud}")
        
        # Test with different environment variables
        original_env = os.environ.copy()
        
        # Test explicit cloud storage
        os.environ['USE_CLOUD_STORAGE'] = 'true'
        StorageFactory.reset_storage()
        should_use_cloud = StorageFactory._should_use_cloud_storage()
        logger.info(f"✅ Explicit cloud storage: {should_use_cloud}")
        
        # Test explicit local storage
        os.environ['USE_CLOUD_STORAGE'] = 'false'
        StorageFactory.reset_storage()
        should_use_cloud = StorageFactory._should_use_cloud_storage()
        logger.info(f"✅ Explicit local storage: {should_use_cloud}")
        
        # Restore original environment
        os.environ.clear()
        os.environ.update(original_env)
        StorageFactory.reset_storage()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Environment detection test failed: {e}")
        return False

def main():
    """Run all storage migration tests"""
    logger.info("🚀 Starting Storage Migration Validation Tests")
    logger.info("=" * 60)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Storage Factory
    storage = test_storage_factory()
    if storage:
        tests_passed += 1
    
    # Test 2: Storage Operations (only if factory worked)
    if storage:
        if test_storage_operations(storage):
            tests_passed += 1
    
    # Test 3: Legacy Compatibility
    if test_legacy_compatibility():
        tests_passed += 1
    
    # Test 4: Environment Detection
    if test_environment_detection():
        tests_passed += 1
    
    # Results
    logger.info("=" * 60)
    logger.info(f"🏁 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        logger.info("🎉 All tests passed! Storage migration is working correctly.")
        return True
    else:
        logger.error(f"❌ {total_tests - tests_passed} tests failed. Storage migration needs fixes.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
