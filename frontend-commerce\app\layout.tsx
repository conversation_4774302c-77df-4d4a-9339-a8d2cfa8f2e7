// Navbar removed to match reference site
import { GeistSans } from 'geist/font/sans';
import { ReactNode } from 'react';
import { Toaster } from 'sonner';
import './globals.css';
import { baseUrl } from '@/lib/utils';
import RootLayoutWithAuth from '../components/auth/RootLayoutWithAuth';
import Header from '../components/layout/Header';
import { SelectedCatalogsProvider } from '@/lib/contexts/SelectedCatalogsContext';

const SITE_NAME = process.env.NEXT_PUBLIC_SITE_NAME || 'Tilbudsjægeren';

export const metadata = {
  metadataBase: new URL(baseUrl),
  title: {
    default: SITE_NAME,
    template: `%s | ${SITE_NAME}`
  },
  description: 'Find de bedste tilbud fra danske supermarkeder med AI assistance',
  robots: {
    follow: true,
    index: true
  }
};

export default async function RootLayout({
  children
}: {
  children: ReactNode;
}) {
  return (
    <html lang="da" className={GeistSans.variable}>
      <body className="bg-white text-gray-900 selection:bg-sky-200 min-h-screen">
        <RootLayoutWithAuth>
          <SelectedCatalogsProvider>
            <Header />
            <main className="max-w-6xl mx-auto px-4 pt-4 pb-8">
              {children}
            </main>
          </SelectedCatalogsProvider>
        </RootLayoutWithAuth>
        <Toaster closeButton toastOptions={{
          style: {
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
          }
        }} />
      </body>
    </html>
  );
}
