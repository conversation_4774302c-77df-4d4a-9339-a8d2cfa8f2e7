import bcrypt

# Use bcrypt for hashing passwords
# pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto") # Removed passlib

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verifies a plain password against a hashed password."""
    # Ensure both plain_password and hashed_password are encoded to bytes
    # The hashed_password from the database should already be a string representation
    # of bytes, or it might be bytes directly if the DB stores it that way.
    # For bcrypt.checkpw, the hashed_password needs to be bytes.
    # If hashed_password is a string (e.g., "$2b$...."), it needs to be encoded.
    return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password.encode('utf-8'))

def hash_password(password: str) -> str:
    """Hashes a plain password."""
    # Hash the password (encoded to bytes) using a generated salt
    hashed_bytes = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
    # Decode the hashed bytes back to a string to store in the database
    return hashed_bytes.decode('utf-8') 