import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from jose import JW<PERSON><PERSON><PERSON>, jwt
# from passlib.context import CryptContext # Removed as it's no longer used
from sqlalchemy.orm import Session
from sqlalchemy import func # Import func
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordBearer

import database
import schemas
from auth_utils import verify_password, hash_password
from database import get_db # Need get_db for get_current_user

# Load environment variables for JWT
# Make sure to set these in your .env file or environment!
SECRET_KEY = os.getenv("SECRET_KEY", "a-default-32-character-key-for-dev-only")
ALGORITHM = os.getenv("ALGORITHM", "HS256")

# Default value
DEFAULT_ACCESS_TOKEN_EXPIRE_MINUTES = 30
ACCESS_TOKEN_EXPIRE_MINUTES = DEFAULT_ACCESS_TOKEN_EXPIRE_MINUTES # Set default initially

# --- NEW: Configuration for Refresh Tokens ---
REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

try:
    # Try to get and convert the environment variable
    expire_minutes_str = os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES")
    if expire_minutes_str is not None:
        # Attempt conversion only if the variable is set
        ACCESS_TOKEN_EXPIRE_MINUTES = int(expire_minutes_str.split('#')[0].strip()) # Attempt conversion, removing potential comments
except ValueError:
    # If conversion fails (e.g., invalid format), keep the default
    print(f"Warning: Invalid value for ACCESS_TOKEN_EXPIRE_MINUTES. Using default: {DEFAULT_ACCESS_TOKEN_EXPIRE_MINUTES} minutes.")
    ACCESS_TOKEN_EXPIRE_MINUTES = DEFAULT_ACCESS_TOKEN_EXPIRE_MINUTES

# Log information about JWT configuration
print(f"JWT Authentication configured with ALGORITHM: {ALGORITHM}, TOKEN_EXPIRE_MINUTES: {ACCESS_TOKEN_EXPIRE_MINUTES}")
print(f"SECRET_KEY is {'set' if SECRET_KEY else 'NOT SET'}")

if not SECRET_KEY or SECRET_KEY == "a-default-32-character-key-for-dev-only":
    print("WARNING: Using default SECRET_KEY. This is insecure and should only be used in development.")

# --- User Retrieval ---

def get_user(db: Session, username: str) -> Optional[database.User]:
    """Retrieves a user from the database by username."""
    # Perform a case-insensitive query
    return db.query(database.User).filter(func.lower(database.User.username) == func.lower(username)).first()

# --- User Creation ---

def create_user(db: Session, user: schemas.UserCreate) -> database.User:
    """Creates a new user in the database."""
    hashed_pw = hash_password(user.password)
    db_user = database.User(username=user.username, hashed_password=hashed_pw)
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

# --- Authentication ---

def authenticate_user(db: Session, username: str, password: str) -> Optional[database.User]:
    """Authenticates a user by checking username and password."""
    user = get_user(db, username)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

# --- JWT Token Creation ---

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Creates a JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire, "scope": "access_token"}) # Add scope
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# --- NEW: Function to create a refresh token ---
def create_refresh_token(data: dict) -> str:
    """Creates a JWT refresh token."""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "scope": "refresh_token"}) # Add scope
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# --- NEW: Function to verify a refresh token ---
def verify_refresh_token(token: str, db: Session) -> Optional[database.User]:
    """Verifies a refresh token and returns the user if valid."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate refresh token",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        if payload.get("scope") != "refresh_token":
            raise credentials_exception
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = get_user(db, username=username)
    if user is None:
        raise credentials_exception
    return user

# Moved from api.py
# --- Helper function for getting current user ---
# Modify get_current_user to accept Request and check cookie
async def get_current_user(
    request: Request, # Add Request dependency
    token_from_header: Optional[str] = Depends(OAuth2PasswordBearer(tokenUrl="/token", auto_error=False)), # Make header optional
    db: Session = Depends(get_db)
) -> schemas.User:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    token = token_from_header # Prioritize header

    if token is None:
        # Fallback to cookie
        token = request.cookies.get("access_token_cookie")

    if token is None:
        # No token found in header or cookie
        raise credentials_exception

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = schemas.TokenData(username=username)
    except JWTError:
        raise credentials_exception

    user = get_user(db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

# --- Optional Helper function for getting current user ---
async def try_get_current_user(
    request: Request,
    token_from_header: str = Depends(OAuth2PasswordBearer(tokenUrl="/token", auto_error=False)),
    db: Session = Depends(get_db)
) -> Optional[schemas.User]:
    """
    Attempts to get the current user from token (from header OR cookie),
    returns None if no token or invalid.
    Prefers header token if both are present.
    """
    token = token_from_header # Prioritize token from Authorization header

    if token is None:
        # If no header token, try getting it from the cookie
        token = request.cookies.get("access_token_cookie") # Use the same cookie name as in /token endpoint

    if token is None:
        # No token found in header or cookie
        return None

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None # No username in payload
        # Optional: Could also check 'is_admin' from payload if needed directly
        token_data = schemas.TokenData(username=username)
    except JWTError:
        return None # Token invalid (expired, wrong signature, etc.)

    user = get_user(db, username=token_data.username)
    # If user not found in DB (e.g., deleted after token issuance), return None
    return user

# --- Admin Dependency ---

async def require_admin(current_user: schemas.User = Depends(get_current_user)) -> schemas.User:
    """Dependency that requires the current user to be an admin."""
    # Now depends on the get_current_user function defined above in this file
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Administrator privileges required for this operation.",
            headers={"WWW-Authenticate": "Bearer"}, # Optional, but can be informative
        )
    return current_user 