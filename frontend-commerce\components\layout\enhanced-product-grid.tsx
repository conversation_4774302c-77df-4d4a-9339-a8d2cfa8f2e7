'use client';

import React, { useState } from 'react';
import { BackendProduct } from '@/lib/backend';
import { buildThumbnailUrl } from '@/lib/ui-helpers';
import ImageModal from '@/components/ui/ImageModal';

interface EnhancedProductGridProps {
  products: BackendProduct[];
  title?: string;
  showCount?: boolean;
}

export default function EnhancedProductGrid({ 
  products, 
  title = "Products",
  showCount = true 
}: EnhancedProductGridProps) {
  const [selectedImage, setSelectedImage] = useState<{
    src: string;
    alt: string;
    title: string;
    subtitle: string;
  } | null>(null);

  const handleImageClick = (product: BackendProduct, imageSrc: string) => {
    setSelectedImage({
      src: imageSrc,
      alt: product.name,
      title: product.name,
      subtitle: `${product.price} kr${product.unit ? ` (${product.unit})` : ''}`
    });
  };

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg">No products found</div>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
          <svg className="w-6 h-6 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          {title}
          {showCount && (
            <span className="text-lg font-normal text-gray-500 dark:text-gray-400">
              ({products.length})
            </span>
          )}
        </h2>
      </div>

      {/* Enhanced Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product) => {
          const imageSrc = buildThumbnailUrl(product.image_path) || '/placeholder.png';
          
          return (
            <div 
              key={product.id} 
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-200 dark:border-gray-700 group hover:scale-[1.02]"
            >
              {/* Image Section */}
              <div 
                className="relative h-48 bg-gray-50 dark:bg-gray-900 cursor-pointer overflow-hidden"
                onClick={() => handleImageClick(product, imageSrc)}
                title="Click to view full size"
              >
                <img
                  src={imageSrc}
                  alt={product.name}
                  className="w-full h-full object-contain p-3 transition-transform duration-300 group-hover:scale-110"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/placeholder.png';
                  }}
                />
                
                {/* Hover overlay */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full p-2">
                    <svg className="w-5 h-5 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Content Section */}
              <div className="p-4 space-y-3">
                {/* Product Name */}
                <h3 className="font-semibold text-gray-900 dark:text-white text-sm leading-tight line-clamp-2" title={product.name}>
                  {product.name}
                </h3>

                {/* Price Section */}
                <div className="space-y-1">
                  <div className="flex items-baseline justify-between">
                    <span className="text-xl font-bold text-sky-600 dark:text-sky-400">
                      {product.price ? `${product.price.toFixed(2)} kr` : 'N/A'}
                    </span>
                    {product.original_price && product.original_price > product.price && (
                      <span className="text-sm text-gray-500 line-through">
                        {product.original_price.toFixed(2)} kr
                      </span>
                    )}
                  </div>
                  
                  {/* Unit and Price per unit */}
                  <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                    {product.unit && (
                      <div className="flex items-center gap-1">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                        </svg>
                        <span>{product.unit}</span>
                      </div>
                    )}
                    {product.price_per_base_unit && product.base_unit_type && (
                      <div className="flex items-center gap-1 text-green-600 dark:text-green-400 font-medium">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                        <span>{product.price_per_base_unit.toFixed(2)} kr/{product.base_unit_type}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Category and Brand */}
                <div className="flex flex-wrap gap-2 text-xs">
                  {product.category && (
                    <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full">
                      {product.category}
                    </span>
                  )}
                  {product.brand && (
                    <span className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full">
                      {product.brand}
                    </span>
                  )}
                </div>

                {/* Store Info */}
                <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>Store ID: {product.catalog_id}</span>
                    {product.id && (
                      <span>#{product.id}</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          isOpen={!!selectedImage}
          onClose={() => setSelectedImage(null)}
          imageSrc={selectedImage.src}
          imageAlt={selectedImage.alt}
          title={selectedImage.title}
          subtitle={selectedImage.subtitle}
        />
      )}
    </>
  );
}
