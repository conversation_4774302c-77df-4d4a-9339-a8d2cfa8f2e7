import Hero from 'components/landing/Hero';
import QuerySection from 'components/landing/QuerySection';
import CatalogList from 'components/landing/CatalogList';

export const metadata = {
  description: 'AvisScanner product search',
  openGraph: {
    type: 'website'
  }
};

export default function HomePage() {
  return (
    <main className="max-w-5xl mx-auto px-4 py-8 space-y-16">
      <Hero />
      <QuerySection />
      <CatalogList />
    </main>
  );
}
