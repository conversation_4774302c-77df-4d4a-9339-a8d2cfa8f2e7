# utils.py - V2 Refactor

import re
import logging
from typing import Tuple, Optional, Dict, Any

logger = logging.getLogger(__name__)

# --- New: Specialized, Simple Regexes ---
# Each regex is designed to do ONE thing well.
# 1. Handles ranges like "180-320 g" or "2-3 stk"
REGEX_RANGE = re.compile(
    r"""^\s*             # Optional leading space
    ([\d.,]+)         # Min quantity - Capture group 1
    \s*-\s*           # Hyphen separator
    ([\d.,]+)         # Max quantity - Capture group 2
    \s*               # Optional space
    ([a-zA-ZæøåÆØÅ]+)   # Unit - Capture group 3
    s?                 # Optional plural 's'
    \s*$              # End of string
    """,
    re.IGNORECASE | re.VERBOSE
)

# 2. Handles multipliers like "3x100g" or "2 x 75 cl"
REGEX_MULTIPACK = re.compile(
    r"""^\s*             # Optional leading space
    (\d+)             # Multiplier (e.g., "3") - Capture group 1
    \s*[xX]\s*        # 'x' separator
    ([\d.,]+)         # Quantity (e.g., "100") - Capture group 2
    \s*               # Optional space
    ([a-zA-Z<PERSON><PERSON><PERSON>ÆØÅ]+)   # Unit (e.g., "g") - Capture group 3
    s?                 # Optional plural 's'
    \s*$              # End of string
    """,
    re.IGNORECASE | re.VERBOSE
)

# 3. Handles simple cases like "500 g", "1.5 kg", "1 stk"
REGEX_SIMPLE = re.compile(
    r"""^\s*             # Optional leading space
    ([\d.,]+)         # Quantity - Capture group 1
    \s*               # Optional space
    ([a-zA-ZæøåÆØÅ]+)   # Unit - Capture group 2
    s?                 # Optional plural 's'
    \s*$              # End of string
    """,
    re.IGNORECASE | re.VERBOSE
)


# --- UNIT_CONVERSION map - complete version from current utils.py ---
UNIT_CONVERSION = {
    # Weight
    'g': ('kg', 0.001),
    'gram': ('kg', 0.001),
    'kg': ('kg', 1.0),
    'kilogram': ('kg', 1.0),
    # Volume
    'ml': ('l', 0.001),
    'milliliter': ('l', 0.001),
    'cl': ('l', 0.01),
    'centiliter': ('l', 0.01),
    'dl': ('l', 0.1),
    'deciliter': ('l', 0.1),
    'l': ('l', 1.0),
    'liter': ('l', 1.0),
    # Pieces/Packages
    'stk': ('stk', 1.0),
    'styk': ('stk', 1.0),
    'styks': ('stk', 1.0),
    'pk': ('pakke', 1.0),
    'pakke': ('pakke', 1.0),
    'ps': ('pose', 1.0),
    'pose': ('pose', 1.0),
    'rl': ('rulle', 1.0),
    'rulle': ('rulle', 1.0),
    'bk': ('bakke', 1.0),
    'bakke': ('bakke', 1.0),
    'ks': ('kasse', 1.0),
    'kasse': ('kasse', 1.0),
}


# --- New: The Orchestrator Function ---
def parse_unit_string(unit_string: Optional[str]) -> Optional[Dict[str, Any]]:
    """
    Attempts to parse a unit string using a waterfall of regexes.
    This function is now responsible ONLY for extracting values, not for price calculation.
    """
    if not unit_string:
        return None

    s = unit_string.strip()

    # --- Waterfall of Parsers ---
    # 1. Try to match a range first, as it's the most specific
    match = REGEX_RANGE.match(s)
    if match:
        min_q_str, max_q_str, unit_type = match.groups()
        try:
            return {
                "quantity_min": float(min_q_str.replace(',', '.')),
                "quantity_max": float(max_q_str.replace(',', '.')),
                "quantity": None, # A range doesn't have a single quantity
                "unit_type": unit_type.lower()
            }
        except ValueError:
            return None # Failed to convert numbers

    # 2. Try to match a multipack
    match = REGEX_MULTIPACK.match(s)
    if match:
        multiplier_str, quantity_str, unit_type = match.groups()
        try:
            total_quantity = int(multiplier_str) * float(quantity_str.replace(',', '.'))
            return {
                "quantity_min": total_quantity,
                "quantity_max": total_quantity,
                "quantity": total_quantity,
                "unit_type": unit_type.lower()
            }
        except ValueError:
            return None

    # 3. Try the simple case
    match = REGEX_SIMPLE.match(s)
    if match:
        quantity_str, unit_type = match.groups()
        try:
            quantity = float(quantity_str.replace(',', '.'))
            return {
                "quantity_min": quantity,
                "quantity_max": quantity,
                "quantity": quantity,
                "unit_type": unit_type.lower()
            }
        except ValueError:
            return None

    # 4. Fallback: If no regex matches, treat it as a unit-only string like "pakke"
    if s.isalpha() and s.lower() in UNIT_CONVERSION:
        return {
            "quantity_min": 1.0,
            "quantity_max": 1.0,
            "quantity": 1.0,
            "unit_type": s.lower()
        }

    # If all else fails, we couldn't parse it
    logger.debug(f"Could not parse complex unit string: '{unit_string}'")
    return None


def calculate_price_per_base_unit(parsed_unit: Optional[Dict[str, Any]], price: Optional[float]) -> Dict[str, Any]:
    """
    Calculates price per base unit based on PRE-PARSED unit data.
    This function is now simpler and only does the math.
    """
    result = {
        'price_per_base_unit': None,
        'price_per_base_unit_min': None,
        'price_per_base_unit_max': None,
        'base_unit_type': None
    }
    if not parsed_unit or not price or price <= 0:
        return result

    unit_type = parsed_unit.get('unit_type')
    if not unit_type or unit_type not in UNIT_CONVERSION:
        return result

    base_unit, factor = UNIT_CONVERSION[unit_type]
    result['base_unit_type'] = base_unit

    if base_unit not in ['kg', 'l']:
        return result # Can't calculate for 'stk', 'pakke' etc.

    # --- New: Handle ranges and single values cleanly ---
    q_min = parsed_unit.get("quantity_min")
    q_max = parsed_unit.get("quantity_max")

    try:
        if q_min is not None and q_min > 0:
            min_quantity_in_base = q_min * factor
            result['price_per_base_unit_max'] = round(price / min_quantity_in_base, 2)

        if q_max is not None and q_max > 0:
            max_quantity_in_base = q_max * factor
            result['price_per_base_unit_min'] = round(price / max_quantity_in_base, 2)
        
        # If min and max are the same, set the main price_per_base_unit
        if result['price_per_base_unit_min'] == result['price_per_base_unit_max']:
            result['price_per_base_unit'] = result['price_per_base_unit_min']

    except (ZeroDivisionError, TypeError):
        logger.warning(f"Could not calculate unit price for parsed unit {parsed_unit}")

    return result


# --- The Main Function to be called from the outside ---
def process_unit_data_for_product(unit_string: Optional[str], price: Optional[float]) -> Dict[str, Any]:
    """
    A single entry point that orchestrates parsing and calculation.
    Returns a single dictionary to update the product with.
    """
    parsed_unit = parse_unit_string(unit_string)

    if parsed_unit:
        price_calcs = calculate_price_per_base_unit(parsed_unit, price)
        # Combine the results into one flat dictionary for the database
        return {
            "quantity": parsed_unit.get("quantity"),
            "quantity_min": parsed_unit.get("quantity_min"),
            "quantity_max": parsed_unit.get("quantity_max"),
            "unit_type": parsed_unit.get("unit_type"),
            **price_calcs # Unpack the price calculation results
        }
    else:
        # Return a default structure for un-parsable units
        return {
            "quantity": None, "quantity_min": None, "quantity_max": None,
            "unit_type": unit_string, # Store the raw string as the unit type
            "price_per_base_unit": None, "price_per_base_unit_min": None,
            "price_per_base_unit_max": None, "base_unit_type": None
        }