#!/usr/bin/env python3

import sys
from datetime import datetime, date, timed<PERSON>ta

def parse_dagrofa_retail_week_title(title_str: str):
    """Test the Meny date parsing logic"""
    import re
    
    print(f"Input: '{title_str}'")
    
    # Normalize to lowercase
    title_str = title_str.lower()
    print(f"Normalized: '{title_str}'")
    
    # First try to match "uge 2925" format (week 29, year 25)
    match = re.search(r"(?:uge|ug)\s*(\d{2})(\d{2})(?:\D|$)", title_str)
    if match:
        print(f"Found 4-digit week+year pattern: week {match.group(1)}, year {match.group(2)}")
        week_str = match.group(1)
        year_str = match.group(2)
    else:
        # Fallback to original pattern for other formats
        match = re.search(r"(?:uge|ug)\s*(\d{1,2})[\s._-]*(\d{2}(\d{2})?)?", title_str)
        if not match:
            print("No week/year pattern found")
            return None
        week_str = match.group(1)
        year_str = match.group(2)
    
    print(f"Week string: '{week_str}', Year string: '{year_str}'")
    
    try:
        week_number = int(week_str)
        print(f"Week number: {week_number}")
        
        if not (1 <= week_number <= 53):
            print(f"Invalid week number {week_number}")
            return None

        # Handle year format
        if year_str:
            if len(year_str) == 4:
                year = int(year_str)
            elif len(year_str) == 2:
                year_val = int(year_str)
                # For 2-digit years, assume 20XX for values 00-50, 19XX for 51-99
                if year_val <= 50:
                    year = 2000 + year_val
                else:
                    year = 1900 + year_val
            else:
                print(f"Unexpected year format '{year_str}'")
                return None
        else:
            # If no year specified, assume current year
            year = datetime.now().year
            print(f"No year found, assuming current year: {year}")
        
        print(f"Parsed year: {year}")

        effective_year = year
        effective_week = week_number - 1
        print(f"Effective week calculation: {week_number} - 1 = {effective_week}")

        if effective_week == 0:
            effective_year -= 1
            # Determine the last week number of the previous year
            effective_week = date(effective_year, 12, 28).isocalendar()[1]
            print(f"Week 0 adjusted to week {effective_week} of {effective_year}")
        
        print(f"Final: Year {effective_year}, Week {effective_week}")
        
        # ISO week date: year, week, weekday (1=Monday, 7=Sunday)
        # We want Friday (5) of that effective_week as start_date
        start_date_obj = datetime.fromisocalendar(effective_year, effective_week, 5).date()
        end_date_obj = start_date_obj + timedelta(days=6)
        
        print(f"Date range: {start_date_obj} to {end_date_obj}")
        
        # Generate filename
        sanitized_store_name = "meny"
        new_filename = f"{sanitized_store_name}_{start_date_obj.strftime('%Y%m%d')}_{end_date_obj.strftime('%Y%m%d')}.pdf"
        cloud_path = f"catalogs/{new_filename}"
        print(f"Expected cloud path: {cloud_path}")
        
        return start_date_obj, end_date_obj

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # Test the problematic input
    test_input = "MENY uge 2925"
    result = parse_dagrofa_retail_week_title(test_input)
    print(f"\nResult: {result}")
