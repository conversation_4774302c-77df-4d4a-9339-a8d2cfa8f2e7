"use client";
import { useState, useEffect } from 'react';
import { getCatalogs, BackendCatalog } from '@/lib/backend';
import { getStoreLogoUrl } from '@/lib/ui-helpers';
import TestWrapper from './TestWrapper';

function TestCatalogListContent() {
  const [catalogs, setCatalogs] = useState<BackendCatalog[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCatalogs = async () => {
      try {
        const data = await getCatalogs();
        setCatalogs(data);
      } catch (err) {
        console.error('Failed to load catalogs:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCatalogs();
  }, []);

  if (loading) {
    return (
      <section className="space-y-4">
        <h2 className="text-2xl font-bold text-gray-900">Tilgængelige Kataloger</h2>
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-sky-500"></div>
        </div>
      </section>
    );
  }

  return (
    <section className="space-y-4">
      <h2 className="text-2xl font-bold text-gray-900">Tilgængelige Kataloger</h2>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {catalogs.map((catalog) => (
          <div 
            key={catalog.id}
            className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden border border-gray-200"
          >
            <div className="h-24 w-full overflow-hidden">
              <img
                src={getStoreLogoUrl(catalog.store_name) || '/placeholder.png'}
                alt={catalog.title}
                className="h-full w-full object-cover"
              />
            </div>
            <div className="p-3">
              <h3 className="font-semibold text-sm text-gray-900 truncate">
                {catalog.title}
              </h3>
              <p className="text-xs text-gray-600 mt-1">
                {catalog.store_name}
              </p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}

export default function TestCatalogList() {
  return (
    <TestWrapper
      fallbackTitle="Test Catalog List"
      fallbackMessage="The catalog list test component is disabled in production"
    >
      <TestCatalogListContent />
    </TestWrapper>
  );
}
