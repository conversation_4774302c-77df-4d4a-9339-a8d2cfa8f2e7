!
python catalog_processor.py

python -m scrapers.run_scraper fotex.json

# Background parsing now integrated into FastAPI (api.py)

python test/debug_scraper.py

npx ai-digest --whitespace-removal

SELECT * FROM public.catalog_processing_queue ORDER BY added_at DESC;

SELECT * FROM public.processed_catalog_hashes ORDER BY id DESC;

Clear hele databasen
TRUNCATE TABLE catalog_processing_queue RESTART IDENTITY;

 


Prompt:

Please read @readme.md for current information about the project. 
Use playwright to access https://avis-scanner.onrender.com/ with screenshot and snapshot. This is our reference site. 
Now take a look at our current work in progress frontend on localhost:3000 - also with screenshot and snapshot. 
Please make the new one have the exact functionality as the old one, as well as being waaaaay nicer looking.

When making functionality - dont reinvent the wheel. Use the same logic as the old site. everything you need is already done in /static/ for example, you just need to rewrite it to next.js and the new frontend.