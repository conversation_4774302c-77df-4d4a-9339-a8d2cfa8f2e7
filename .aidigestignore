
# File extensions
*.log
*.tmp
*.bak
*.swp
*.pdf
*.db
*.sqlite
*.sqlite3

# Build and distribution
build/
dist/
node_modules/

# Data directories
scrapers\downloads\
scrapers\debug\
catalogs\
aviser\
images\
local_storage\
logos\

# High Priority Token Savers (47.8 KB saved)
# Test and debug files
test/
*_test_results.json
comprehensive_utils_test_results.json
utils_refactor_test_results.json
test_*.py
debug_*.py

# Legacy/backup utils (25.6 KB saved)
utils_ENHANCED.py
utilsSUGGESTED.py
utils_BACKUP.py

# Migration and setup scripts (21.8 KB saved)
migrate_paths_to_cloud.py
setup_cloud_folders.py
update_scrapers_cloud.py
process_cloud_catalogs.py

# Static assets and templates (82.5 KB saved)
static/
templates/
frontend-commerce/styles/

# Medium Priority
# Alembic migration files
alembic/versions/
alembic/__pycache__/

# Cache and build artifacts
__pycache__/
.pytest_cache/
frontend-commerce/.vscode/
.cursor/

# Config and credentials
gen-lang-client-*.json
*.bat
*.sh

# Documentation (keep essential README.md and system_prompt.md)
Commands.md
Design guide.md
Default prompts.md
CLOUD_STORAGE_STRUCTURE.md
frontend-commerce/README.md
frontend-commerce/license.md

# Specific large files to exclude
manual_recording_*.zip
migration_backup_*.txt

# Temporary: Exclude frontend for backend-focused work
frontend-commerce/

# Temporary Contextual: Keep only scrapers currently being worked on
scrapers/store_scrapers/lidl_scraper.py
scrapers/store_scrapers/discount365_scraper.py
scrapers/store_scrapers/netto_scraper.py
scrapers/store_scrapers/superbrugsen_scraper.py
scrapers/store_scrapers/brugsen_scraper.py
scrapers/store_scrapers/rema1000_scraper.py
scrapers/store_scrapers/bilka_scraper.py
scrapers/store_scrapers/meny_scraper.py
# Keep fotex, spar, minkobmand as examples