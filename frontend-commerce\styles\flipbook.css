/* Flipbook Viewer Styles */

.catalog-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.flipbook {
  margin: 0 auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.page-wrapper {
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  overflow: hidden;
}

/* PDF.js canvas styling */
.react-pdf__Page {
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.react-pdf__Page__canvas {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Loading states */
.react-pdf__Document {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.react-pdf__message {
  padding: 20px;
  color: #666;
  font-size: 14px;
}

/* Zoom and pan container */
.react-transform-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.react-transform-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .flipbook {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  
  .page-wrapper {
    border-radius: 2px;
  }
}

/* Fullscreen mode adjustments */
.catalog-viewer:-webkit-full-screen {
  background: #000;
}

.catalog-viewer:-moz-full-screen {
  background: #000;
}

.catalog-viewer:fullscreen {
  background: #000;
}

/* Toolbar styling */
.catalog-viewer .toolbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Page flip animations */
.flipbook .stf__parent {
  perspective: 2000px;
}

.flipbook .stf__item {
  transform-style: preserve-3d;
  transition: transform 0.6s ease-in-out;
}

/* Custom scrollbar for mobile */
.flipbook::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.flipbook::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.flipbook::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.flipbook::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Loading spinner */
.catalog-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;
}

.catalog-loading .spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0ea5e9;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error state */
.catalog-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #dc2626;
  text-align: center;
  padding: 20px;
}

.catalog-error .error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* Keyboard shortcuts help */
.keyboard-help {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  line-height: 1.4;
  opacity: 0.7;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.keyboard-help:hover {
  opacity: 1;
}

.keyboard-help .help-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: #0ea5e9;
}

.keyboard-help .help-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.keyboard-help .help-key {
  font-family: monospace;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 3px;
  margin-left: 8px;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .catalog-viewer .toolbar {
    padding: 8px 12px;
  }
  
  .catalog-viewer .toolbar .toolbar-group {
    gap: 8px;
  }
  
  .catalog-viewer .toolbar button {
    padding: 8px;
  }
  
  .keyboard-help {
    display: none; /* Hide on mobile */
  }
  
  .flipbook {
    margin: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .react-pdf__Page__canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Print styles */
@media print {
  .catalog-viewer .toolbar,
  .keyboard-help {
    display: none !important;
  }
  
  .catalog-viewer {
    background: white !important;
  }
  
  .flipbook {
    box-shadow: none !important;
    margin: 0 !important;
  }
}
