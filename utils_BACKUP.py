import re
import logging
from typing import Tuple, Optional, Dict, Any

logger = logging.getLogger(__name__)

# Regex to capture quantity and unit, handling various formats
# - Optional leading space
# - Quantity: digits, decimals (.), commas (,) as thousands separators (optional)
# - Optional multiplier (e.g., '3 x ')
# - Unit: letters (like kg, g, l, ml, stk, pk, pakke), potentially with trailing 's'
# Handles: "500 g", "1.5 kg", "1,5 kg", "3 stk", "2x100g", "1 liter", "75 cl"
# Does NOT handle ranges like "500-600 g" - will likely take the first number.
UNIT_REGEX = re.compile(
    r"""^\s*             # Optional leading space
    (?:(\d+)\s*[xX]\s*)? # Optional multiplier (e.g., "3 x ") - Capture group 1
    ([\d.,]+)         # Quantity (digits, dots, commas) - Capture group 2
    \s*               # Optional space
    ([a-zA-ZæøåÆØÅ]+)   # Unit (letters, including Danish) - Capture group 3
    s?                 # Optional plural 's'
    \s*$              # Optional trailing space and end of string
    """, 
    re.IGNORECASE | re.VERBOSE
)

# Mapping of common units to base units (kg, l, stk) and conversion factors
UNIT_CONVERSION = {
    # Weight
    'g': ('kg', 0.001),
    'gram': ('kg', 0.001),
    'kg': ('kg', 1.0),
    'kilogram': ('kg', 1.0),
    # Volume
    'ml': ('l', 0.001),
    'milliliter': ('l', 0.001),
    'cl': ('l', 0.01),
    'centiliter': ('l', 0.01),
    'dl': ('l', 0.1),
    'deciliter': ('l', 0.1),
    'l': ('l', 1.0),
    'liter': ('l', 1.0),
    # Pieces/Packages
    'stk': ('stk', 1.0),
    'styk': ('stk', 1.0),
    'styks': ('stk', 1.0),
    'pk': ('pakke', 1.0), # Treat package as a base unit for now
    'pakke': ('pakke', 1.0),
    'ps': ('pose', 1.0), # Treat bag as a base unit
    'pose': ('pose', 1.0),
    'rl': ('rulle', 1.0), # Treat roll as a base unit
    'rulle': ('rulle', 1.0),
    'bk': ('bakke', 1.0), # Treat tray as a base unit
    'bakke': ('bakke', 1.0),
    'ks': ('kasse', 1.0), # Treat case as a base unit
    'kasse': ('kasse', 1.0),
    # Add more Danish/common abbreviations as needed
}

def parse_and_calculate_unit_price(unit_string: Optional[str], price: Optional[float]) -> Dict[str, Any]:
    """
    Parses a unit string (e.g., "500 g", "1.5 kg", "3 stk", "2x100g") and calculates
    the price per base unit (kg or liter) if applicable.

    Args:
        unit_string: The raw unit string extracted by the LLM.
        price: The product price.

    Returns:
        A dictionary containing:
        - 'quantity': Extracted numeric quantity (float, total quantity).
        - 'unit_type': Normalized unit type (str, e.g., "g", "kg", "l", "stk").
        - 'price_per_base_unit': Calculated price per kg or liter (float, optional).
        - 'base_unit_type': The base unit used for calculation ("kg" or "l", optional).
    """
    result = {
        'quantity': None,
        'unit_type': None,
        'price_per_base_unit': None,
        'base_unit_type': None
    }

    if not unit_string or price is None or price <= 0:
        return result

    match = UNIT_REGEX.match(unit_string.strip())
    if not match:
        # Could not parse with regex. Check if the string is a known unit itself (e.g., "pakke").
        unit_type_lower = unit_string.strip().lower()
        if unit_type_lower in UNIT_CONVERSION:
            result['quantity'] = 1.0
            result['unit_type'] = unit_type_lower
        elif unit_string.isalpha():
             # Fallback for unknown alphabetic strings
             result['unit_type'] = unit_string.lower()
        
        logger.debug(f"Could not parse unit string with regex: '{unit_string}'. Handled as unit-only string.")
        return result

    multiplier_str, quantity_str, unit_type_str = match.groups()

    try:
        # Handle potential commas as decimal separators common in DK/EU
        quantity_str_cleaned = quantity_str.replace('.', '').replace(',', '.')
        quantity = float(quantity_str_cleaned)
        
        # Apply multiplier if present
        multiplier = int(multiplier_str) if multiplier_str else 1
        total_quantity = quantity * multiplier
        
        result['quantity'] = total_quantity
        
    except ValueError:
        logger.warning(f"Could not convert quantity '{quantity_str}' to float in unit string: '{unit_string}'")
        return result # Cannot calculate price without quantity

    unit_type_lower = unit_type_str.lower()
    result['unit_type'] = unit_type_lower # Store the detected unit type

    if unit_type_lower in UNIT_CONVERSION:
        base_unit, factor = UNIT_CONVERSION[unit_type_lower]
        result['unit_type'] = unit_type_lower # Use the matched key as normalized unit

        # Calculate price per base unit (kg or l)
        if base_unit in ['kg', 'l']:
            try:
                # Calculate total quantity in the base unit (e.g., total grams -> kg)
                total_quantity_in_base_unit = total_quantity * factor
                if total_quantity_in_base_unit > 0:
                    result['price_per_base_unit'] = round(price / total_quantity_in_base_unit, 2)
                    result['base_unit_type'] = base_unit
                else:
                     logger.warning(f"Calculated zero or negative base quantity for '{unit_string}', cannot calculate unit price.")
            except ZeroDivisionError:
                 logger.warning(f"Division by zero attempted for unit string: '{unit_string}' with price {price}")
            except Exception as e:
                 logger.error(f"Error calculating unit price for '{unit_string}': {e}")
        # else: base_unit is 'stk', 'pakke' etc. - no price_per_base_unit calculation needed/possible

    else:
        logger.debug(f"Unit type '{unit_type_lower}' not found in UNIT_CONVERSION map for string: '{unit_string}'")
        # Store the unknown unit type anyway
        result['unit_type'] = unit_type_lower


    return result

# --- Example Usage ---
if __name__ == '__main__':
    test_cases = [
        ("500 g", 10.0),
        ("1.5 kg", 30.0),
        ("1,5 kg", 30.0),
        ("3 stk", 15.0),
        (" 750 ml ", 12.50),
        ("75 cl", 12.50),
        ("1 Liter", 8.0),
        ("2x100g", 25.0),
        ("1 pk", 5.0),
        ("flæskesteg", 99.95), # No quantity/unit
        ("12kg", 120.0),
        ("100 G", 5.0),
        ("3 Bakke", 45.0),
        ("1 Rulle", 10.0),
        ("1 kasse", 75.0),
        ("pakke", 20.0), # Test standalone unit
        ("Kasse", 110.0)  # Test standalone unit with different casing
    ]

    for unit_str, price_val in test_cases:
        parsed = parse_and_calculate_unit_price(unit_str, price_val)
        print(f"Input: ('{unit_str}', {price_val}) -> Output: {parsed}") 