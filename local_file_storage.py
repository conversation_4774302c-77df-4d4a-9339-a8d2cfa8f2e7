"""
Local File Storage Implementation
Implements the StorageInterface for local file system operations
Used for development and testing environments
"""

import os
import shutil
import logging
from typing import List, Optional, Union
from pathlib import Path
from urllib.parse import urljoin
from storage_interface import StorageInterface, StorageError, FileNotFoundError, UploadError, DownloadError

logger = logging.getLogger(__name__)


class LocalFileStorage(StorageInterface):
    """Local file system implementation of StorageInterface"""
    
    def __init__(self, base_dir: Union[str, Path] = None):
        """
        Initialize local file storage
        
        Args:
            base_dir: Base directory for local storage (defaults to ./local_storage)
        """
        self.base_dir = Path(base_dir) if base_dir else Path("local_storage")
        self.base_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Initialized Local File Storage with base directory: {self.base_dir}")
    
    def _get_local_path(self, cloud_path: str) -> Path:
        """Convert cloud path to local file system path"""
        # Remove leading slash if present and convert to Path
        clean_path = cloud_path.lstrip('/')
        return self.base_dir / clean_path
    
    def upload_file(self, local_path: Union[str, Path], cloud_path: str) -> bool:
        """Copy a file to local storage (simulates upload)"""
        try:
            source_path = Path(local_path)
            if not source_path.exists():
                logger.error(f"Source file does not exist: {source_path}")
                return False
            
            dest_path = self._get_local_path(cloud_path)
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(source_path, dest_path)
            
            logger.info(f"Successfully copied {source_path} to {dest_path}")
            return True
            
        except Exception as e:
            logger.error(f"Local upload failed for {local_path} -> {cloud_path}: {e}")
            raise UploadError(f"Failed to upload {local_path}: {e}")
    
    def download_file(self, cloud_path: str, local_path: Union[str, Path]) -> bool:
        """Copy a file from local storage (simulates download)"""
        try:
            source_path = self._get_local_path(cloud_path)
            if not source_path.exists():
                logger.error(f"Source file does not exist: {source_path}")
                raise FileNotFoundError(f"File not found: {cloud_path}")
            
            dest_path = Path(local_path)
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(source_path, dest_path)
            
            logger.info(f"Successfully copied {source_path} to {dest_path}")
            return True
            
        except FileNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Local download failed for {cloud_path} -> {local_path}: {e}")
            raise DownloadError(f"Failed to download {cloud_path}: {e}")
    
    def file_exists(self, cloud_path: str) -> bool:
        """Check if a file exists in local storage"""
        try:
            local_path = self._get_local_path(cloud_path)
            exists = local_path.exists() and local_path.is_file()
            logger.debug(f"File exists check for {cloud_path}: {exists}")
            return exists
        except Exception as e:
            logger.error(f"Error checking file existence for {cloud_path}: {e}")
            return False
    
    def delete_file(self, cloud_path: str) -> bool:
        """Delete a file from local storage"""
        try:
            local_path = self._get_local_path(cloud_path)
            if not local_path.exists():
                logger.warning(f"File does not exist, cannot delete: {cloud_path}")
                return False
            
            local_path.unlink()
            logger.info(f"Successfully deleted {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"Local delete failed for {cloud_path}: {e}")
            return False
    
    def list_files(self, prefix: str = "") -> List[str]:
        """List files in local storage with optional prefix"""
        try:
            if prefix:
                search_path = self._get_local_path(prefix)
                if search_path.is_file():
                    # If prefix points to a file, return just that file
                    return [prefix]
                elif search_path.is_dir():
                    # If prefix points to a directory, list files in it
                    files = []
                    for file_path in search_path.rglob('*'):
                        if file_path.is_file():
                            # Convert back to relative path from base_dir
                            rel_path = file_path.relative_to(self.base_dir)
                            files.append(str(rel_path).replace('\\', '/'))  # Normalize path separators
                    return files
                else:
                    # Prefix doesn't exist as file or directory
                    return []
            else:
                # List all files
                files = []
                for file_path in self.base_dir.rglob('*'):
                    if file_path.is_file():
                        rel_path = file_path.relative_to(self.base_dir)
                        files.append(str(rel_path).replace('\\', '/'))
                return files
                
        except Exception as e:
            logger.error(f"Error listing files with prefix '{prefix}': {e}")
            return []
    
    def get_file_url(self, cloud_path: str, expires_in: int = 3600) -> Optional[str]:
        """Get a local file URL (for development server)"""
        try:
            local_path = self._get_local_path(cloud_path)
            if not local_path.exists():
                logger.error(f"Cannot generate URL for non-existent file: {cloud_path}")
                return None
            
            # For local development, return a simple file:// URL or localhost URL
            # This assumes a local development server is serving files
            base_url = os.getenv('LOCAL_FILE_BASE_URL', 'http://localhost:8000/files/')
            url = urljoin(base_url, cloud_path)
            
            logger.debug(f"Generated local URL for {cloud_path}: {url}")
            return url
            
        except Exception as e:
            logger.error(f"Error generating URL for {cloud_path}: {e}")
            return None
    
    def get_file_size(self, cloud_path: str) -> Optional[int]:
        """Get the size of a file in local storage"""
        try:
            local_path = self._get_local_path(cloud_path)
            if not local_path.exists():
                return None
            
            size = local_path.stat().st_size
            logger.debug(f"File size for {cloud_path}: {size} bytes")
            return size
            
        except Exception as e:
            logger.error(f"Error getting file size for {cloud_path}: {e}")
            return None
    
    def copy_file(self, source_path: str, dest_path: str) -> bool:
        """Copy a file within local storage"""
        try:
            source_local = self._get_local_path(source_path)
            if not source_local.exists():
                logger.error(f"Source file does not exist: {source_path}")
                return False
            
            dest_local = self._get_local_path(dest_path)
            dest_local.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(source_local, dest_local)
            logger.info(f"Successfully copied {source_path} to {dest_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error copying file {source_path} -> {dest_path}: {e}")
            return False
