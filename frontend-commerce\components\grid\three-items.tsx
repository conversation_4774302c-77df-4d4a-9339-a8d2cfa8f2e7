import { GridTileImage } from './tile';
import { getProducts, BackendProduct } from '@/lib/backend';
import Link from 'next/link';

function ThreeItemGridItem({
  item,
  size,
  priority
}: {
  item: BackendProduct;
  size: 'full' | 'half';
  priority?: boolean;
}) {
  const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';

  return (
    <div
      className={size === 'full' ? 'md:col-span-4 md:row-span-2' : 'md:col-span-2 md:row-span-1'}
    >
      <Link
        className="relative block aspect-square h-full w-full"
        href={`/product/${item.id}`}
        prefetch={true}
      >
        <GridTileImage
          src={item.image_path ? `${API_BASE}${item.image_path}` : '/placeholder.png'}
          fill
          sizes={
            size === 'full' ? '(min-width: 768px) 66vw, 100vw' : '(min-width: 768px) 33vw, 100vw'
          }
          priority={priority}
          alt={item.name}
          label={{
            position: size === 'full' ? 'center' : 'bottom',
            title: item.name,
            amount: item.price.toString(),
            currencyCode: 'DKK'
          }}
        />
      </Link>
    </div>
  );
}

export async function ThreeItemGrid() {
  // Get featured products from our backend
  const products = await getProducts();

  if (!products[0] || !products[1] || !products[2]) return null;

  const [firstProduct, secondProduct, thirdProduct] = products;

  return (
    <section className="mx-auto grid max-w-(--breakpoint-2xl) gap-4 px-4 pb-4 md:grid-cols-6 md:grid-rows-2 lg:max-h-[calc(100vh-200px)]">
      <ThreeItemGridItem size="full" item={firstProduct} priority={true} />
      <ThreeItemGridItem size="half" item={secondProduct} priority={true} />
      <ThreeItemGridItem size="half" item={thirdProduct} />
    </section>
  );
}
