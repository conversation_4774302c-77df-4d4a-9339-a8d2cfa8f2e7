import os
from logging.config import fileConfig

from sqlalchemy import engine_from_config, create_engine
from sqlalchemy import pool

# Import Base from your database model FIRST
from database import Base

# Load .env file BEFORE accessing database URL
from dotenv import load_dotenv, find_dotenv

dotenv_path = find_dotenv()
if not dotenv_path:
    print("WARNING: .env file not found by find_dotenv(). Alembic might not get correct DATABASE_URL.")
else:
    print(f"INFO: Loading environment variables from: {dotenv_path}")
    load_dotenv(dotenv_path=dotenv_path)

from alembic import context

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Get the URL directly from the environment
DATABASE_URL = os.getenv("DATABASE_URL")
print(f"INFO: Value of DATABASE_URL read from environment: {DATABASE_URL}")
if not DATABASE_URL or not DATABASE_URL.startswith("postgresql://"):
    raise ValueError("DATABASE_URL not configured correctly in environment for PostgreSQL")

# Set the sqlalchemy.url in the config object just in case anything else uses it
# but we will bypass engine_from_config below.
config.set_main_option("sqlalchemy.url", DATABASE_URL)

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata # Point to your Base metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = DATABASE_URL # Use directly loaded URL
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # Create engine directly using the DATABASE_URL from environment
    connectable = create_engine(DATABASE_URL)

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
