"""add_processed_catalog_hashes_and_queue_tables

Revision ID: f737131ce452
Revises: 3562757e2767
Create Date: 2025-06-09 19:16:23.514125

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f737131ce452'
down_revision: Union[str, None] = '3562757e2767'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
