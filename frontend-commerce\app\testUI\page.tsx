"use client";

import { useState, useEffect } from 'react';
import Image from "next/image";
import { getCatalogs, BackendCatalog } from '@/lib/backend';
import { toast } from 'sonner';

export default function TestUIPage() {
  const [catalogs, setCatalogs] = useState<BackendCatalog[]>([]);
  const [question, setQuestion] = useState('');
  const [selectedCatalogs, setSelectedCatalogs] = useState<number[]>([]);

  useEffect(() => {
    const fetchCatalogs = async () => {
      try {
        const data = await getCatalogs();
        setCatalogs(data.slice(0, 6)); // Limit for mockups
      } catch (err) {
        console.error('Failed to load catalogs:', err);
      }
    };
    fetchCatalogs();
  }, []);

  const toggleCatalog = (id: number) => {
    setSelectedCatalogs(prev => 
      prev.includes(id) ? prev.filter(c => c !== id) : [...prev, id]
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto space-y-16">
        
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">🎨 UX Personality Test</h1>
          <p className="text-lg text-gray-600">Discover the perfect "human feel" for Tilbudsjægeren</p>

          {/* Navigation to Hybrid Designs */}
          <div className="mt-8 space-x-4">
            <a
              href="/testUI/hybrid"
              className="inline-block px-6 py-3 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors shadow-lg"
            >
              🎯 View Hybrid Designs (1+3 Combined)
            </a>
            <a
              href="/testUI/advanced"
              className="inline-block px-6 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors shadow-lg"
            >
              🚀 Advanced Adaptive Design
            </a>
          </div>
        </div>

        {/* ===== MOCKUP 1: GOOGLE-LIKE MINIMALIST ===== */}
        <div className="border-t-8 border-blue-500 pt-8">
          <h2 className="text-2xl font-bold text-blue-600 mb-6">1. 🔍 Google-Like Minimalist</h2>
          <p className="text-gray-600 mb-8"><strong>Vibe:</strong> Clean, fast, efficient. "Just get me the answer."</p>
          
          <div className="bg-white min-h-96 flex flex-col items-center justify-center p-8">
            {/* Simple Logo */}
            <div className="w-32 h-32 relative mb-8">
              <Image 
                src="/logos/logo-samlet.png" 
                alt="Tilbudsjægeren"
                fill
                className="object-contain"
              />
            </div>
            
            {/* Minimal Search */}
            <div className="w-full max-w-lg">
              <input
                type="text"
                placeholder="Søg efter tilbud..."
                className="w-full px-6 py-4 text-lg border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
              />
              <div className="flex justify-center mt-6 space-x-4">
                <button className="px-6 py-2 bg-gray-100 text-gray-800 rounded hover:shadow-md transition-shadow">
                  Søg Tilbud
                </button>
                <button className="px-6 py-2 bg-gray-100 text-gray-800 rounded hover:shadow-md transition-shadow">
                  Jeg Har Lyst
                </button>
              </div>
            </div>
            
            {/* Minimal Store Selection */}
            <div className="mt-8 flex space-x-2">
              {catalogs.slice(0, 4).map((catalog) => (
                <div
                  key={catalog.id}
                  onClick={() => toggleCatalog(catalog.id)}
                  className={`w-12 h-12 rounded-full overflow-hidden cursor-pointer transition-all ${
                    selectedCatalogs.includes(catalog.id) ? 'ring-2 ring-blue-500 scale-110' : 'opacity-60 hover:opacity-100'
                  }`}
                >
                  <img
                    src={catalog.store_logo_url || '/placeholder.png'}
                    alt={catalog.title}
                    className="w-full h-full object-contain"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* ===== MOCKUP 2: TECH STARTUP SILICON VALLEY ===== */}
        <div className="border-t-8 border-purple-500 pt-8">
          <h2 className="text-2xl font-bold text-purple-600 mb-6">2. 🚀 Tech Startup Silicon Valley</h2>
          <p className="text-gray-600 mb-8"><strong>Vibe:</strong> Cutting-edge, innovative, "We're disrupting grocery shopping."</p>
          
          <div className="bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 min-h-96 p-8 rounded-xl">
            <div className="max-w-4xl mx-auto">
              {/* Tech Header */}
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold text-white mb-2">
                  AI-Powered Deal Discovery
                </h3>
                <p className="text-purple-300">Next-generation grocery intelligence platform</p>
              </div>
              
              {/* Futuristic Search */}
              <div className="relative mb-8">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg blur opacity-20"></div>
                <div className="relative bg-gray-800 border border-purple-500 rounded-lg p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">AI</span>
                    </div>
                    <input
                      type="text"
                      placeholder="Query our neural network..."
                      className="flex-1 bg-transparent text-white placeholder-gray-400 outline-none text-lg"
                      value={question}
                      onChange={(e) => setQuestion(e.target.value)}
                    />
                    <button className="px-6 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all">
                      Execute
                    </button>
                  </div>
                </div>
              </div>
              
              {/* Tech Store Grid */}
              <div className="grid grid-cols-6 gap-4">
                {catalogs.map((catalog) => (
                  <div
                    key={catalog.id}
                    onClick={() => toggleCatalog(catalog.id)}
                    className={`relative group cursor-pointer transition-all duration-300 ${
                      selectedCatalogs.includes(catalog.id) ? 'scale-110' : 'hover:scale-105'
                    }`}
                  >
                    <div className={`w-16 h-16 rounded-lg overflow-hidden border-2 ${
                      selectedCatalogs.includes(catalog.id) 
                        ? 'border-purple-400 shadow-lg shadow-purple-500/50' 
                        : 'border-gray-600 group-hover:border-purple-400'
                    }`}>
                      <img
                        src={catalog.store_logo_url || '/placeholder.png'}
                        alt={catalog.title}
                        className="w-full h-full object-contain bg-white"
                      />
                    </div>
                    {selectedCatalogs.includes(catalog.id) && (
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
              
              {/* Tech Stats */}
              <div className="mt-8 grid grid-cols-3 gap-4 text-center">
                <div className="bg-gray-800/50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-purple-400">1.2M+</div>
                  <div className="text-gray-400 text-sm">Products Analyzed</div>
                </div>
                <div className="bg-gray-800/50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-400">99.7%</div>
                  <div className="text-gray-400 text-sm">Accuracy Rate</div>
                </div>
                <div className="bg-gray-800/50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-400">€2.3M</div>
                  <div className="text-gray-400 text-sm">Saved by Users</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* ===== MOCKUP 3: OVER-EXPLAINING FOR SENIORS ===== */}
        <div className="border-t-8 border-green-500 pt-8">
          <h2 className="text-2xl font-bold text-green-600 mb-6">3. 👴 Senior-Friendly Tutorial</h2>
          <p className="text-gray-600 mb-8"><strong>Vibe:</strong> Patient, helpful, "Let me walk you through this step by step."</p>
          
          <div className="bg-green-50 min-h-96 p-8 rounded-xl border-2 border-green-200">
            <div className="max-w-4xl mx-auto">
              {/* Friendly Header */}
              <div className="text-center mb-8">
                <div className="w-24 h-24 relative mx-auto mb-4">
                  <Image 
                    src="/logos/logo-samlet.png" 
                    alt="Tilbudsjægeren Owl"
                    fill
                    className="object-contain"
                  />
                </div>
                <h3 className="text-2xl font-bold text-green-800 mb-2">
                  Velkommen til Tilbudsjægeren! 👋
                </h3>
                <p className="text-green-700 text-lg">Vi hjælper dig med at finde de bedste tilbud - helt enkelt!</p>
              </div>
              
              {/* Step-by-step Guide */}
              <div className="space-y-6">
                {/* Step 1 */}
                <div className="bg-white rounded-lg p-6 border-l-4 border-green-500">
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-bold">1</div>
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-green-800 mb-2">Vælg dine foretrukne butikker</h4>
                      <p className="text-green-700 mb-4">Klik på logoerne nedenfor for de butikker, du handler i. Du kan vælge flere!</p>
                      <div className="grid grid-cols-3 gap-4">
                        {catalogs.slice(0, 6).map((catalog) => (
                          <div
                            key={catalog.id}
                            onClick={() => toggleCatalog(catalog.id)}
                            className={`p-4 rounded-lg border-2 cursor-pointer transition-all text-center ${
                              selectedCatalogs.includes(catalog.id) 
                                ? 'border-green-500 bg-green-100' 
                                : 'border-gray-300 hover:border-green-300 bg-white'
                            }`}
                          >
                            <img
                              src={catalog.store_logo_url || '/placeholder.png'}
                              alt={catalog.title}
                              className="w-12 h-12 object-contain mx-auto mb-2"
                            />
                            <div className="text-sm font-medium text-gray-700">{catalog.title.split(' ')[0]}</div>
                            {selectedCatalogs.includes(catalog.id) && (
                              <div className="text-green-600 text-sm mt-1">✓ Valgt</div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Step 2 */}
                <div className="bg-white rounded-lg p-6 border-l-4 border-blue-500">
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">2</div>
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-blue-800 mb-2">Skriv hvad du søger efter</h4>
                      <p className="text-blue-700 mb-4">Skriv f.eks. "billig kaffe" eller "økologiske æg". Vores AI forstår dansk!</p>
                      <div className="relative">
                        <input
                          type="text"
                          placeholder="Eksempel: billig kaffe eller økologiske æg"
                          className="w-full px-4 py-3 text-lg border-2 border-blue-300 rounded-lg focus:outline-none focus:border-blue-500"
                          value={question}
                          onChange={(e) => setQuestion(e.target.value)}
                        />
                        <div className="absolute right-3 top-3 text-blue-400">
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Step 3 */}
                <div className="bg-white rounded-lg p-6 border-l-4 border-purple-500">
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold">3</div>
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-purple-800 mb-2">Tryk på "Find Tilbud" knappen</h4>
                      <p className="text-purple-700 mb-4">Vores AI vil finde de bedste tilbud for dig på få sekunder!</p>
                      <button className="px-8 py-4 bg-purple-500 text-white text-lg font-semibold rounded-lg hover:bg-purple-600 transition-colors shadow-lg">
                        🔍 Find Tilbud Nu
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Help Section */}
              <div className="mt-8 bg-yellow-50 border-2 border-yellow-200 rounded-lg p-6">
                <div className="flex items-center space-x-3 mb-3">
                  <span className="text-2xl">💡</span>
                  <h4 className="text-lg font-semibold text-yellow-800">Har du brug for hjælp?</h4>
                </div>
                <p className="text-yellow-700 mb-4">Hvis du har spørgsmål, kan du altid:</p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-white rounded-lg">
                    <span className="text-3xl mb-2 block">📞</span>
                    <div className="font-medium">Ring til os</div>
                    <div className="text-sm text-gray-600">70 20 30 40</div>
                  </div>
                  <div className="text-center p-4 bg-white rounded-lg">
                    <span className="text-3xl mb-2 block">📧</span>
                    <div className="font-medium">Send en email</div>
                    <div className="text-sm text-gray-600"><EMAIL></div>
                  </div>
                  <div className="text-center p-4 bg-white rounded-lg">
                    <span className="text-3xl mb-2 block">💬</span>
                    <div className="font-medium">Chat med os</div>
                    <div className="text-sm text-gray-600">Nederst på siden</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* ===== MOCKUP 4: PREMIUM LUXURY SHOPPING ===== */}
        <div className="border-t-8 border-gold-500 pt-8" style={{borderTopColor: '#D4AF37'}}>
          <h2 className="text-2xl font-bold mb-6" style={{color: '#D4AF37'}}>4. 💎 Premium Luxury Experience</h2>
          <p className="text-gray-600 mb-8"><strong>Vibe:</strong> Sophisticated, exclusive, "You deserve the finest deals."</p>

          <div className="bg-gradient-to-br from-gray-900 to-black min-h-96 p-8 rounded-xl">
            <div className="max-w-4xl mx-auto">
              {/* Luxury Header */}
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold text-white mb-2" style={{fontFamily: 'serif'}}>
                  Tilbudsjægeren <span style={{color: '#D4AF37'}}>Premium</span>
                </h3>
                <p className="text-gray-300">Curated deals for discerning shoppers</p>
              </div>

              {/* Elegant Search */}
              <div className="relative mb-8">
                <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 rounded-full flex items-center justify-center" style={{background: 'linear-gradient(45deg, #D4AF37, #FFD700)'}}>
                      <span className="text-black text-sm font-bold">AI</span>
                    </div>
                    <input
                      type="text"
                      placeholder="What premium products are you seeking today?"
                      className="flex-1 bg-transparent text-white placeholder-gray-400 outline-none text-lg"
                      value={question}
                      onChange={(e) => setQuestion(e.target.value)}
                    />
                    <button className="px-8 py-3 text-black font-semibold rounded-lg transition-all hover:scale-105" style={{background: 'linear-gradient(45deg, #D4AF37, #FFD700)'}}>
                      Discover
                    </button>
                  </div>
                </div>
              </div>

              {/* Premium Store Selection */}
              <div className="grid grid-cols-6 gap-6">
                {catalogs.map((catalog) => (
                  <div
                    key={catalog.id}
                    onClick={() => toggleCatalog(catalog.id)}
                    className={`relative group cursor-pointer transition-all duration-500 ${
                      selectedCatalogs.includes(catalog.id) ? 'scale-110' : 'hover:scale-105'
                    }`}
                  >
                    <div className={`w-20 h-20 rounded-xl overflow-hidden border-2 ${
                      selectedCatalogs.includes(catalog.id)
                        ? 'shadow-lg'
                        : 'border-white/20 group-hover:border-white/40'
                    }`} style={{
                      borderColor: selectedCatalogs.includes(catalog.id) ? '#D4AF37' : undefined,
                      boxShadow: selectedCatalogs.includes(catalog.id) ? '0 0 20px rgba(212, 175, 55, 0.5)' : undefined
                    }}>
                      <img
                        src={catalog.store_logo_url || '/placeholder.png'}
                        alt={catalog.title}
                        className="w-full h-full object-contain bg-white"
                      />
                    </div>
                    {selectedCatalogs.includes(catalog.id) && (
                      <div className="absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center" style={{background: 'linear-gradient(45deg, #D4AF37, #FFD700)'}}>
                        <span className="text-black text-xs font-bold">✓</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Premium Features */}
              <div className="mt-8 grid grid-cols-2 gap-6">
                <div className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10">
                  <h4 className="text-white font-semibold mb-2">🏆 Exclusive Deals</h4>
                  <p className="text-gray-400 text-sm">Access to premium offers not available elsewhere</p>
                </div>
                <div className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10">
                  <h4 className="text-white font-semibold mb-2">⚡ Priority Processing</h4>
                  <p className="text-gray-400 text-sm">Lightning-fast AI responses for immediate results</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* ===== MOCKUP 5: PLAYFUL GAMIFIED ===== */}
        <div className="border-t-8 border-pink-500 pt-8">
          <h2 className="text-2xl font-bold text-pink-600 mb-6">5. 🎮 Playful & Gamified</h2>
          <p className="text-gray-600 mb-8"><strong>Vibe:</strong> Fun, engaging, "Let's make deal hunting a game!"</p>

          <div className="bg-gradient-to-br from-pink-100 via-purple-100 to-blue-100 min-h-96 p-8 rounded-xl">
            <div className="max-w-4xl mx-auto">
              {/* Playful Header */}
              <div className="text-center mb-8">
                <div className="w-32 h-32 relative mx-auto mb-4">
                  <Image
                    src="/logos/logo-samlet.png"
                    alt="Tilbudsjægeren Owl"
                    fill
                    className="object-contain animate-bounce"
                  />
                </div>
                <h3 className="text-3xl font-bold text-purple-800 mb-2">
                  🎯 Deal Hunter Challenge!
                </h3>
                <p className="text-purple-600">Level up your savings game!</p>
              </div>

              {/* Game Stats */}
              <div className="grid grid-cols-4 gap-4 mb-8">
                <div className="bg-white rounded-lg p-4 text-center shadow-lg">
                  <div className="text-2xl mb-1">🏆</div>
                  <div className="text-lg font-bold text-purple-600">Level 5</div>
                  <div className="text-xs text-gray-600">Deal Hunter</div>
                </div>
                <div className="bg-white rounded-lg p-4 text-center shadow-lg">
                  <div className="text-2xl mb-1">💰</div>
                  <div className="text-lg font-bold text-green-600">€127</div>
                  <div className="text-xs text-gray-600">Saved Today</div>
                </div>
                <div className="bg-white rounded-lg p-4 text-center shadow-lg">
                  <div className="text-2xl mb-1">🔥</div>
                  <div className="text-lg font-bold text-orange-600">12</div>
                  <div className="text-xs text-gray-600">Streak Days</div>
                </div>
                <div className="bg-white rounded-lg p-4 text-center shadow-lg">
                  <div className="text-2xl mb-1">⭐</div>
                  <div className="text-lg font-bold text-yellow-600">2,450</div>
                  <div className="text-xs text-gray-600">Points</div>
                </div>
              </div>

              {/* Fun Search */}
              <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
                <h4 className="text-lg font-bold text-purple-800 mb-4">🎯 What's your next target?</h4>
                <div className="flex space-x-2 mb-4">
                  <input
                    type="text"
                    placeholder="Type your quest... (e.g., 'epic coffee deals')"
                    className="flex-1 px-4 py-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:border-purple-500"
                    value={question}
                    onChange={(e) => setQuestion(e.target.value)}
                  />
                  <button className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all transform hover:scale-105">
                    🚀 Hunt!
                  </button>
                </div>

                {/* Quick Quest Buttons */}
                <div className="flex flex-wrap gap-2">
                  {['🥚 Egg Hunt', '☕ Coffee Quest', '🍓 Berry Mission', '🧀 Cheese Challenge'].map((quest) => (
                    <button
                      key={quest}
                      onClick={() => setQuestion(quest.split(' ')[1]?.toLowerCase() || '')}
                      className="px-4 py-2 bg-gradient-to-r from-blue-400 to-purple-400 text-white rounded-full text-sm hover:from-blue-500 hover:to-purple-500 transition-all transform hover:scale-105"
                    >
                      {quest}
                    </button>
                  ))}
                </div>
              </div>

              {/* Gamified Store Selection */}
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <h4 className="text-lg font-bold text-purple-800 mb-4">🏪 Choose your hunting grounds!</h4>
                <div className="grid grid-cols-6 gap-4">
                  {catalogs.map((catalog) => (
                    <div
                      key={catalog.id}
                      onClick={() => toggleCatalog(catalog.id)}
                      className={`relative cursor-pointer transition-all duration-300 transform ${
                        selectedCatalogs.includes(catalog.id) ? 'scale-110 rotate-3' : 'hover:scale-105 hover:-rotate-1'
                      }`}
                    >
                      <div className={`w-16 h-16 rounded-xl overflow-hidden border-3 ${
                        selectedCatalogs.includes(catalog.id)
                          ? 'border-pink-400 shadow-lg shadow-pink-500/50'
                          : 'border-purple-300 hover:border-pink-400'
                      }`}>
                        <img
                          src={catalog.store_logo_url || '/placeholder.png'}
                          alt={catalog.title}
                          className="w-full h-full object-contain bg-white"
                        />
                      </div>
                      {selectedCatalogs.includes(catalog.id) && (
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center animate-pulse">
                          <span className="text-white text-xs font-bold">✓</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Achievement Notification */}
              <div className="mt-6 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-lg p-4 text-center">
                <div className="text-white font-bold">🎉 Achievement Unlocked: Store Explorer! 🎉</div>
                <div className="text-yellow-100 text-sm">You've selected {selectedCatalogs.length} hunting grounds!</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
