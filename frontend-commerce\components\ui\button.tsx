import React from 'react';
import Link from 'next/link';
import { clsx } from 'clsx';

type ButtonProps = {
  children: React.ReactNode;
  variant?: 'default' | 'outline' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
  asChild?: boolean;
};

export const Button = ({
  children,
  variant = 'default',
  size = 'md',
  className = '',
  onClick,
  type = 'button',
  disabled = false,
  asChild = false,
  ...props
}: ButtonProps & React.HTMLAttributes<HTMLButtonElement>) => {
  const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-colors';
  
  const variantStyles = {
    default: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
    outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
    ghost: 'bg-transparent hover:bg-gray-100 text-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2',
    link: 'bg-transparent text-blue-600 hover:underline p-0 focus:ring-0'
  };
  
  const sizeStyles = {
    sm: 'text-sm px-3 py-1',
    md: 'text-sm px-4 py-2',
    lg: 'text-base px-6 py-3'
  };
  
  const styles = clsx(
    baseStyles,
    variantStyles[variant],
    sizeStyles[size],
    disabled && 'opacity-50 cursor-not-allowed',
    className
  );

  if (asChild) {
    return (
      <div className={styles}>
        {React.Children.map(children, child => {
          if (React.isValidElement(child)) {
            const childElement = child as React.ReactElement<any>;
            return React.cloneElement(childElement, {
              className: clsx((childElement.props as any)?.className),
              ...props
            });
          }
          return child;
        })}
      </div>
    );
  }
  
  return (
    <button
      type={type}
      className={styles}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
};
