#!/usr/bin/env python3
"""
Quick script to process <PERSON><PERSON> and Bilka catalogs directly from cloud storage.
"""

import os
import sys
from datetime import datetime, date
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment
from dotenv import load_dotenv
load_dotenv()

# Import required modules
from database import SessionLocal, Store, Catalog, CatalogProcessingQueue
from sqlalchemy import func
from pdf_extractor import process_pdf_catalog_from_cloud
from catalog_processor import parse_danish_date_range, parse_verbose_danish_date_range
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def process_catalog_from_cloud(store_name: str, title: str, raw_date_info: str, cloud_pdf_path: str):
    """Process a single catalog from cloud storage"""
    
    logger.info(f"Processing {store_name} catalog from cloud: {cloud_pdf_path}")
    
    # Parse dates - try different parsing methods
    logger.info(f"Parsing date string: '{raw_date_info}'")
    date_result = parse_danish_date_range(raw_date_info)
    logger.info(f"Danish date parsing result: {date_result}")

    if not date_result:
        # Try verbose parsing for complex strings like Bilka's
        logger.info(f"Trying verbose Danish date parsing...")
        date_result = parse_verbose_danish_date_range(raw_date_info)
        logger.info(f"Verbose date parsing result: {date_result}")

    if not date_result:
        logger.error(f"Failed to parse dates from '{raw_date_info}' - got None")
        return False

    start_date, end_date = date_result
    if not start_date or not end_date:
        logger.error(f"Failed to parse dates from '{raw_date_info}' - got {start_date}, {end_date}")
        return False
    
    # Create database session
    db = SessionLocal()
    try:
        # Get or create store
        store = db.query(Store).filter(Store.name == store_name).first()
        if not store:
            store = Store(name=store_name)
            db.add(store)
            db.flush()

        # Check if catalog already exists (using date-only comparison)
        existing_catalog = db.query(Catalog).filter(
            Catalog.store_id == store.id,
            func.date(Catalog.valid_from) == start_date,
            func.date(Catalog.valid_to) == end_date
        ).first()
        
        if existing_catalog:
            logger.info(f"Catalog already exists: {title}")
            return True
        
        # Convert dates to datetime objects
        valid_from_dt = datetime.combine(start_date, datetime.min.time())
        valid_to_dt = datetime.combine(end_date, datetime.min.time())
        
        # Process PDF from cloud storage
        processed_catalog, image_paths = process_pdf_catalog_from_cloud(
            cloud_pdf_path=cloud_pdf_path,
            store_name=store_name,
            title=title,
            valid_from=valid_from_dt,
            valid_to=valid_to_dt,
            attempt_date_extraction=False,
            db=db
        )
        
        if processed_catalog:
            # Add to processing queue for AI analysis
            today = date.today()
            is_active_today = start_date <= today <= end_date
            
            queue_entry = CatalogProcessingQueue(
                catalog_db_id=processed_catalog.id,
                pdf_path_to_process=cloud_pdf_path,
                pdf_content_hash="temp_hash",  # We'll calculate this properly later
                status='PENDING',
                retry_count=0
            )
            db.add(queue_entry)
            db.commit()
            
            logger.info(f"✅ Successfully processed {store_name} catalog: {title}")
            return True
        else:
            logger.error(f"Failed to process PDF: {cloud_pdf_path}")
            return False
            
    except Exception as e:
        logger.error(f"Error processing {store_name} catalog: {e}", exc_info=True)
        db.rollback()
        return False
    finally:
        db.close()

def main():
    """Process Netto and Bilka catalogs from cloud storage"""

    print("🚀 Starting cloud catalog processing...")

    # Netto catalog
    print("\n📦 Processing Netto catalog...")
    netto_success = process_catalog_from_cloud(
        store_name="Netto",
        title="Netto Catalog (5. - 11. juli)",
        raw_date_info="5. - 11. juli",
        cloud_pdf_path="catalogs/netto_20250705_20250711.pdf"
    )
    
    # Bilka catalog
    print("\n📦 Processing Bilka catalog...")
    bilka_success = process_catalog_from_cloud(
        store_name="Bilka",
        title="Bilka Catalog (Gælder fra d. 4. juli til og med d. 10. juli*)",
        raw_date_info="Gælder fra d. 4. juli til og med d. 10. juli*",
        cloud_pdf_path="catalogs/bilka_20250704_20250710.pdf"
    )

    # 365discount catalog
    print("\n📦 Processing 365discount catalog...")
    discount365_success = process_catalog_from_cloud(
        store_name="365discount",
        title="365discount Catalog (Avisen gælder fra torsdag den 3. juli til og med onsdag den 9. juli 2025)",
        raw_date_info="Avisen gælder fra torsdag den 3. juli til og med onsdag den 9. juli 2025",
        cloud_pdf_path="catalogs/365discount__.pdf"
    )

    print("\n=== Results ===")
    print(f"Netto: {'✅ Success' if netto_success else '❌ Failed'}")
    print(f"Bilka: {'✅ Success' if bilka_success else '❌ Failed'}")
    print(f"365discount: {'✅ Success' if discount365_success else '❌ Failed'}")

    if netto_success and bilka_success and discount365_success:
        print("\n🎉 All catalogs processed successfully!")
        print("They should now appear on the website!")
    else:
        print("\n⚠️ Some catalogs failed to process")

if __name__ == "__main__":
    main()
