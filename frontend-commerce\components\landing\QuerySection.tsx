"use client";
import { useState, useEffect } from 'react';
import { queryOffers, getCatalogs, BackendCatalog, QueryResponse, BackendProduct } from '@/lib/backend';
import OfferList from './OfferList';
import EnhancedProductGrid from '@/components/layout/enhanced-product-grid';
import { toast } from 'sonner';
import { buildThumbnailUrl, getStoreLogoUrl } from '@/lib/ui-helpers';
import { useSelectedCatalogsContext } from '@/lib/contexts/SelectedCatalogsContext';

// Define the API base for image URLs
const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';

export default function QuerySection() {
  const [question, setQuestion] = useState('');
  const [catalogs, setCatalogs] = useState<BackendCatalog[]>([]);
  const [catalogsLoading, setCatalogsLoading] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [answer, setAnswer] = useState<string | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<BackendProduct[]>([]);
  const [promptsShown, setPromptsShown] = useState(false);

  // Use selected catalogs from context
  const { selectedCatalogs, toggleCatalog, clearSelectedCatalogs } = useSelectedCatalogsContext();

  // Example prompts for the suggestion gallery
  const examplePrompts = [
    'Billige økologiske æg', 
    'Tilbud på Coca-Cola', 
    'Hvilke kataloger har jordbær?',
    'Bedste tilbud på kaffe denne uge',
    'Hvor finder jeg det billigste vaskepulver?'
  ];

  useEffect(() => {
    const fetchCatalogs = async () => {
      try {
        setCatalogsLoading(true);
        const data = await getCatalogs();
        setCatalogs(data);
      } catch (err) {
        console.error('Failed to load catalogs:', err);
        toast.error('Kunne ikke indlæse kataloger');
      } finally {
        setCatalogsLoading(false);
      }
    };
    
    fetchCatalogs();
  }, []);

  // Use the selected catalogs from the shared context
  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!question.trim()) {
      toast.error('Indtast venligst et spørgsmål');
      return;
    }
    
    setLoading(true);
    setError(null);
    setAnswer(null);
    setRelatedProducts([]);
    
    try {
      // Pass the selectedCatalogs from context to the query
      const res = await queryOffers(question.trim(), selectedCatalogs.length > 0 ? selectedCatalogs : undefined);
      
      // Check if the response contains an error from the AI model
      if (res.response && res.response.includes('Der opstod en fejl under behandling af din forespørgsel med AI-modellen')) {
        toast.error('AI-modellen kunne ikke gennemføre forespørgslen');
        setError('AI-modellen kunne ikke gennemføre forespørgslen. Prøv igen med et andet spørgsmål eller vælg flere kataloger.');
      } else {
        setAnswer(res.response);
        setRelatedProducts(res.products || []);
      }
    } catch (err) {
      toast.error('Kunne ikke hente svar');
      setError('Kunne ikke hente svar. Prøv igen senere.');
      console.error('Query error:', err);
    } finally {
      setLoading(false);
    }
  }


  return (
    <section className="bg-sky-100/50 backdrop-blur-md p-6 rounded-xl shadow-lg border border-sky-200/50">
      <h2 className="text-2xl font-bold text-gray-900">Spørg AI om tilbud</h2>
      
      {/* Example Prompts */}
      <div className="flex flex-wrap gap-2 text-sm my-4">
        {examplePrompts.map((prompt) => (
          <button
            key={prompt}
            type="button"
            className="bg-white border border-gray-300 text-gray-900 px-4 py-3 rounded-full hover:bg-gray-50 hover:border-sky-400 hover:text-sky-700 hover:shadow-lg hover:shadow-sky-500/20 hover:scale-105 transition-all duration-200 active:scale-95"
            onClick={() => setQuestion(prompt)}
          >
            {prompt}
          </button>
        ))}
      </div>
      
      {/* 🏪 SHOP SELECTOR - MOVED ABOVE SEARCH FOR BETTER UX FLOW */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-3">
          <p className="text-gray-700 font-medium">
            Vælg butikker først:
            {selectedCatalogs.length > 0 && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-sky-500 text-white">
                {selectedCatalogs.length} valgt
              </span>
            )}
          </p>
          {selectedCatalogs.length > 0 && (
            <button
              onClick={() => clearSelectedCatalogs()}
              className="text-xs text-gray-400 hover:text-sky-400 transition-colors duration-200"
            >
              Nulstil valg
            </button>
          )}
        </div>

        {catalogsLoading ? (
          <div className="flex justify-center items-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-sky-500"></div>
          </div>
        ) : catalogs.length === 0 ? (
          <p className="text-sm text-gray-600">Ingen kataloger tilgængelige</p>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-3">
            {catalogs.map((catalog) => {
              const isSelected = selectedCatalogs.includes(catalog.id);
              return (
                <div
                  key={catalog.id}
                  onClick={() => toggleCatalog(catalog.id)}
                  className={`cursor-pointer transition-all duration-300 ${isSelected ? 'scale-105' : 'scale-100 hover:scale-102'}`}
                >
                  <div className={`h-20 w-full overflow-hidden rounded-lg border-2 shadow-md ${isSelected ? 'border-sky-400 shadow-lg shadow-sky-500/30 bg-sky-50/10' : 'border-gray-600/50 hover:border-sky-300 hover:shadow-lg hover:shadow-sky-500/10 bg-white/5 hover:bg-white/10'} active:scale-95 transition-all duration-200`}>
                    <div className="relative h-full w-full">
                      <img
                        src={catalog.store_logo_url || catalog.first_page_image_url || '/placeholder.png'}
                        alt={catalog.title}
                        className={`h-full w-full object-contain ${isSelected ? '' : 'opacity-80 group-hover:opacity-100'}`}
                      />
                      {isSelected && (
                        <div className="absolute inset-0 bg-sky-900/50 flex items-center justify-center">
                          <div className="bg-sky-500 rounded-full p-1">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      <form onSubmit={onSubmit} className="space-y-6">
        {/* Query Input */}
        <div className="flex gap-2">
          <input
            type="text"
            placeholder="Dit spørgsmål..."
            className="flex-1 bg-white/80 border border-gray-300 focus:border-sky-500 focus:ring-sky-500 rounded-lg px-4 py-3 shadow-inner text-gray-900 outline-none placeholder-gray-500"
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
          />
          <button
            type="submit"
            className="bg-white/60 text-gray-800 hover:bg-white/90 transition-colors px-3 py-1.5 rounded-full text-sm shadow-sm font-semibold shadow-md disabled:bg-sky-800/50 transition-all hover:shadow-lg hover:shadow-sky-500/30"
            disabled={loading}
          >
            {loading ? (
              <span className="flex items-center gap-2">
                <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Søger...
              </span>
            ) : (
              'Spørg'
            )}
          </button>
        </div>
      </form>
      
      {/* Error Message */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/50 p-4 rounded-lg">
          <p className="text-red-300">{error}</p>
        </div>
      )}
      
      {/* Results Section (Answer & Products) */}
      {answer && (
        <div className="mt-6 space-y-6">
          {/* AI Answer */}
          <div className="p-5 bg-white/95 backdrop-blur-sm border border-sky-200 rounded-lg shadow-lg">
            <h3 className="text-xl font-bold mb-3 text-sky-600 flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
              AI Svar
            </h3>
            <div className="text-gray-800 whitespace-pre-wrap">
              {answer.split('\n').map((paragraph, idx) => (
                paragraph ? (
                  <p key={idx} className="mb-3 leading-relaxed">
                    {paragraph}
                  </p>
                ) : null
              ))}
            </div>
          </div>

          {/* Related Products */}
          {relatedProducts.length > 0 && (
            <div className="rounded-lg p-5 bg-black/20 backdrop-blur-sm border border-gray-700/80 shadow-lg">
              <h3 className="text-xl font-bold mb-3 text-sky-400 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" />
                </svg>
                Relaterede Produkter
              </h3>
              <EnhancedProductGrid
                products={relatedProducts}
                title=""
                showCount={false}
              />
            </div>
          )}
        </div>
      )}
    </section>
  );
}
