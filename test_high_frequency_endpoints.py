#!/usr/bin/env python3
"""
Test script to verify high-frequency endpoint optimizations
"""

import asyncio
import aiohttp
import time
import os
from dotenv import load_dotenv
load_dotenv()

# Test configuration
API_BASE = "http://localhost:6969"
TEST_DURATION = 30  # seconds
POLL_INTERVAL = 0.5  # seconds (2 requests per second)

async def test_endpoint_performance():
    """Test the optimized high-frequency endpoints"""
    print("🧪 TESTING HIGH-FREQUENCY ENDPOINT OPTIMIZATIONS")
    print("=" * 60)
    
    # Test results tracking
    results = {
        "catalogs_requests": 0,
        "catalogs_successes": 0,
        "catalogs_failures": 0,
        "parsing_status_requests": 0,
        "parsing_status_successes": 0,
        "parsing_status_failures": 0,
        "total_time": 0,
        "errors": []
    }
    
    start_time = time.time()
    
    async with aiohttp.ClientSession() as session:
        print(f"🔄 Starting {TEST_DURATION}s test with {POLL_INTERVAL}s intervals...")
        print(f"   Expected requests: ~{int(TEST_DURATION / POLL_INTERVAL)} per endpoint")
        
        while time.time() - start_time < TEST_DURATION:
            # Test /catalogs endpoint (no auth needed now)
            try:
                results["catalogs_requests"] += 1
                async with session.get(f"{API_BASE}/catalogs") as response:
                    if response.status == 200:
                        data = await response.json()
                        results["catalogs_successes"] += 1
                        if results["catalogs_requests"] == 1:
                            print(f"✅ /catalogs: {len(data)} catalogs returned")
                    else:
                        results["catalogs_failures"] += 1
                        error_text = await response.text()
                        results["errors"].append(f"/catalogs {response.status}: {error_text[:100]}")
            except Exception as e:
                results["catalogs_failures"] += 1
                results["errors"].append(f"/catalogs error: {str(e)[:100]}")
            
            # Test /parsing_status endpoint (no auth needed now)
            try:
                results["parsing_status_requests"] += 1
                async with session.get(f"{API_BASE}/parsing_status") as response:
                    if response.status == 200:
                        data = await response.json()
                        results["parsing_status_successes"] += 1
                        if results["parsing_status_requests"] == 1:
                            print(f"✅ /parsing_status: {len(data)} statuses returned")
                    else:
                        results["parsing_status_failures"] += 1
                        error_text = await response.text()
                        results["errors"].append(f"/parsing_status {response.status}: {error_text[:100]}")
            except Exception as e:
                results["parsing_status_failures"] += 1
                results["errors"].append(f"/parsing_status error: {str(e)[:100]}")
            
            # Wait before next poll
            await asyncio.sleep(POLL_INTERVAL)
    
    results["total_time"] = time.time() - start_time
    
    # Print results
    print(f"\n📊 TEST RESULTS ({results['total_time']:.1f}s):")
    print(f"   /catalogs:")
    print(f"     Requests: {results['catalogs_requests']}")
    print(f"     Successes: {results['catalogs_successes']}")
    print(f"     Failures: {results['catalogs_failures']}")
    print(f"     Success Rate: {results['catalogs_successes']/results['catalogs_requests']*100:.1f}%")
    
    print(f"   /parsing_status:")
    print(f"     Requests: {results['parsing_status_requests']}")
    print(f"     Successes: {results['parsing_status_successes']}")
    print(f"     Failures: {results['parsing_status_failures']}")
    print(f"     Success Rate: {results['parsing_status_successes']/results['parsing_status_requests']*100:.1f}%")
    
    total_requests = results['catalogs_requests'] + results['parsing_status_requests']
    total_successes = results['catalogs_successes'] + results['parsing_status_successes']
    total_failures = results['catalogs_failures'] + results['parsing_status_failures']
    
    print(f"\n📈 OVERALL:")
    print(f"   Total Requests: {total_requests}")
    print(f"   Total Successes: {total_successes}")
    print(f"   Total Failures: {total_failures}")
    print(f"   Overall Success Rate: {total_successes/total_requests*100:.1f}%")
    print(f"   Requests/Second: {total_requests/results['total_time']:.1f}")
    
    # Show errors if any
    if results["errors"]:
        print(f"\n❌ ERRORS ({len(results['errors'])}):")
        for error in results["errors"][:10]:  # Show first 10 errors
            print(f"   {error}")
        if len(results["errors"]) > 10:
            print(f"   ... and {len(results['errors']) - 10} more")
    
    # Determine success
    success_rate = total_successes / total_requests * 100
    if success_rate >= 95:
        print(f"\n✅ TEST PASSED: {success_rate:.1f}% success rate")
        print("   High-frequency endpoints are working correctly!")
        return True
    else:
        print(f"\n❌ TEST FAILED: {success_rate:.1f}% success rate")
        print("   Connection pool issues may still exist")
        return False

async def test_concurrent_load():
    """Test multiple concurrent clients polling the endpoints"""
    print(f"\n🧪 TESTING CONCURRENT LOAD (3 clients)")
    print("=" * 60)
    
    async def client_worker(client_id: int):
        """Simulate a frontend client polling the API"""
        successes = 0
        failures = 0
        
        async with aiohttp.ClientSession() as session:
            for i in range(10):  # 10 requests per client
                try:
                    # Alternate between endpoints
                    endpoint = "/catalogs" if i % 2 == 0 else "/parsing_status"
                    async with session.get(f"{API_BASE}{endpoint}") as response:
                        if response.status == 200:
                            successes += 1
                        else:
                            failures += 1
                except Exception:
                    failures += 1
                
                await asyncio.sleep(0.1)  # 10 requests per second per client
        
        return {"client_id": client_id, "successes": successes, "failures": failures}
    
    # Run 3 concurrent clients
    tasks = [client_worker(i) for i in range(3)]
    results = await asyncio.gather(*tasks)
    
    total_successes = sum(r["successes"] for r in results)
    total_failures = sum(r["failures"] for r in results)
    total_requests = total_successes + total_failures
    
    print(f"📊 CONCURRENT LOAD RESULTS:")
    for result in results:
        success_rate = result["successes"] / (result["successes"] + result["failures"]) * 100
        print(f"   Client {result['client_id']}: {result['successes']}/{result['successes'] + result['failures']} ({success_rate:.1f}%)")
    
    overall_success_rate = total_successes / total_requests * 100
    print(f"   Overall: {total_successes}/{total_requests} ({overall_success_rate:.1f}%)")
    
    if overall_success_rate >= 95:
        print("✅ Concurrent load test PASSED")
        return True
    else:
        print("❌ Concurrent load test FAILED")
        return False

async def main():
    """Main test function"""
    print("🚀 TESTING HIGH-FREQUENCY ENDPOINT OPTIMIZATIONS")
    print("=" * 80)
    print("⚠️  Make sure the API server is running on http://localhost:6969")
    print("=" * 80)
    
    try:
        # Test 1: High-frequency polling
        test1_success = await test_endpoint_performance()
        
        # Test 2: Concurrent load
        test2_success = await test_concurrent_load()
        
        print("\n" + "=" * 80)
        print("📋 FINAL RESULTS:")
        print(f"   High-frequency polling test: {'✅ PASS' if test1_success else '❌ FAIL'}")
        print(f"   Concurrent load test: {'✅ PASS' if test2_success else '❌ FAIL'}")
        
        if test1_success and test2_success:
            print("\n🎉 ALL TESTS PASSED - Endpoint optimizations are working!")
            print("   The frontend can now poll these endpoints without connection pool issues.")
        else:
            print("\n⚠️ Some tests failed - Connection pool issues may persist")
            
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
