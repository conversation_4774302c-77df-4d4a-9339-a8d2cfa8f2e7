"""
Storage Interface - Abstract base class for all storage operations
Provides a unified interface for local and cloud storage backends
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Union
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class StorageInterface(ABC):
    """Abstract interface for all storage operations"""
    
    @abstractmethod
    def upload_file(self, local_path: Union[str, Path], cloud_path: str) -> bool:
        """
        Upload a file from local path to cloud storage
        
        Args:
            local_path: Path to local file
            cloud_path: Destination path in cloud storage (e.g., "catalogs/file.pdf")
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    def download_file(self, cloud_path: str, local_path: Union[str, Path]) -> bool:
        """
        Download a file from cloud storage to local path
        
        Args:
            cloud_path: Source path in cloud storage
            local_path: Destination local path
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    def file_exists(self, cloud_path: str) -> bool:
        """
        Check if a file exists in cloud storage
        
        Args:
            cloud_path: Path to check in cloud storage
            
        Returns:
            True if file exists, False otherwise
        """
        pass
    
    @abstractmethod
    def delete_file(self, cloud_path: str) -> bool:
        """
        Delete a file from cloud storage
        
        Args:
            cloud_path: Path to file in cloud storage
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    def list_files(self, prefix: str = "") -> List[str]:
        """
        List files in cloud storage with optional prefix filter
        
        Args:
            prefix: Optional prefix to filter files (e.g., "catalogs/")
            
        Returns:
            List of file paths
        """
        pass
    
    @abstractmethod
    def get_file_url(self, cloud_path: str, expires_in: int = 3600) -> Optional[str]:
        """
        Get a public URL for a file in cloud storage
        
        Args:
            cloud_path: Path to file in cloud storage
            expires_in: URL expiration time in seconds (default: 1 hour)
            
        Returns:
            Public URL string or None if failed
        """
        pass
    
    @abstractmethod
    def get_file_size(self, cloud_path: str) -> Optional[int]:
        """
        Get the size of a file in cloud storage
        
        Args:
            cloud_path: Path to file in cloud storage
            
        Returns:
            File size in bytes or None if file doesn't exist
        """
        pass
    
    @abstractmethod
    def copy_file(self, source_path: str, dest_path: str) -> bool:
        """
        Copy a file within cloud storage
        
        Args:
            source_path: Source path in cloud storage
            dest_path: Destination path in cloud storage
            
        Returns:
            True if successful, False otherwise
        """
        pass


class StorageError(Exception):
    """Custom exception for storage operations"""
    pass


class FileNotFoundError(StorageError):
    """Raised when a file is not found in storage"""
    pass


class UploadError(StorageError):
    """Raised when file upload fails"""
    pass


class DownloadError(StorageError):
    """Raised when file download fails"""
    pass
