import TestHero from '@/components/test/TestHero';
import TestQuerySection from '@/components/test/TestQuerySection';
import TestCatalogList from '@/components/test/TestCatalogList';

export const metadata = {
  title: 'Test Playground - Tilbudsjægeren',
  description: 'Experimental UI features and animations playground',
  robots: {
    index: false, // Don't index the test page
    follow: false
  }
};

export default function TestPage() {
  return (
    <main className="max-w-5xl mx-auto px-4 py-8 space-y-16">
      {/* Test Banner */}
      <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white p-4 rounded-lg text-center">
        <h1 className="text-xl font-bold">🧪 Experimental Test Site</h1>
        <p className="text-sm opacity-90">Playground for new UI features and animations</p>
      </div>
      
      <TestHero />
      <TestQuerySection />
      <TestCatalogList />
    </main>
  );
}
