'use client';

import { useAuth } from '@/lib/auth';
import Image from 'next/image';
import Link from 'next/link';

export default function Header() {
  const { user, logout } = useAuth();

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 max-w-screen-2xl items-center justify-between">
        {/* Logo */}
        <div className="w-40 h-16 relative pl-6">
          <Link href="/">
            <Image
              src="/logos/tilbudsjaegeren-title-svg.svg"
              alt="Tilbudsjægeren"
              fill
              className="object-contain"
              priority
            />
          </Link>
        </div>

        {/* Auth Buttons */}
        <div className="flex items-center space-x-4">
          {user ? (
            // User is logged in
            <>
              {user.is_admin && (
                <Link
                  href="/admin"
                  className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900"
                >
                  Admin
                </Link>
              )}
              <button
                onClick={logout}
                className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                Log ud
              </button>
            </>
          ) : (
            // User is not logged in
            <>
              <Link
                href="/login"
                className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                Log ind
              </Link>
              <Link
                href="/signup"
                className="px-4 py-2 text-sm font-medium text-white bg-sky-500 rounded-md hover:bg-sky-600"
              >
                Opret konto
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
}
