'use client';

import React, { useEffect } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageSrc: string;
  imageAlt: string;
  title?: string;
  subtitle?: string;
  images?: Array<{ src: string; alt: string; title?: string }>;
  currentIndex?: number;
  onNavigate?: (index: number) => void;
}

export default function ImageModal({
  isOpen,
  onClose,
  imageSrc,
  imageAlt,
  title,
  subtitle,
  images,
  currentIndex = 0,
  onNavigate
}: ImageModalProps) {
  // Handle escape key and scroll lock
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      const originalOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';

      return () => {
        document.removeEventListener('keydown', handleEscape);
        document.body.style.overflow = originalOverflow;
      };
    }
  }, [isOpen, onClose]);

  // Handle navigation with arrow keys
  useEffect(() => {
    const handleArrowKeys = (e: KeyboardEvent) => {
      if (!images || !onNavigate) return;
      
      if (e.key === 'ArrowLeft' && currentIndex > 0) {
        onNavigate(currentIndex - 1);
      } else if (e.key === 'ArrowRight' && currentIndex < images.length - 1) {
        onNavigate(currentIndex + 1);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleArrowKeys);
    }

    return () => {
      document.removeEventListener('keydown', handleArrowKeys);
    };
  }, [isOpen, currentIndex, images, onNavigate]);

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handlePrevious = () => {
    if (images && onNavigate && currentIndex > 0) {
      onNavigate(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (images && onNavigate && currentIndex < images.length - 1) {
      onNavigate(currentIndex + 1);
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
      onWheel={(e) => {
        // Allow scrolling within the modal content area
        e.stopPropagation();
      }}
    >
      <div className="relative max-w-7xl max-h-[90vh] w-full h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-black/50 backdrop-blur-sm rounded-t-lg">
          <div className="flex-1">
            {title && (
              <h2 className="text-lg font-semibold text-white truncate">{title}</h2>
            )}
            {subtitle && (
              <p className="text-sm text-gray-300 truncate">{subtitle}</p>
            )}
            {images && images.length > 1 && (
              <p className="text-xs text-gray-400 mt-1">
                {currentIndex + 1} of {images.length}
              </p>
            )}
          </div>
          
          {/* Navigation buttons */}
          {images && images.length > 1 && onNavigate && (
            <div className="flex items-center gap-2 mx-4">
              <button
                onClick={handlePrevious}
                disabled={currentIndex === 0}
                className="p-2 rounded-lg bg-white/10 hover:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="Previous image"
              >
                <ChevronLeft className="w-5 h-5 text-white" />
              </button>
              <button
                onClick={handleNext}
                disabled={currentIndex === images.length - 1}
                className="p-2 rounded-lg bg-white/10 hover:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="Next image"
              >
                <ChevronRight className="w-5 h-5 text-white" />
              </button>
            </div>
          )}
          
          <button
            onClick={onClose}
            className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
            title="Close (Esc)"
          >
            <X className="w-5 h-5 text-white" />
          </button>
        </div>

        {/* Image container with zoom */}
        <div className="flex-1 flex items-center justify-center bg-black/30 backdrop-blur-sm rounded-b-lg overflow-auto">
          <div className="relative w-full h-full flex items-center justify-center p-4">
            <img
              src={imageSrc}
              alt={imageAlt}
              className="max-w-full max-h-full object-contain cursor-zoom-in hover:scale-105 transition-transform duration-300"
              style={{
                maxHeight: 'calc(90vh - 120px)',
                minWidth: '60vw',
                width: 'auto'
              }}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/placeholder.png';
              }}
              onClick={(e) => {
                const img = e.target as HTMLImageElement;
                if (img.style.transform === 'scale(2)') {
                  img.style.transform = 'scale(1)';
                  img.style.cursor = 'zoom-in';
                } else {
                  img.style.transform = 'scale(2)';
                  img.style.cursor = 'zoom-out';
                }
              }}
            />
          </div>
        </div>

        {/* Touch/swipe navigation for mobile */}
        {images && images.length > 1 && onNavigate && (
          <>
            {/* Left touch area */}
            <div 
              className="absolute left-0 top-0 w-1/3 h-full flex items-center justify-start pl-4 md:hidden"
              onClick={handlePrevious}
            >
              {currentIndex > 0 && (
                <div className="p-3 rounded-full bg-black/50 backdrop-blur-sm">
                  <ChevronLeft className="w-6 h-6 text-white" />
                </div>
              )}
            </div>
            
            {/* Right touch area */}
            <div 
              className="absolute right-0 top-0 w-1/3 h-full flex items-center justify-end pr-4 md:hidden"
              onClick={handleNext}
            >
              {currentIndex < images.length - 1 && (
                <div className="p-3 rounded-full bg-black/50 backdrop-blur-sm">
                  <ChevronRight className="w-6 h-6 text-white" />
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
