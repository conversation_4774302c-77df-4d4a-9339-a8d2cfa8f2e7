'use client';

import React, { useEffect, useState } from 'react';
import { X, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageSrc: string;
  imageAlt: string;
  title?: string;
  subtitle?: string;
  images?: Array<{ src: string; alt: string; title?: string }>;
  currentIndex?: number;
  onNavigate?: (index: number) => void;
}

export default function ImageModal({
  isOpen,
  onClose,
  imageSrc,
  imageAlt,
  title,
  subtitle,
  images,
  currentIndex = 0,
  onNavigate
}: ImageModalProps) {
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 });

  // Handle window resize for responsive modal
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    // Set initial size
    handleResize();

    // Add resize listener
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Handle escape key and scroll lock
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      const originalOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';

      return () => {
        document.removeEventListener('keydown', handleEscape);
        document.body.style.overflow = originalOverflow;
      };
    }
  }, [isOpen, onClose]);

  // Handle navigation with arrow keys
  useEffect(() => {
    const handleArrowKeys = (e: KeyboardEvent) => {
      if (!images || !onNavigate) return;
      
      if (e.key === 'ArrowLeft' && currentIndex > 0) {
        onNavigate(currentIndex - 1);
      } else if (e.key === 'ArrowRight' && currentIndex < images.length - 1) {
        onNavigate(currentIndex + 1);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleArrowKeys);
    }

    return () => {
      document.removeEventListener('keydown', handleArrowKeys);
    };
  }, [isOpen, currentIndex, images, onNavigate]);

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handlePrevious = () => {
    if (images && onNavigate && currentIndex > 0) {
      onNavigate(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (images && onNavigate && currentIndex < images.length - 1) {
      onNavigate(currentIndex + 1);
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-[9999] p-6"
      onClick={handleBackdropClick}
      onWheel={(e) => {
        // Allow scrolling within the modal content area
        e.stopPropagation();
      }}
    >
      <div className="relative max-w-[95vw] max-h-[95vh] w-full h-full flex flex-col bg-white rounded-xl shadow-2xl overflow-hidden"
           style={{ minHeight: '60vh' }}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200">
          <div className="flex-1">
            {title && (
              <h2 className="text-lg font-semibold text-gray-900 truncate">{title}</h2>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 truncate">{subtitle}</p>
            )}
            {images && images.length > 1 && (
              <p className="text-xs text-gray-500 mt-1">
                {currentIndex + 1} of {images.length}
              </p>
            )}
            <p className="text-xs text-gray-400 mt-1">
              Click to zoom • Drag to pan • Scroll to zoom • Double-click to reset
            </p>
          </div>
          
          {/* Navigation buttons */}
          {images && images.length > 1 && onNavigate && (
            <div className="flex items-center gap-2 mx-4">
              <button
                onClick={handlePrevious}
                disabled={currentIndex === 0}
                className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="Previous image"
              >
                <ChevronLeft className="w-5 h-5 text-gray-600" />
              </button>
              <button
                onClick={handleNext}
                disabled={currentIndex === images.length - 1}
                className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="Next image"
              >
                <ChevronRight className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          )}
          
          <button
            onClick={onClose}
            className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
            title="Close (Esc)"
          >
            <X className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Image container with smart zoom and pan */}
        <div className="flex-1 bg-gray-100 overflow-hidden relative">
          <TransformWrapper
            initialScale={1}
            minScale={0.5}
            maxScale={5}
            centerOnInit={true}
            wheel={{ step: 0.1 }}
            pinch={{ step: 5 }}
            doubleClick={{ mode: 'reset' }}
            panning={{
              velocityDisabled: false,
              lockAxisX: false,
              lockAxisY: false
            }}
          >
            {({ zoomIn, zoomOut, resetTransform, centerView }) => (
              <>
                {/* Zoom controls */}
                <div className="absolute top-4 right-4 z-10 flex flex-col gap-2">
                  <button
                    onClick={() => zoomIn()}
                    className="p-2 bg-white/90 hover:bg-white rounded-lg shadow-lg transition-colors"
                    title="Zoom In"
                  >
                    <ZoomIn className="w-5 h-5 text-gray-700" />
                  </button>
                  <button
                    onClick={() => zoomOut()}
                    className="p-2 bg-white/90 hover:bg-white rounded-lg shadow-lg transition-colors"
                    title="Zoom Out"
                  >
                    <ZoomOut className="w-5 h-5 text-gray-700" />
                  </button>
                  <button
                    onClick={() => resetTransform()}
                    className="p-2 bg-white/90 hover:bg-white rounded-lg shadow-lg transition-colors"
                    title="Reset Zoom"
                  >
                    <RotateCcw className="w-5 h-5 text-gray-700" />
                  </button>
                </div>

                <TransformComponent
                  wrapperClass="w-full h-full flex items-center justify-center p-6"
                  contentClass="max-w-full max-h-full"
                >
                  <img
                    src={imageSrc}
                    alt={imageAlt}
                    className="max-w-full max-h-full object-contain shadow-lg rounded-lg"
                    style={{
                      maxHeight: 'calc(95vh - 160px)',
                      maxWidth: '100%',
                      width: 'auto',
                      height: 'auto'
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      console.error('Failed to load image:', imageSrc);
                      target.src = '/placeholder.png';
                    }}
                    onLoad={() => {
                      console.log('Image loaded successfully:', imageSrc);
                    }}
                    draggable={false}
                  />
                </TransformComponent>
              </>
            )}
          </TransformWrapper>
        </div>

        {/* Touch/swipe navigation for mobile */}
        {images && images.length > 1 && onNavigate && (
          <>
            {/* Left touch area */}
            <div 
              className="absolute left-0 top-0 w-1/3 h-full flex items-center justify-start pl-4 md:hidden"
              onClick={handlePrevious}
            >
              {currentIndex > 0 && (
                <div className="p-3 rounded-full bg-black/50 backdrop-blur-sm">
                  <ChevronLeft className="w-6 h-6 text-white" />
                </div>
              )}
            </div>
            
            {/* Right touch area */}
            <div 
              className="absolute right-0 top-0 w-1/3 h-full flex items-center justify-end pr-4 md:hidden"
              onClick={handleNext}
            >
              {currentIndex < images.length - 1 && (
                <div className="p-3 rounded-full bg-black/50 backdrop-blur-sm">
                  <ChevronRight className="w-6 h-6 text-white" />
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
