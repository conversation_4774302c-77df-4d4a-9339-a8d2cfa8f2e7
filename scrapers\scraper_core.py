# File: scrapers/scraper_core.py

import asyncio
import logging
import re
import unicodedata
from pathlib import Path
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin

import aiohttp
import sys # For StreamHandler sys.stderr
from storage_factory import StorageFactory
from playwright.async_api import (
    async_playwright,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    Playwright,
    TimeoutError as PlaywrightTimeoutError,
    <PERSON><PERSON><PERSON> as PlaywrightError
)

# Basic logging configuration for the module
# Individual scrapers can get their own logger instance.
core_logger = logging.getLogger(__name__)

# Reduce <PERSON><PERSON>'s own logger verbosity
playwright_logger = logging.getLogger('playwright')
playwright_logger.setLevel(logging.WARNING) # Or logging.ERROR to be even quieter

def sanitize_filename(name: str) -> str:
    """
    Cleans a string to be a valid and safe filename.
    - Normalizes unicode characters to their closest ASCII equivalent.
    - Removes characters that are invalid in Windows/Linux/MacOS filenames.
    - Replaces whitespace with a single underscore.
    - Limits the filename length.
    """
    if not name:
        name = "untitled"
    
    # Normalize unicode characters to ASCII ('ø' in 'Købmand' -> 'o')
    name = unicodedata.normalize('NFKD', name).encode('ascii', 'ignore').decode('ascii')
    
    # Lowercase for consistency
    name = name.lower()
    
    # Remove characters that are invalid in most filesystems
    name = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '', name)
    
    # Replace spaces, dots, and multiple dashes/underscores with a single underscore
    name = re.sub(r'[\s._-]+', '_', name.strip())
    
    # Remove leading/trailing underscores that might result from cleaning
    name = name.strip('_')
    
    # Limit length to avoid filesystem errors
    return name[:100]




class BaseScraper:
    """
    A base class for Playwright scrapers, providing common setup, teardown,
    and utility functions.
    """

    def __init__(self, config: dict):
        self.config: dict = config
        self.store_name: str = config.get('store_name', 'UnknownStore')
        self.base_url: Optional[str] = config.get('base_url')
        self.catalog_list_url: Optional[str] = config.get('catalog_list_url') # URL to start scraping from

        # Output path for downloads, defaults to 'scrapers/downloads' relative to project root
        project_root = Path(__file__).resolve().parent.parent
        self.output_path: Path = project_root / config.get('output_path', 'scrapers/downloads')

        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None  # Primary page for the scraper

        self.logger = logging.getLogger(f"scraper.{self.store_name}")
        log_level_str = config.get('behavior_flags', {}).get('debug_level', 'INFO').upper()
        self.logger.setLevel(log_level_str)

        # Ensure this logger's messages are outputted, regardless of root logger config
        if not self.logger.handlers:  # Add handler only if no handlers are already attached
            handler = logging.StreamHandler(sys.stderr) # Send logs to stderr
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s')
            handler.setFormatter(formatter)
            # The handler's level defaults to NOTSET, so it will process messages based on self.logger's level.
            self.logger.addHandler(handler)
            self.logger.propagate = False # Prevents messages from also going to root logger's handlers.
                                          # Set to True if you want them to go to both, False for only this handler.
            self.logger.debug(f"Dedicated StreamHandler added to logger: {self.logger.name} with level {logging.getLevelName(self.logger.getEffectiveLevel())} and handler {handler.name} with level {logging.getLevelName(handler.level)}")

        self.browser_type_name: str = config.get('behavior_flags', {}).get('browser_type', 'chromium')
        self.headless: bool = config.get('behavior_flags', {}).get('headless', True)
        self.user_agent: Optional[str] = config.get('behavior_flags', {}).get('user_agent')
        self.viewport: Optional[Dict[str, int]] = config.get('behavior_flags', {}).get('viewport')

        self.collected_catalogs: List[Dict[str, Any]] = []

    async def _click_and_capture_pdf(self, page: Page, clickable, *, navigation_timeout: int = 60000, click_timeout: int = 10000) -> Optional[str]:
        """Click a locator|function and capture the resulting PDF URL regardless of whether it opens in a new tab or same tab.

        Returns the PDF URL string, or None if not captured.
        """
        try:
            # Allow both a Locator or a callable performing the click.
            async def _do_click():
                if callable(clickable):
                    await clickable()
                else:
                    await clickable.click(timeout=click_timeout)

            # First try to catch a popup/tab.
            try:
                async with page.context.expect_page(timeout=5000) as popup_info:
                    await _do_click()
                popup_page = await popup_info.value
                await popup_page.wait_for_load_state('load', timeout=navigation_timeout)
                pdf_url = popup_page.url
            except PlaywrightTimeoutError:
                # No popup – assume same-page navigation.
                await _do_click()
                await page.wait_for_url(re.compile(r".*\.pdf(?:\?.*)?$"), timeout=navigation_timeout)
                pdf_url = page.url

            # Basic validation
            if pdf_url and pdf_url.lower().endswith('.pdf'):
                return pdf_url
            return pdf_url  # May still be useful even if missing .pdf extension
        except Exception as e:
            self.logger.error(f"_click_and_capture_pdf: Failed to capture PDF URL – {e}")
            return None

    async def _setup_playwright(self, playwright_instance: Playwright):
        self.logger.debug(f"BASE_SCRAPER._SETUP_PLAYWRIGHT: Entered for {self.store_name}. Playwright instance: {playwright_instance}")
        """
        Initializes Playwright browser and context.
        The playwright_instance is passed from the async_playwright() context manager.
        """
        self.playwright = playwright_instance
        self.logger.debug(f"Setting up Playwright with {self.browser_type_name} (headless: {self.headless})...")

        browser_type = getattr(self.playwright, self.browser_type_name, None)
        if not browser_type:
            self.logger.error(f"Invalid browser type configured: {self.browser_type_name}")
            raise ValueError(f"Invalid browser type: {self.browser_type_name}")

        # Normal desktop mode with human-looking flags
        launch_args = [
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-gpu",
            "--disable-web-security",
            # KEEP images, fonts, CSS, JS for normal browsing
            "--disable-images=false",
            "--disable-javascript=false",
            "--disable-features=VizDisplayCompositor"
        ]
        launch_options = {'headless': self.headless, 'args': launch_args}
        if self.browser_type_name == 'chromium' and self.config.get('behavior_flags', {}).get('chromium_sandbox_disabled', False):
            launch_options['args'] = list(set(launch_options.get('args', [])) | {"--no-sandbox", "--disable-setuid-sandbox"})
            self.logger.info("Chromium sandbox disabled via launch options.")

        browser_channel = self.config.get('behavior_flags', {}).get('browser_channel')
        if browser_channel:
            launch_options['channel'] = browser_channel
            self.logger.info(f"Using browser channel: {browser_channel} from config.")

        try:
            self.browser = await browser_type.launch(**launch_options)
            self.logger.debug(f"{self.browser_type_name.capitalize()} browser launched (Version: {self.browser.version}).")
            self.logger.debug(f"BASE_SCRAPER._SETUP_PLAYWRIGHT: Browser launched successfully. Version: {self.browser.version if self.browser else 'N/A'}")
        except PlaywrightError as e:
            self.logger.debug(f"BASE_SCRAPER._SETUP_PLAYWRIGHT: FAILED to launch {self.browser_type_name} browser: {e}", exc_info=True)
            self.logger.error(f"Failed to launch {self.browser_type_name} browser: {e}", exc_info=True)
            raise

        # Human-looking context options
        context_options = {
            "user_agent": (
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                "AppleWebKit/537.36 (KHTML, like Gecko) "
                "Chrome/126.0.0.0 Safari/537.36"
            ),
            "viewport": {"width": 1920, "height": 1080},
            "locale": "da-DK",
            "timezone_id": "Europe/Copenhagen",
        }

        # Override with config-specific options if provided
        if self.user_agent:
            context_options['user_agent'] = self.user_agent
        if self.viewport:
            context_options['viewport'] = self.viewport
        
        # Set download path for the context
        download_dir = self._ensure_download_dir()
        context_options['accept_downloads'] = True
        # Note: Playwright's `downloads_path` on context creation is tricky and often doesn't work as expected
        # for all scenarios. It's more reliable to use `download.save_as()` later.
        # We still ensure the directory exists.

        self.context = await self.browser.new_context(**context_options)
        self.logger.debug("Browser context created.")

        # 🥷 Inject enhanced stealth script BEFORE any navigation
        stealth_script = """
            // 1. Remove webdriver property
            delete Object.getPrototypeOf(navigator).webdriver;

            // 2. Fake chrome.runtime
            Object.defineProperty(window, 'chrome', {
                writable: true,
                configurable: true,
                value: { runtime: {} }
            });

            // 3. Block window.close() - THE KEY FIX
            const originalClose = window.close;
            window.close = function (...args) {
                console.warn('🚫 window.close() was blocked by stealth script');
                return undefined;
            };
        """
        await self.context.add_init_script(stealth_script)
        self.logger.info("🥷 Enhanced anti-bot stealth script injected - window.close() blocked!")

        # Start trace recording if enabled
        enable_trace = self.config.get('behavior_flags', {}).get('enable_trace', False)
        trace_file = self.config.get('behavior_flags', {}).get('trace_file')
        if enable_trace and trace_file:
            await self.context.tracing.start(
                screenshots=True,
                snapshots=True,
                sources=True
            )
            self.logger.info(f"Playwright trace recording started. Will save to: {trace_file}")
            self._trace_file = trace_file
        else:
            self._trace_file = None

        # Block heavy resources to save memory & bandwidth, but allow CSS and fonts for proper rendering
        async def _route_handler(route):
            if route.request.resource_type in ("image", "media"):
                await route.abort()
            else:
                await route.continue_()
        await self.context.route("**/*", _route_handler)

        self.page = await self.context.new_page()
        self.logger.debug("Initial page created for the scraper.")

        # 🕵️ Add event spy to catch who closes the page
        self.page.on("close", lambda: self.logger.error("💀 Page WAS CLOSED by something!"))
        self.logger.info("🕵️ Page close event spy installed")


    async def _close(self):
        """Closes Playwright resources."""
        self.logger.debug("Closing Playwright resources...")

        # Stop trace recording if it was started
        if hasattr(self, '_trace_file') and self._trace_file and self.context:
            try:
                await self.context.tracing.stop(path=self._trace_file)
                self.logger.info(f"Playwright trace saved to: {self._trace_file}")
            except Exception as e:
                self.logger.warning(f"Error saving trace: {e}")

        # Only close page if explicitly requested (opt-in via config flag)
        auto_close_page = self.config.get('behavior_flags', {}).get('auto_close_page', False)
        if auto_close_page:
            try:
                if self.page and not self.page.is_closed():
                    await self.page.close()
                    self.logger.debug("Page closed (auto_close_page=True).")
            except PlaywrightError as e:
                self.logger.warning(f"Error closing page: {e}")
        else:
            self.logger.debug("Page left open (auto_close_page=False or not set).")

        try:
            if self.context:
                await self.context.close()
                self.logger.debug("Browser context closed.")
        except PlaywrightError as e:
            self.logger.warning(f"Error closing browser context: {e}")

        try:
            if self.browser:
                await self.browser.close()
                self.logger.debug("Browser closed.")
        except PlaywrightError as e:
            self.logger.warning(f"Error closing browser: {e}")
        # Playwright instance itself is managed by the `async with async_playwright()` block in run()

    def _ensure_download_dir(self) -> Path:
        """Ensures the store-specific download directory exists and returns its path."""
        store_download_dir = self.output_path / self._sanitize_filename(self.store_name)
        store_download_dir.mkdir(parents=True, exist_ok=True)
        self.logger.debug(f"Ensured download directory exists: {store_download_dir}")
        return store_download_dir

    def _sanitize_filename(self, name: str) -> str:
        """Cleans a string to be a valid filename."""
        if not name:
            name = "untitled"
        # Remove characters that are invalid in Windows/Linux/MacOS filenames
        name = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '_', name)
        # Replace multiple spaces/underscores with a single underscore
        name = re.sub(r'[\s_]+', '_', name.strip())
        # Limit length to avoid issues (e.g., 100 chars for the base name)
        return name[:100]

    def _parse_dagrofa_week_format(self, title_str: str):
        """
        Parse Dagrofa group (Meny, Spar, Min Købmand) week format like "MENY uge 2925".
        Returns (start_date, end_date) as date objects or None if parsing fails.
        """
        from datetime import datetime, date, timedelta
        import re

        if not title_str:
            return None

        # Normalize to lowercase
        title_str = title_str.lower()

        # Match "uge 2925" format (week 29, year 25)
        match = re.search(r"(?:uge|ug)\s*(\d{2})(\d{2})(?:\D|$)", title_str)
        if not match:
            return None

        week_str = match.group(1)
        year_str = match.group(2)

        try:
            week_number = int(week_str)
            if not (1 <= week_number <= 53):
                return None

            # Handle 2-digit year (25 = 2025)
            year_val = int(year_str)
            if year_val <= 50:
                year = 2000 + year_val
            else:
                year = 1900 + year_val

            # Dagrofa promotional week starts on Friday of (ISO Week WW - 1)
            effective_year = year
            effective_week = week_number - 1

            if effective_week == 0:
                effective_year -= 1
                effective_week = date(effective_year, 12, 28).isocalendar()[1]

            # Get Friday of the effective week as start date
            start_date_obj = datetime.fromisocalendar(effective_year, effective_week, 5).date()
            end_date_obj = start_date_obj + timedelta(days=6)

            return start_date_obj, end_date_obj

        except Exception as e:
            self.logger.error(f"Error parsing Dagrofa week format '{title_str}': {e}")
            return None

    def _parse_danish_date_range(self, date_string: str) -> tuple[str | None, str | None]:
        """
        Parse Danish date ranges into YYYYMMDD format.

        Handles patterns like:
        - "5. - 11. juli" → ("20250705", "20250711")
        - "26. juni - 23. juli" → ("20250626", "20250723")
        - "Gælder fra d. 4. juli til og med d. 10. juli*" → ("20250704", "20250710")

        Returns (valid_from, valid_to) as strings or (None, None) if parsing fails.
        """
        if not date_string or "Unknown_Date" in date_string:
            return None, None

        # Danish month mapping
        danish_months = {
            'januar': 1, 'februar': 2, 'marts': 3, 'april': 4, 'maj': 5, 'juni': 6,
            'juli': 7, 'august': 8, 'september': 9, 'oktober': 10, 'november': 11, 'december': 12
        }

        try:
            from datetime import datetime
            current_year = datetime.now().year

            # Clean up the string - remove common Danish phrases
            cleaned = date_string.lower()
            cleaned = re.sub(r'gælder fra d\.?\s*', '', cleaned)
            cleaned = re.sub(r'til og med d\.?\s*', ' - ', cleaned)
            cleaned = re.sub(r'\*+', '', cleaned)  # Remove asterisks
            cleaned = cleaned.strip()

            # Handle patterns like "5. - 11. juli" or "4. juli - 10. juli"
            if ' - ' in cleaned:
                parts = cleaned.split(' - ')
                start_part = parts[0].strip()
                end_part = parts[1].strip()

                # Parse start date
                start_day = None
                start_month = None

                # Extract day from start part
                day_match = re.search(r'(\d+)\.?', start_part)
                if day_match:
                    start_day = int(day_match.group(1))

                # Find month in start part
                for month_name, month_num in danish_months.items():
                    if month_name in start_part:
                        start_month = month_num
                        break

                # Parse end date
                end_day = None
                end_month = None

                # Extract day from end part
                day_match = re.search(r'(\d+)\.?', end_part)
                if day_match:
                    end_day = int(day_match.group(1))

                # Find month in end part
                for month_name, month_num in danish_months.items():
                    if month_name in end_part:
                        end_month = month_num
                        break

                # If start doesn't have month, use end month
                if start_month is None and end_month is not None:
                    start_month = end_month

                if start_day and start_month and end_day and end_month:
                    valid_from = f"{current_year}{start_month:02d}{start_day:02d}"
                    valid_to = f"{current_year}{end_month:02d}{end_day:02d}"
                    return valid_from, valid_to

        except (ValueError, IndexError) as e:
            self.logger.warning(f"Could not parse date string '{date_string}': {e}")

        return None, None

    async def _handle_cookies(self, page: Page, cookie_selectors: List[str], timeout_ms: int = 10000) -> bool:
        """
        Attempts to find and click a cookie consent button.
        Iterates through a list of selectors.
        """
        if not cookie_selectors:
            self.logger.debug("No cookie selectors provided for _handle_cookies.")
            return False

        for selector in cookie_selectors:
            try:
                self.logger.debug(f"Looking for cookie button with selector: '{selector}'")
                cookie_button = page.locator(selector).first # Use .first to avoid ambiguity if multiple match
                
                # Wait for the button to be potentially visible and clickable
                await cookie_button.wait_for(state="visible", timeout=timeout_ms / len(cookie_selectors)) # Split timeout
                
                if await cookie_button.is_visible(): # Double check visibility
                    self.logger.debug(f"Cookie button '{selector}' found and visible. Attempting click.")
                    await cookie_button.click(timeout=5000) # Shorter timeout for the click itself
                    self.logger.debug(f"Clicked cookie button: '{selector}'. Pausing briefly.")
                    await page.wait_for_timeout(self.config.get('behavior_flags', {}).get('wait_after_cookie_ms', 2000))
                    return True # Successfully clicked
            except PlaywrightTimeoutError:
                self.logger.debug(f"Timed out waiting for cookie button '{selector}' or during click.")
            except Exception as e:
                self.logger.warning(f"Error interacting with cookie button '{selector}': {e}")
        
        self.logger.warning(f"Could not find or click any cookie button using selectors: {cookie_selectors}")
        return False

    async def _navigate_to_url(self, page: Page, url: str, wait_until_strategy: str = 'domcontentloaded') -> bool:
        """Navigates to a given URL with error handling and Cloudflare retry logic."""
        self.logger.debug(f"Navigating to: {url}")
        nav_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)

        # Retry policy for Cloudflare 403 responses
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.logger.debug(f"Navigation attempt {attempt + 1}/{max_retries} to {url}")
                response = await page.goto(url, wait_until=wait_until_strategy, timeout=nav_timeout)

                if response:
                    status_code = response.status
                    self.logger.debug(f"Navigation response: Status {status_code}")

                    # Handle Cloudflare 403 with retry
                    if status_code == 403:
                        if attempt < max_retries - 1:  # Don't sleep on last attempt
                            backoff_delay = 2 ** attempt
                            self.logger.warning(f"Received 403 (Cloudflare block) on attempt {attempt + 1}. Retrying in {backoff_delay}s...")
                            await asyncio.sleep(backoff_delay)
                            continue
                        else:
                            self.logger.error(f"Cloudflare still blocking after {max_retries} attempts")
                            return False

                    # Success for any other status code
                    self.logger.debug(f"Successfully navigated to {url}. Status: {status_code}")
                    return True
                else:
                    self.logger.warning(f"Navigation to {url} completed but no response object received.")
                    # This case is unusual but Playwright might allow it. Treat as success for now if no error.
                    return True

            except PlaywrightTimeoutError:
                self.logger.error(f"Timeout navigating to {url} after {nav_timeout}ms on attempt {attempt + 1}")
                if attempt == max_retries - 1:  # Last attempt
                    return False
                # Retry on timeout as well
                backoff_delay = 2 ** attempt
                self.logger.info(f"Retrying navigation after timeout in {backoff_delay}s...")
                await asyncio.sleep(backoff_delay)
                continue
            except PlaywrightError as e:
                self.logger.error(f"Playwright error navigating to {url} on attempt {attempt + 1}: {e}")
                return False
            except Exception as e:
                self.logger.error(f"Unexpected error navigating to {url} on attempt {attempt + 1}: {e}", exc_info=True)
                return False

        return False
        
    def _resolve_url(self, page: Page, target_url: str) -> str:
        """Resolves a potentially relative URL against the page's current URL or the base_url."""
        if target_url.startswith(('http://', 'https://')):
            return target_url
        current_page_url = page.url
        if current_page_url and current_page_url != "about:blank":
            return urljoin(current_page_url, target_url)
        if self.base_url:
            return urljoin(self.base_url, target_url)
        self.logger.warning(f"Could not resolve relative URL '{target_url}' as base_url is not set and page URL is blank.")
        return target_url # Return as is, likely to fail navigation

    async def _download_file_content(self, url: str) -> Optional[bytes]:
        """
        Asynchronously downloads file content from a URL using aiohttp.
        """
        self.logger.debug(f"Attempting to download file content from: {url}")
        try:
            async with aiohttp.ClientSession() as session:
                # Set a reasonable timeout for the request
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=90)) as response:
                    response.raise_for_status() # Raises an exception for 4xx/5xx status codes
                    content = await response.read()
                    self.logger.info(f"Successfully downloaded {len(content)} bytes from {url}")
                    return content
        except asyncio.TimeoutError:
            self.logger.error(f"Timeout error while trying to download from {url}")
            return None
        except aiohttp.ClientError as e:
            self.logger.error(f"Aiohttp client error downloading from {url}: {e}", exc_info=True)
            return None
        except Exception as e:
            self.logger.error(f"An unexpected error occurred during file download from {url}: {e}", exc_info=True)
            return None

    def upload_pdf_to_cloud(self, local_pdf_path: str, catalog_info: Dict[str, Any]) -> Optional[str]:
        """
        Upload a downloaded PDF to Google Cloud Storage

        Args:
            local_pdf_path: Path to the local PDF file
            catalog_info: Dictionary containing catalog metadata

        Returns:
            Cloud storage path if successful, None otherwise
        """
        try:
            # Extract date information for cloud path
            valid_from = catalog_info.get('valid_from', '')
            valid_to = catalog_info.get('valid_to', '')

            # If dates are missing, try to parse from raw_date_info
            if not valid_from or not valid_to:
                raw_date_info = catalog_info.get('raw_date_info', '')
                if raw_date_info:
                    # Try Dagrofa week format first for Meny, Spar, Min Købmand
                    if self.store_name.lower() in ["meny", "spar", "min købmand"]:
                        parsed_dates = self._parse_dagrofa_week_format(raw_date_info)
                        if parsed_dates:
                            valid_from = parsed_dates[0].strftime('%Y%m%d')
                            valid_to = parsed_dates[1].strftime('%Y%m%d')
                            self.logger.info(f"Parsed Dagrofa week dates from '{raw_date_info}': {valid_from} - {valid_to}")

                    # Fallback to regular Danish date parsing
                    if not valid_from or not valid_to:
                        parsed_from, parsed_to = self._parse_danish_date_range(raw_date_info)
                        if parsed_from and parsed_to:
                            valid_from = parsed_from
                            valid_to = parsed_to
                            self.logger.info(f"Parsed dates from '{raw_date_info}': {valid_from} - {valid_to}")

            # Upload to cloud storage using storage factory
            storage = StorageFactory.create_storage()

            # Generate cloud storage path: catalogs/storename_validfrom_validto.pdf
            from datetime import datetime
            if isinstance(valid_from, str):
                valid_from_str = valid_from.replace('-', '')
            else:
                valid_from_str = valid_from.strftime('%Y%m%d') if valid_from else datetime.now().strftime('%Y%m%d')

            if isinstance(valid_to, str):
                valid_to_str = valid_to.replace('-', '')
            else:
                valid_to_str = valid_to.strftime('%Y%m%d') if valid_to else datetime.now().strftime('%Y%m%d')

            filename = f"{self.store_name.lower()}_{valid_from_str}_{valid_to_str}.pdf"

            # Use StoragePaths for consistent path construction
            from storage_factory import StoragePaths
            from datetime import datetime

            # Convert string dates back to datetime objects for StoragePaths
            try:
                if isinstance(valid_from, str):
                    valid_from_date = datetime.strptime(valid_from, '%Y-%m-%d').date()
                else:
                    valid_from_date = valid_from

                if isinstance(valid_to, str):
                    valid_to_date = datetime.strptime(valid_to, '%Y-%m-%d').date()
                else:
                    valid_to_date = valid_to

                # Generate proper cloud storage path using StoragePaths
                country_code = StoragePaths.detect_country_code(self.store_name)
                date_period = StoragePaths.generate_date_period(valid_from_date, valid_to_date)
                cloud_path = StoragePaths.catalog_path(
                    filename=filename,
                    country_code=country_code,
                    store_name=self.store_name,
                    date_period=date_period
                )
            except Exception as e:
                self.logger.warning(f"Failed to generate StoragePaths, falling back to legacy: {e}")
                cloud_path = f"catalogs/{filename}"

            # Upload file to cloud storage
            success = storage.upload_file(local_pdf_path, cloud_path)
            cloud_path = cloud_path if success else None

            if cloud_path:
                self.logger.info(f"✅ Uploaded PDF to cloud storage: {cloud_path}")
                # Update catalog info with cloud path
                catalog_info['cloud_pdf_path'] = cloud_path
                return cloud_path
            else:
                self.logger.warning(f"❌ Failed to upload PDF to cloud storage: {local_pdf_path}")
                return None

        except Exception as e:
            self.logger.error(f"❌ Error uploading PDF to cloud storage: {e}")
            return None

    async def scrape_catalogs(self) -> List[Dict[str, Any]]:
        """
        Abstract method for scraping catalog data.
        Subclasses MUST implement this method.

        This method should populate `self.collected_catalogs` with dictionaries,
        each representing a scraped catalog. Required keys for each dict:
        - 'store_name': str
        - 'title': str (e.g., "Uge 42 Avis")
        - 'raw_date_info': str (the text extracted for date parsing, e.g., "22. okt - 28. okt" or "Uge 42/2024")
        - 'pdf_url': str (the direct URL of the PDF if found)
        - 'local_path': str (absolute path to the downloaded PDF file)

        Returns:
            The list of collected catalog dictionaries.
        """
        self.logger.error(
            f"scrape_catalogs() method not implemented in {self.__class__.__name__}. "
            "Subclasses must override this method."
        )
        raise NotImplementedError("Subclasses must implement scrape_catalogs()")

    async def run(self) -> List[Dict[str, Any]]:
        self.logger.debug(f"BASE_SCRAPER.RUN: Entered for {self.store_name}. Current page state: {'Page exists' if self.page else 'No page'}, Closed: {self.page.is_closed() if self.page else 'N/A'}")
        """
        Main public method to run the scraper.
        Initializes Playwright, calls the scrape_catalogs method (implemented by subclass),
        and handles cleanup.
        """
        self.logger.info(f"Starting run for {self.store_name} scraper...")
        self.collected_catalogs = [] # Reset for this run

        try:
            async with async_playwright() as p_instance:
                self.logger.debug(f"BASE_SCRAPER.RUN: About to call _setup_playwright. Playwright instance from context: {p_instance}")
                await self._setup_playwright(p_instance)
                self.logger.debug(f"BASE_SCRAPER.RUN: _setup_playwright completed. Browser: {self.browser}, Context: {self.context}, Page: {self.page}")
                if self.page:
                    self.logger.debug(f"BASE_SCRAPER.RUN: Page is_closed after setup: {self.page.is_closed()}")
                    if not self.browser or not self.context or not self.page:
                        self.logger.error("Playwright setup failed to initialize browser, context, or page. Aborting.")
                        return []
                    
                    self.collected_catalogs = await self.scrape_catalogs() # Call the subclass's implementation

        except PlaywrightError as pe:
            self.logger.error(f"A critical Playwright error occurred during run for {self.store_name}: {pe}", exc_info=True)
        except NotImplementedError:
            self.logger.error(f"Scraping logic not implemented for {self.store_name}. Please implement scrape_catalogs().")
        except Exception as e:
            self.logger.error(f"An unexpected error occurred during the run for {self.store_name}: {e}", exc_info=True)
        finally:
            await self._close()

        self.logger.info(f"Finished run for {self.store_name}. Collected {len(self.collected_catalogs)} catalog(s).")
        return self.collected_catalogs