[{"input": {"unit_string": "144 g / 6 stk.", "price": 32.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "144 g / 6 stk."}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "144 g / 6 stk.", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "750 ml.", "price": 15.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "750 ml."}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "750 ml.", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "125 g. 10 stk.", "price": 25.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "125 g. 10 stk."}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "125 g. 10 stk.", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "175 g.", "price": 12.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "175 g."}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "175 g.", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "900 ml.", "price": 18.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "900 ml."}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "900 ml.", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "330-720 ml.", "price": 20.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "330-720 ml."}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "330-720 ml.", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "stk.", "price": 10.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "stk."}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "stk.", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "g", "price": 5.0}, "comparison": {"identical": false, "differences": [{"field": "price_per_base_unit", "current": null, "suggested": 5000.0}, {"field": "base_unit_type", "current": null, "suggested": "kg"}, {"field": "price_per_base_unit_min", "current": null, "suggested": 5000.0}, {"field": "price_per_base_unit_max", "current": null, "suggested": 5000.0}], "current": {"quantity": 1.0, "unit_type": "g", "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": 1.0, "quantity_min": 1.0, "quantity_max": 1.0, "unit_type": "g", "price_per_base_unit": 5000.0, "price_per_base_unit_min": 5000.0, "price_per_base_unit_max": 5000.0, "base_unit_type": "kg"}, "improvement_type": "price_calculation_improvement"}}, {"input": {"unit_string": "42 stk.", "price": 30.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "42 stk."}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "42 stk.", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "200-300 ml", "price": 15.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "ml"}, {"field": "base_unit_type", "current": null, "suggested": "l"}, {"field": "price_per_base_unit_min", "current": null, "suggested": 50.0}, {"field": "price_per_base_unit_max", "current": null, "suggested": 75.0}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": 200.0, "quantity_max": 300.0, "unit_type": "ml", "price_per_base_unit": null, "price_per_base_unit_min": 50.0, "price_per_base_unit_max": 75.0, "base_unit_type": "l"}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "150-350 stk.", "price": 25.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "150-350 stk."}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "150-350 stk.", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "500-750 ml", "price": 22.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "ml"}, {"field": "base_unit_type", "current": null, "suggested": "l"}, {"field": "price_per_base_unit_min", "current": null, "suggested": 29.33}, {"field": "price_per_base_unit_max", "current": null, "suggested": 44.0}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": 500.0, "quantity_max": 750.0, "unit_type": "ml", "price_per_base_unit": null, "price_per_base_unit_min": 29.33, "price_per_base_unit_max": 44.0, "base_unit_type": "l"}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "40-70 stk.", "price": 18.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "40-70 stk."}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "40-70 stk.", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": "500g", "price": 20.0}, "comparison": {"identical": false, "differences": [{"field": "price_per_base_unit_min", "current": null, "suggested": 40.0}, {"field": "price_per_base_unit_max", "current": null, "suggested": 40.0}], "current": {"quantity": 500.0, "unit_type": "g", "price_per_base_unit": 40.0, "base_unit_type": "kg"}, "suggested": {"quantity": 500.0, "quantity_min": 500.0, "quantity_max": 500.0, "unit_type": "g", "price_per_base_unit": 40.0, "price_per_base_unit_min": 40.0, "price_per_base_unit_max": 40.0, "base_unit_type": "kg"}, "improvement_type": "different"}}, {"input": {"unit_string": "1 kg", "price": 35.0}, "comparison": {"identical": false, "differences": [{"field": "price_per_base_unit_min", "current": null, "suggested": 35.0}, {"field": "price_per_base_unit_max", "current": null, "suggested": 35.0}], "current": {"quantity": 1.0, "unit_type": "kg", "price_per_base_unit": 35.0, "base_unit_type": "kg"}, "suggested": {"quantity": 1.0, "quantity_min": 1.0, "quantity_max": 1.0, "unit_type": "kg", "price_per_base_unit": 35.0, "price_per_base_unit_min": 35.0, "price_per_base_unit_max": 35.0, "base_unit_type": "kg"}, "improvement_type": "different"}}, {"input": {"unit_string": "1.5 l", "price": 12.0}, "comparison": {"identical": false, "differences": [{"field": "quantity", "current": 15.0, "suggested": 1.5}, {"field": "price_per_base_unit", "current": 0.8, "suggested": 8.0}, {"field": "price_per_base_unit_min", "current": null, "suggested": 8.0}, {"field": "price_per_base_unit_max", "current": null, "suggested": 8.0}], "current": {"quantity": 15.0, "unit_type": "l", "price_per_base_unit": 0.8, "base_unit_type": "l"}, "suggested": {"quantity": 1.5, "quantity_min": 1.5, "quantity_max": 1.5, "unit_type": "l", "price_per_base_unit": 8.0, "price_per_base_unit_min": 8.0, "price_per_base_unit_max": 8.0, "base_unit_type": "l"}, "improvement_type": "different"}}, {"input": {"unit_string": "2x100g", "price": 15.0}, "comparison": {"identical": false, "differences": [{"field": "price_per_base_unit_min", "current": null, "suggested": 75.0}, {"field": "price_per_base_unit_max", "current": null, "suggested": 75.0}], "current": {"quantity": 200.0, "unit_type": "g", "price_per_base_unit": 75.0, "base_unit_type": "kg"}, "suggested": {"quantity": 200.0, "quantity_min": 200.0, "quantity_max": 200.0, "unit_type": "g", "price_per_base_unit": 75.0, "price_per_base_unit_min": 75.0, "price_per_base_unit_max": 75.0, "base_unit_type": "kg"}, "improvement_type": "different"}}, {"input": {"unit_string": "3 x 75 cl", "price": 25.0}, "comparison": {"identical": false, "differences": [{"field": "price_per_base_unit_min", "current": null, "suggested": 11.11}, {"field": "price_per_base_unit_max", "current": null, "suggested": 11.11}], "current": {"quantity": 225.0, "unit_type": "cl", "price_per_base_unit": 11.11, "base_unit_type": "l"}, "suggested": {"quantity": 225.0, "quantity_min": 225.0, "quantity_max": 225.0, "unit_type": "cl", "price_per_base_unit": 11.11, "price_per_base_unit_min": 11.11, "price_per_base_unit_max": 11.11, "base_unit_type": "l"}, "improvement_type": "different"}}, {"input": {"unit_string": "pakke", "price": 8.0}, "comparison": {"identical": false, "differences": [{"field": "base_unit_type", "current": null, "suggested": "pakke"}], "current": {"quantity": 1.0, "unit_type": "pakke", "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": 1.0, "quantity_min": 1.0, "quantity_max": 1.0, "unit_type": "pakke", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": "pakke"}, "improvement_type": "different"}}, {"input": {"unit_string": "", "price": 10.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": ""}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}, {"input": {"unit_string": null, "price": 10.0}, "comparison": {"identical": true, "differences": [], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": null, "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "identical"}}, {"input": {"unit_string": "invalid unit", "price": 10.0}, "comparison": {"identical": false, "differences": [{"field": "unit_type", "current": null, "suggested": "invalid unit"}], "current": {"quantity": null, "unit_type": null, "price_per_base_unit": null, "base_unit_type": null}, "suggested": {"quantity": null, "quantity_min": null, "quantity_max": null, "unit_type": "invalid unit", "price_per_base_unit": null, "price_per_base_unit_min": null, "price_per_base_unit_max": null, "base_unit_type": null}, "improvement_type": "major_improvement"}}]