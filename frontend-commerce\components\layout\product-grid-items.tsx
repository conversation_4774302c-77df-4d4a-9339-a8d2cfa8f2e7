import Grid from 'components/grid';
import { GridTileImage } from 'components/grid/tile';
import { BackendProduct } from 'lib/backend';
import Link from 'next/link';

export default function ProductGridItems({ products }: { products: BackendProduct[] }) {
  return (
    <>
      {products.map((product) => (
        <Grid.Item key={product.id} className="animate-fadeIn">
          <Link
            className="relative inline-block h-full w-full"
            href="#"
          >
            <GridTileImage
              alt={product.name}
              label={{
                title: product.name,
                amount: product.price.toFixed(2),
                currencyCode: 'DKK'
              }}
              src={product.image_path ?? '/placeholder.png'}
              fill
              sizes="(min-width: 768px) 33vw, (min-width: 640px) 50vw, 100vw"
            />
          </Link>
        </Grid.Item>
      ))}
    </>
  );
}
