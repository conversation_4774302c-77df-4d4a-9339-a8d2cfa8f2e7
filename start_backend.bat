@echo off
echo Activating virtual environment...
call .venv\Scripts\activate.bat

echo Checking/installing requirements...
python -m pip install -r requirements.txt

echo Ensuring database schema is up to date...
alembic upgrade head

echo Initializing database with default data (if needed)...
python db_init.py

echo Starting Danish Supermarket Offers application (with auto-reload)...
echo Access the web interface at http://localhost:8001

uvicorn api:app --reload --host 0.0.0.0 --port 6969

pause 