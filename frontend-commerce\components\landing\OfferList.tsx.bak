import React from 'react';

const OfferList = () => {
  return <div>Placeholder</div>;
};

export default OfferList;
import Image from 'next/image';
import { BackendProduct, BackendCatalog } from 'lib/backend';
import { buildThumbnailUrl, getStoreLogoUrl } from 'lib/ui-helpers';

interface OfferListProps {
  offers: BackendProduct[];
  catalogs: Pick<BackendCatalog, 'id' | 'store_name'>[];
  loading?: boolean;
}

const OfferList: React.FC<OfferListProps> = ({ offers, catalogs, loading }) => {
  const getCatalogInfo = (catalogId: number) => {
    return catalogs.find(c => c.id === catalogId);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {Array.from({ length: 10 }).map((_, index) => (
          <div key={index} className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/60 rounded-lg overflow-hidden shadow-lg animate-pulse">
            <div className="h-48 bg-gray-700/50"></div>
            <div className="p-4">
              <div className="h-6 bg-gray-700/50 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-700/50 rounded w-1/2 mb-4"></div>
              <div className="h-6 bg-gray-700/50 rounded w-1/3"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!offers || offers.length === 0) {
    return (
      <div className="text-center py-12 text-gray-400">
        <p>No offers found for your query.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      {offers.map((offer) => {
        const catalogInfo = getCatalogInfo(offer.catalog_id);
        const storeName = catalogInfo?.store_name;
        const storeLogo = getStoreLogoUrl(storeName);

        return (
          <div key={offer.id} className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/60 rounded-lg overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-sky-500/20 group">
            <div className="relative h-48 bg-gray-900/50 flex items-center justify-center overflow-hidden">
              <Image
                src={buildThumbnailUrl(offer.image_path) || storeLogo || '/placeholder.png'}
                alt={offer.name}
                layout="fill"
                objectFit="contain"
                className="p-2 transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <div className="p-4 flex flex-col justify-between h-40">
              <h3 className="text-base font-semibold text-gray-100 truncate" title={offer.name}>
                {offer.name}
              </h3>
              <div>
                <div className="flex justify-between items-baseline mt-2">
                  <p className="text-xl font-bold text-sky-400">
                    {offer.price.toFixed(2).replace('.', ',')} kr.
                  </p>
                  {offer.original_price && (
                    <p className="text-sm text-gray-400 line-through">
                      {offer.original_price.toFixed(2).replace('.', ',')} kr.
                    </p>
                  )}
                </div>
                <div className="flex items-center mt-3 pt-3 border-t border-gray-700/60">
                  {storeLogo && <img src={storeLogo} alt={storeName || ''} className="h-5 w-auto mr-2 object-contain" />}
                  <p className="text-xs text-gray-400">{storeName || 'Unknown Store'}</p>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default OfferList;
import Image from 'next/image';
import { BackendProduct, BackendCatalog } from 'lib/backend';
import { buildThumbnailUrl, getStoreLogoUrl } from 'lib/ui-helpers';

interface OfferListProps {
  offers: BackendProduct[];
  catalogs: Pick<BackendCatalog, 'id' | 'store_name'>[];
  loading?: boolean;
}

const OfferList: React.FC<OfferListProps> = ({ offers, catalogs, loading }) => {
  const getCatalogInfo = (catalogId: number) => {
    return catalogs.find(c => c.id === catalogId);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {Array.from({ length: 10 }).map((_, index) => (
          <div key={index} className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/60 rounded-lg overflow-hidden shadow-lg animate-pulse">
            <div className="h-48 bg-gray-700/50"></div>
            <div className="p-4">
              <div className="h-6 bg-gray-700/50 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-700/50 rounded w-1/2 mb-4"></div>
              <div className="h-6 bg-gray-700/50 rounded w-1/3"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!offers || offers.length === 0) {
    return (
      <div className="text-center py-12 text-gray-400">
        <p>No offers found for your query.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      {offers.map((offer) => {
        const catalogInfo = getCatalogInfo(offer.catalog_id);
        const storeName = catalogInfo?.store_name;
        const storeLogo = getStoreLogoUrl(storeName);

        return (
          <div key={offer.id} className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/60 rounded-lg overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-sky-500/20 group">
            <div className="relative h-48 bg-gray-900/50 flex items-center justify-center overflow-hidden">
              <Image
                src={buildThumbnailUrl(offer.image_path) || storeLogo || '/placeholder.png'}
                alt={offer.name}
                layout="fill"
                objectFit="contain"
                className="p-2 transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <div className="p-4 flex flex-col justify-between h-40">
              <h3 className="text-base font-semibold text-gray-100 truncate" title={offer.name}>
                {offer.name}
              </h3>
              <div>
                <div className="flex justify-between items-baseline mt-2">
                  <p className="text-xl font-bold text-sky-400">
                    {offer.price.toFixed(2).replace('.', ',')} kr.
                  </p>
                  {offer.original_price && (
                    <p className="text-sm text-gray-400 line-through">
                      {offer.original_price.toFixed(2).replace('.', ',')} kr.
                    </p>
                  )}
                </div>
                <div className="flex items-center mt-3 pt-3 border-t border-gray-700/60">
                  {storeLogo && <img src={storeLogo} alt={storeName || ''} className="h-5 w-auto mr-2 object-contain" />}
                  <p className="text-xs text-gray-400">{storeName || 'Unknown Store'}</p>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default OfferList;
import Image from 'next/image';
import { BackendProduct, BackendCatalog } from 'lib/backend';
import { buildThumbnailUrl, getStoreLogoUrl } from 'lib/ui-helpers';

interface OfferListProps {
  offers: BackendProduct[];
  catalogs: Pick<BackendCatalog, 'id' | 'store_name'>[];
  loading?: boolean;
}

const OfferList: React.FC<OfferListProps> = ({ offers, catalogs, loading }) => {
  const getCatalogInfo = (catalogId: number) => {
    return catalogs.find(c => c.id === catalogId);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {Array.from({ length: 10 }).map((_, index) => (
          <div key={index} className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/60 rounded-lg overflow-hidden shadow-lg animate-pulse">
            <div className="h-48 bg-gray-700/50"></div>
            <div className="p-4">
              <div className="h-6 bg-gray-700/50 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-700/50 rounded w-1/2 mb-4"></div>
              <div className="h-6 bg-gray-700/50 rounded w-1/3"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!offers || offers.length === 0) {
    return (
      <div className="text-center py-12 text-gray-400">
        <p>No offers found for your query.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      {offers.map((offer) => {
        const catalogInfo = getCatalogInfo(offer.catalog_id);
        const storeName = catalogInfo?.store_name;
        const storeLogo = getStoreLogoUrl(storeName);

        return (
          <div key={offer.id} className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/60 rounded-lg overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-sky-500/20 group">
            <div className="relative h-48 bg-gray-900/50 flex items-center justify-center overflow-hidden">
              <Image
                src={buildThumbnailUrl(offer.image_path) || storeLogo || '/placeholder.png'}
                alt={offer.name}
                layout="fill"
                objectFit="contain"
                className="p-2 transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <div className="p-4 flex flex-col justify-between h-40">
              <h3 className="text-base font-semibold text-gray-100 truncate" title={offer.name}>
                {offer.name}
              </h3>
              <div>
                <div className="flex justify-between items-baseline mt-2">
                  <p className="text-xl font-bold text-sky-400">
                    {offer.price.toFixed(2).replace('.', ',')} kr.
                  </p>
                  {offer.original_price && (
                    <p className="text-sm text-gray-400 line-through">
                      {offer.original_price.toFixed(2).replace('.', ',')} kr.
                    </p>
                  )}
                </div>
                <div className="flex items-center mt-3 pt-3 border-t border-gray-700/60">
                  {storeLogo && <img src={storeLogo} alt={storeName || ''} className="h-5 w-auto mr-2 object-contain" />}
                  <p className="text-xs text-gray-400">{storeName || 'Unknown Store'}</p>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default OfferList;
import Image from 'next/image';
import { BackendProduct, BackendCatalog } from 'lib/backend';
import { buildThumbnailUrl, getStoreLogoUrl } from 'lib/ui-helpers';

interface OfferListProps {
  offers: BackendProduct[];
  catalogs: Pick<BackendCatalog, 'id' | 'store_name'>[];
  loading?: boolean;
}

const OfferList: React.FC<OfferListProps> = ({ offers, catalogs, loading }) => {
  const getCatalogInfo = (catalogId: number) => {
    return catalogs.find(c => c.id === catalogId);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {Array.from({ length: 10 }).map((_, index) => (
          <div key={index} className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/60 rounded-lg overflow-hidden shadow-lg animate-pulse">
            <div className="h-48 bg-gray-700/50"></div>
            <div className="p-4">
              <div className="h-6 bg-gray-700/50 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-700/50 rounded w-1/2 mb-4"></div>
              <div className="h-6 bg-gray-700/50 rounded w-1/3"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!offers.length) {
    return (
      <div className="text-center py-12 text-gray-400">
        <p>No offers found for your query.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      {offers.map((offer) => {
        const catalogInfo = getCatalogInfo(offer.catalog_id);
        const storeName = catalogInfo?.store_name;
        const storeLogo = getStoreLogoUrl(storeName);

        return (
          <div key={offer.id} className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/60 rounded-lg overflow-hidden shadow-lg transform transition-all duration-300 hover:scale-105 hover:shadow-sky-500/20 group">
            <div className="relative h-48 bg-gray-900/50 flex items-center justify-center overflow-hidden">
              <Image
                src={buildThumbnailUrl(offer.image_path) || storeLogo || '/placeholder.png'}
                alt={offer.name}
                layout="fill"
                objectFit="contain"
                className="p-2 transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <div className="p-4 flex flex-col justify-between h-40">
              <h3 className="text-base font-semibold text-gray-100" title={offer.name}>
                {offer.name}
              </h3>
              <div>
                <div className="flex justify-between items-baseline mt-2">
                  <p className="text-xl font-bold text-sky-400">
                    {offer.price.toFixed(2).replace('.', ',')} kr.
                  </p>
                  {offer.original_price && (
                    <p className="text-sm text-gray-400 line-through">
                      {offer.original_price.toFixed(2).replace('.', ',')} kr.
                    </p>
                  )}
                </div>
                <div className="flex items-center mt-3 pt-3 border-t border-gray-700/60">
                  {storeLogo && <img src={storeLogo} alt={storeName || ''} className="h-5 w-auto mr-2 object-contain" />}
                  <p className="text-xs text-gray-400">{storeName || 'Unknown Store'}</p>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default OfferList;
                  <p className="text-xs text-sky-300">{product.category || ''}</p>
                  <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded-full bg-sky-900/30 text-sky-200">
                    Tilbud
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Show when products are available */}
      {products.length > 0 && (
        <div className="mt-4 text-center">
          <p className="text-sm text-gray-400">
            Viser {products.length} {products.length === 1 ? 'produkt' : 'produkter'}
          </p>
        </div>
      )}
    </div>
  );
};

export default OfferList;
