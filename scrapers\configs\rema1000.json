{"store_name": "Rema 1000", "scraper_class_name": "Rema1000Scraper", "base_url": "https://rema1000.dk/", "catalog_list_url": "https://rema1000.dk/avis", "output_path": "scrapers/downloads/", "selectors": {"cookie_accept_selectors": ["button.coi-banner__accept[aria-label='Accepter alle']", "#coiAcceptAllButton", "#coiPage-1 button.coi-banner__accept"], "catalog_tile_selector": "a[href*='/avis/']:has(div.flex.flex-col.gap-0.text-center)", "catalog_date_selector_within_tile": "div.flex.flex-col h4.font-condensed", "catalog_title_selector_within_tile": "div.flex.flex-col h3.font-condensed", "viewer_menu_button_selector": "button#headlessui-menu-button-1", "viewer_pdf_download_link_selector": "a.block.cursor-pointer:has-text('Hent som PDF')"}, "behavior_flags": {"wait_after_cookie_ms": 1000, "wait_after_goto_ms": 1500, "wait_for_catalog_list_ms": 2000, "wait_after_tile_click_ms": 4000, "wait_after_menu_click_ms": 1500, "cookie_consent_timeout_ms": 15000, "element_wait_timeout_ms": 40000, "navigation_timeout_ms": 120000, "download_event_timeout_ms": 60000, "debug_level": "INFO", "headless": true, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}}