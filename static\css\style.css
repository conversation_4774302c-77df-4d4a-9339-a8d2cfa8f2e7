/* Based on Design Guide */
body {
    font-family: 'Roboto', 'Helvetica Neue', sans-serif;
    background-color: #f8f9fa; /* Light background */
    color: #333;
    margin: 0;
    padding: 0;
    line-height: 1.6; /* leading-relaxed equivalent */
}

.container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3 {
    font-weight: 600; /* font-semibold */
    line-height: 1.25; /* leading-tight */
    color: #333;
    margin-bottom: 0.5em; /* mb-2 */
}
h1 { font-size: 24px; } /* text-xl/2xl */
h2 { font-size: 18px; } /* text-lg */
p {
    margin-bottom: 1em; /* Roughly mb-4 */
    font-size: 16px; /* text-base */
    font-weight: 400; /* font-normal */
}
.metadata {
    font-size: 14px; /* text-sm */
    color: #6c757d; /* Muted color */
}
.highlight {
    color: #6482AD; /* Accent color */
    font-weight: 500; /* font-medium */
}

/* Spacing & Layout */
.section {
    margin-bottom: 32px; /* ~mb-8 */
    background-color: #fff; /* Add white background to sections */
    padding: 24px; /* Add padding to sections */
    border-radius: 8px; /* Add rounded corners */
    box-shadow: 0 2px 4px rgba(0,0,0,0.05); /* Add subtle shadow */
}
.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 24px; /* gap-6 */
}

/* Components */
.card {
    background-color: #F5EDED;
    border: 1px solid #E2DAD6;
    border-radius: 6px; /* rounded-md */
    padding: 16px; /* Adjusted padding */
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); /* shadow-sm */
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card img.catalog-cover-image {
    max-width: 100%;
    height: 150px; /* Fixed height for consistency */
    object-fit: contain; /* Preserve aspect ratio, show full logo */
    border-radius: 4px; /* slightly rounded corners */
    margin-bottom: 12px; /* mb-3 */
    background-color: #f8f9fa; /* Light background for logos */
    padding: 8px; /* Add some padding around logos */
}
.card-content {
    flex-grow: 1;
}
.card-actions {
    margin-top: 16px; /* mt-4 */
    display: flex; /* Align buttons */
    gap: 8px; /* Space between buttons */
}

.button, button, input[type="submit"] {
    display: inline-block;
    padding: 8px 16px; /* py-2 px-4 */
    border: none;
    border-radius: 4px;
    font-weight: 500; /* font-medium */
    cursor: pointer;
    text-align: center;
    text-decoration: none;
    transition: background-color 0.2s ease, transform 0.1s ease;
    font-size: 14px; /* Slightly smaller buttons */
}
.button:active, button:active, input[type="submit"]:active {
    transform: scale(0.98);
}

.button-primary, button.button-primary {
    background-color: #6482AD;
    color: white;
}
.button-primary:hover, button.button-primary:hover {
    background-color: #7FA1C3;
}

.button-secondary, button.button-secondary {
    background-color: #E2DAD6;
    color: #333;
}
.button-secondary:hover, button.button-secondary:hover {
    background-color: #d1c8c4; /* Darken secondary slightly */
}

.button-danger, button.button-danger {
     background-color: #dc3545;
     color: white;
}
.button-danger:hover, button.button-danger:hover {
     background-color: #c82333;
}

/* Make buttons in cards slightly smaller */
.card-actions .button {
    padding: 6px 12px;
    font-size: 13px;
}

.form-group {
    margin-bottom: 16px; /* mb-4 */
}
.form-label {
    display: block;
    margin-bottom: 4px; /* mb-1 */
    font-weight: 500; /* font-medium */
    font-size: 14px; /* text-sm */
}
.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #E2DAD6;
    border-radius: 4px;
    font-size: 16px; /* text-base */
    box-sizing: border-box; /* Include padding in width */
    background-color: #fff;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
.form-control:focus {
    outline: none;
    border-color: #6482AD;
    box-shadow: 0 0 0 2px rgba(100, 130, 173, 0.2);
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
 }

select.form-control {
    /* Allow select to size naturally but respect width */
    width: auto;
    min-width: 150px;
    /* Add dropdown arrow styling if needed */
}

#queryResult {
    background-color: #e9ecef; /* Lighter gray */
    border-left: 4px solid #6482AD; /* Accent border */
    padding: 16px; /* p-4 */
    margin-top: 20px; /* Adjusted from 16px */
    border-radius: 4px;
    min-height: 50px; /* Ensure it's visible even when empty */
}
#queryResult p:first-child {
    margin-top: 0;
}
#queryResult p:last-child {
    margin-bottom: 0;
}
#queryResult ul, #queryResult ol {
    padding-left: 20px;
}

.prompt-gallery {
    display: flex;
    gap: 8px; /* gap-2 */
    flex-wrap: wrap;
    margin-top: 8px; /* mt-2 */
}
.prompt-tag {
    background-color: #E2DAD6;
    color: #333;
    padding: 4px 8px; /* py-1 px-2 */
    border-radius: 12px; /* rounded-full */
    font-size: 12px; /* text-xs */
    cursor: pointer;
    transition: background-color 0.2s ease;
}
.prompt-tag:hover {
    background-color: #d1c8c4;
}

/* Header/Nav */
.app-header {
    background-color: #fff;
    padding: 10px 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.app-header .logo {
    font-size: 20px; /* text-xl */
    font-weight: 600; /* font-semibold */
    color: #6482AD; /* Accent */
    text-decoration: none;
}
 .app-header nav {
    display: flex;
    align-items: center;
    gap: 15px; /* Use gap for spacing */
 }
 .app-header nav a {
     text-decoration: none;
     color: #333;
     font-weight: 500;
     transition: color 0.2s ease;
 }
 .app-header nav a:hover {
     color: #6482AD;
 }

 /* Style the auth section within the nav */
 #auth-section {
    display: flex;
    align-items: center;
    gap: 10px; /* Space between welcome msg and button */
 }
 #auth-section span {
     font-size: 14px;
 }
 #auth-section .button, #auth-section button {
    padding: 5px 10px; /* Smaller padding for nav buttons */
    font-size: 13px;
 }

/* Loading Indicator */
#loadingIndicator {
    display: none; /* Hidden by default */
    margin-top: 15px;
    font-style: italic;
    color: #6c757d;
}

/* Utility */
.text-muted {
    color: #6c757d !important; /* Match metadata color */
}
.mb-3 {
    margin-bottom: 1rem !important;
}
.mt-4 {
    margin-top: 1.5rem !important;
}
.me-2 {
    margin-right: 0.5rem !important;
}
.ms-auto {
    margin-left: auto !important;
}
.text-end {
    text-align: right !important;
}

/* Minimal Modal Styling (to prevent completely broken look) */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1050; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}
.modal-dialog {
    position: relative;
    margin: 50px auto;
    max-width: 500px;
    pointer-events: none;
}
.modal.fade .modal-dialog {
    transition: transform .3s ease-out;
    transform: translateY(-50px);
}
.modal.show .modal-dialog {
    transform: none;
    pointer-events: auto;
}
.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: 0.3rem;
    outline: 0;
}
.modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
}
.modal-title { margin-bottom: 0; line-height: 1.5; font-size: 1.25rem; }
.modal-header .btn-close {
    padding: 0.5rem 0.5rem;
    margin: -0.5rem -0.5rem -0.5rem auto;
    background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
    border: 0;
    border-radius: 0.25rem;
    opacity: .5;
}
.modal-header .btn-close:hover { opacity: .75; }

.modal-body { position: relative; flex: 1 1 auto; padding: 1rem; }
.modal-footer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    padding: 0.75rem;
    border-top: 1px solid #dee2e6;
    border-bottom-right-radius: calc(0.3rem - 1px);
    border-bottom-left-radius: calc(0.3rem - 1px);
}
.modal-footer > * { margin: 0.25rem; }

/* Basic Alert Styling */
.alert {
    position: relative;
    padding: 1rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem;
}
.alert-danger { color: #842029; background-color: #f8d7da; border-color: #f5c2c7; }
.alert-success { color: #0f5132; background-color: #d1e7dd; border-color: #badbcc; }
.alert-warning { color: #664d03; background-color: #fff3cd; border-color: #ffecb5; }
.alert-info { color: #055160; background-color: #cff4fc; border-color: #b6effb; }
.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 1.25rem 1rem;
}

/* Responsive adjustments for login/auth section */
@media (max-width: 768px) {
    #auth-section {
        margin-top: 15px; /* Add some space above on smaller screens */
        margin-bottom: 15px; /* Add some space below on smaller screens */
        /* You might also want to ensure it aligns to the right or adjust its flex properties if needed */
        /* For example, if it's in a flex container with the h1:
        display: flex; 
        justify-content: flex-end; 
        width: 100%; /* Or appropriate width */
        */
    }
}

/* Add any other general responsive styles or utility classes below */ 