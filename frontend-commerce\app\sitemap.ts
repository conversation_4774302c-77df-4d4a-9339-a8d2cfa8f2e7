import { getStores, getProducts } from '@/lib/backend';
import { baseUrl, validateEnvironmentVariables } from '@/lib/utils';
import { MetadataRoute } from 'next';

type Route = {
  url: string;
  lastModified: string;
};

export const dynamic = 'force-dynamic';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  validateEnvironmentVariables();

  const routesMap = [''].map((route) => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date().toISOString()
  }));

  const storesPromise = getStores().then((stores) =>
    stores.map((store) => ({
      url: `${baseUrl}/search/${store.name.toLowerCase()}`,
      lastModified: new Date().toISOString()
    }))
  );

  const productsPromise = getProducts().then((products) =>
    products.map((product) => ({
      url: `${baseUrl}/product/${product.id}`,
      lastModified: new Date().toISOString()
    }))
  );

  let fetchedRoutes: Route[] = [];

  try {
    fetchedRoutes = (
      await Promise.all([storesPromise, productsPromise])
    ).flat();
  } catch (error) {
    console.error('Error generating sitemap:', error);
    // Return basic routes if there's an error
    fetchedRoutes = [];
  }

  return [...routesMap, ...fetchedRoutes];
}
