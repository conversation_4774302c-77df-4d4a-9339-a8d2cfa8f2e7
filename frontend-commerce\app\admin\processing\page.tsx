"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth';
import { getCatalogs, getParsingStatus, reparseCatalog, stopParsing, BackendCatalog, ParsingStatus, getOperationLogs, triggerReconciliation, cleanMemory, triggerManualScrape, getAvailableScrapers } from '@/lib/backend';
import Link from 'next/link';
import { toast } from 'sonner';

// 🏗️ ENTERPRISE TYPES
interface OperationLog {
  id: number;
  catalog_id?: number;
  store_name?: string;
  operation_type: string;
  operation_status: string;
  message?: string;
  started_at: string;
  completed_at?: string;
  duration_seconds?: number;
  triggered_by?: string;
  metadata?: any;
}

interface EnterpriseStats {
  total_operations: number;
  successful_operations: number;
  failed_operations: number;
  avg_duration: number;
  last_reconciliation: string;
}

type CatalogWithStatus = BackendCatalog & {
  parsingStatus?: ParsingStatus;
};

// Helper component for status badges
const StatusBadge = ({ status }: { status: ParsingStatus['status'] }) => {
  const styles = {
    completed: 'bg-green-100 text-green-800',
    processing: 'bg-blue-100 text-blue-800 animate-pulse',
    queued: 'bg-gray-100 text-gray-800',
    error: 'bg-red-100 text-red-800',
    cancelled: 'bg-yellow-100 text-yellow-800',
    cancelling: 'bg-yellow-100 text-yellow-800 animate-pulse',
    skipped: 'bg-indigo-100 text-indigo-800',
    unknown: 'bg-purple-100 text-purple-800',
  };
  return (
    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${styles[status] || styles.unknown}`}>
      {status}
    </span>
  );
};

export default function AdminProcessingPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [combinedData, setCombinedData] = useState<CatalogWithStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [isRunningAll, setIsRunningAll] = useState(false);

  // 🏗️ ENTERPRISE STATE
  const [operationLogs, setOperationLogs] = useState<OperationLog[]>([]);
  const [enterpriseStats, setEnterpriseStats] = useState<EnterpriseStats | null>(null);
  const [showOperationLogs, setShowOperationLogs] = useState(false);
  const [isReconciling, setIsReconciling] = useState(false);
  const [isCleaningMemory, setIsCleaningMemory] = useState(false);
  const [selectedStore, setSelectedStore] = useState<string>('');
  const [isManualScraping, setIsManualScraping] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000); // 5 seconds
  const [availableScrapers, setAvailableScrapers] = useState<string[]>([]);
  const [selectedCatalogId, setSelectedCatalogId] = useState<string>('');
  const [isParsingCatalog, setIsParsingCatalog] = useState(false);

  // 🏗️ ENTERPRISE API FUNCTIONS
  const fetchOperationLogs = async () => {
    const token = localStorage.getItem('token');
    if (!token) return;

    try {
      const data = await getOperationLogs(token, 50);
      setOperationLogs(data.operation_logs || []);

      // Calculate enterprise stats - FILTER ONLY PARSE OPERATIONS FOR CATALOGS
      const logs = data.operation_logs || [];
      const parseOperations = logs.filter((log: OperationLog) => log.operation_type === 'PARSE');
      const successful = parseOperations.filter((log: OperationLog) => log.operation_status === 'SUCCESS').length;
      const failed = parseOperations.filter((log: OperationLog) => log.operation_status === 'FAILED').length;
      const avgDuration = parseOperations.length > 0
        ? parseOperations.reduce((acc: number, log: OperationLog) => acc + (log.duration_seconds || 0), 0) / parseOperations.length
        : 0;

      setEnterpriseStats({
        total_operations: parseOperations.length,
        successful_operations: successful,
        failed_operations: failed,
        avg_duration: avgDuration,
        last_reconciliation: logs.find((log: OperationLog) => log.operation_type === 'RECONCILE')?.completed_at || 'Never'
      });
    } catch (error) {
      console.error('Failed to fetch operation logs:', error);
    }
  };

  const handleReconciliation = async () => {
    const token = localStorage.getItem('token');
    if (!token) return;

    setIsReconciling(true);
    try {
      const result = await triggerReconciliation(token);
      toast.success(result.message);
      fetchData();
      fetchOperationLogs();
    } catch (error) {
      toast.error('Reconciliation failed');
    } finally {
      setIsReconciling(false);
    }
  };

  const handleCleanMemory = async () => {
    const token = localStorage.getItem('token');
    if (!token) return;

    setIsCleaningMemory(true);
    try {
      const result = await cleanMemory(token, 24);
      toast.success(result.message);
      fetchOperationLogs();
    } catch (error) {
      toast.error('Memory cleanup failed');
    } finally {
      setIsCleaningMemory(false);
    }
  };

  const handleManualScrape = async () => {
    if (!selectedStore) {
      toast.error('Please select a store');
      return;
    }

    const token = localStorage.getItem('token');
    if (!token) return;

    setIsManualScraping(true);
    try {
      const result = await triggerManualScrape(token, selectedStore);
      toast.success(result.message);
      fetchData();
      fetchOperationLogs();
    } catch (error) {
      toast.error('Manual scrape failed');
    } finally {
      setIsManualScraping(false);
    }
  };

  const handleManualParse = async () => {
    if (!selectedCatalogId) {
      toast.error('Please select a catalog to parse');
      return;
    }

    const token = localStorage.getItem('token');
    if (!token) return;

    setIsParsingCatalog(true);
    try {
      const response = await fetch(`/api/parse_catalog/${selectedCatalogId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`Parsing started for catalog ${selectedCatalogId}`);
        fetchData();
        fetchOperationLogs();
      } else {
        const errorData = await response.json();
        toast.error(`Failed to start parsing: ${errorData.detail || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error starting parsing:', error);
      toast.error('Failed to start parsing. Please try again.');
    } finally {
      setIsParsingCatalog(false);
    }
  };

  // Fetch available scrapers from backend
  const fetchAvailableScrapers = async () => {
    const token = localStorage.getItem('token');
    if (!token) return;

    try {
      const scrapers = await getAvailableScrapers(token);
      setAvailableScrapers(scrapers);
    } catch (error) {
      console.error('Failed to fetch available scrapers:', error);
      // Fallback to hardcoded list if API fails - matches actual config files
      setAvailableScrapers(['bilka', 'brugsen', 'discount365', 'fotex', 'lidl', 'meny', 'minkobmand', 'netto', 'rema1000', 'spar', 'superbrugsen']);
    }
  };

  // Data fetching and combining logic
  const fetchData = async () => {
    const token = localStorage.getItem('token');
    if (!token) {
        setLoading(false);
        return;
    };

    try {
      const [catalogs, statuses] = await Promise.all([
        getCatalogs(),
        getParsingStatus(token),
      ]);

      const data: CatalogWithStatus[] = catalogs.map(catalog => ({
        ...catalog,
        parsingStatus: statuses[catalog.id] || { status: 'unknown', message: 'Status not tracked.' },
      }));

      setCombinedData(data.sort((a, b) => b.id - a.id));
    } catch (err) {
      console.error('Failed to load processing data:', err);
      toast.error('Could not load data. Please refresh.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.is_admin) {
      fetchData(); // Initial fetch
      fetchOperationLogs(); // Fetch enterprise logs
      fetchAvailableScrapers(); // Fetch available scrapers

      if (autoRefresh) {
        const interval = setInterval(() => {
          fetchData();
          fetchOperationLogs();
        }, refreshInterval);
        return () => clearInterval(interval);
      }
    }
  }, [user, autoRefresh, refreshInterval]);

  // Redirect non-admins
  useEffect(() => {
    if (!isLoading && !user?.is_admin) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  const handleReparse = async (catalogId: number) => {
    const token = localStorage.getItem('token');
    if (!token) return;
    toast.info(`Requesting re-parse for catalog ${catalogId}...`);
    try {
      await reparseCatalog(catalogId, token);
      toast.success(`Catalog ${catalogId} queued for re-parsing.`);
      fetchData(); // Refresh data immediately
    } catch (error) {
      toast.error(`Failed to re-parse catalog: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleStop = async (catalogId: number) => {
    const token = localStorage.getItem('token');
    if (!token) return;
    toast.info(`Requesting to stop parsing for catalog ${catalogId}...`);
    try {
      await stopParsing(catalogId, token);
      toast.success(`Stop request sent for catalog ${catalogId}.`);
      fetchData(); // Refresh data immediately
    } catch (error) {
      toast.error(`Failed to stop parsing: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleRunAllParsers = async () => {
    const token = localStorage.getItem('token');
    if (!token) return;

    setIsRunningAll(true);
    try {
      const response = await fetch('/api/parse_all_unprocessed', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`${result.message} (${result.catalogs_queued} catalogs queued)`);
        fetchData();
      } else {
        const errorData = await response.json();
        toast.error(`Failed to start bulk parsing: ${errorData.detail || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error starting bulk parsing:', error);
      toast.error('Failed to start bulk parsing. Please try again.');
    } finally {
      setIsRunningAll(false);
    }
  };

  if (isLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[70vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 🏗️ ENTERPRISE HEADER */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">🏗️ Enterprise Pipeline Dashboard</h1>
              <p className="mt-1 text-sm text-gray-500">Real-time monitoring and control for million-dollar SaaS operations</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-600">Auto-refresh:</label>
                <button
                  onClick={() => setAutoRefresh(!autoRefresh)}
                  className={`px-3 py-1 rounded-md text-xs font-medium ${
                    autoRefresh ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {autoRefresh ? 'ON' : 'OFF'}
                </button>
              </div>
              <Link href="/admin" className="text-indigo-600 hover:text-indigo-900 font-medium">← Back to Admin</Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">

        {/* 🏗️ ENTERPRISE STATS DASHBOARD */}
        {enterpriseStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">📊</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Operations</p>
                  <p className="text-2xl font-bold text-gray-900">{enterpriseStats.total_operations}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">✅</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Successful</p>
                  <p className="text-2xl font-bold text-green-600">{enterpriseStats.successful_operations}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">❌</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Failed</p>
                  <p className="text-2xl font-bold text-red-600">{enterpriseStats.failed_operations}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">⚡</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Avg Duration</p>
                  <p className="text-2xl font-bold text-purple-600">{enterpriseStats.avg_duration.toFixed(1)}s</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">🔧</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Last Reconcile</p>
                  <p className="text-sm font-bold text-indigo-600">
                    {enterpriseStats.last_reconciliation !== 'Never'
                      ? new Date(enterpriseStats.last_reconciliation).toLocaleTimeString()
                      : 'Never'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 🏗️ ENTERPRISE CONTROL PANEL */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">🎛️ Enterprise Control Panel</h2>
            <p className="text-sm text-gray-500">Manual controls for enterprise operations</p>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">

              {/* Bulk Operations */}
              <button
                onClick={handleRunAllParsers}
                disabled={isRunningAll}
                className="flex items-center justify-center px-4 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
              >
                {isRunningAll ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    Running All...
                  </>
                ) : (
                  <>🚀 Run All Parsers</>
                )}
              </button>

              {/* Status Reconciliation */}
              <button
                onClick={handleReconciliation}
                disabled={isReconciling}
                className="flex items-center justify-center px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
              >
                {isReconciling ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    Reconciling...
                  </>
                ) : (
                  <>🔧 Reconcile Status</>
                )}
              </button>

              {/* Memory Cleanup */}
              <button
                onClick={handleCleanMemory}
                disabled={isCleaningMemory}
                className="flex items-center justify-center px-4 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
              >
                {isCleaningMemory ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    Cleaning...
                  </>
                ) : (
                  <>🧹 Clean Memory</>
                )}
              </button>

              {/* Operation Logs Toggle */}
              <button
                onClick={() => setShowOperationLogs(!showOperationLogs)}
                className="flex items-center justify-center px-4 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg font-medium transition-colors"
              >
                📊 {showOperationLogs ? 'Hide' : 'Show'} Logs
              </button>
            </div>

            {/* Manual Scrape Section */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-sm font-medium text-gray-900 mb-3">🔧 Manual Scrape Control</h3>
              <div className="flex gap-3">
                <select
                  value={selectedStore}
                  onChange={(e) => setSelectedStore(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="">Select store to scrape...</option>
                  {availableScrapers.map(scraper => (
                    <option key={scraper} value={scraper}>
                      {scraper.charAt(0).toUpperCase() + scraper.slice(1)}
                    </option>
                  ))}
                </select>
                <button
                  onClick={handleManualScrape}
                  disabled={isManualScraping || !selectedStore}
                  className="px-6 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-md font-medium transition-colors"
                >
                  {isManualScraping ? 'Scraping...' : 'Scrape Now'}
                </button>
              </div>
            </div>

            {/* Manual Parse Section */}
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-sm font-medium text-gray-900 mb-3">🔧 Manual Parse Control</h3>
              <div className="flex gap-3">
                <select
                  value={selectedCatalogId}
                  onChange={(e) => setSelectedCatalogId(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="">Select catalog to parse...</option>
                  {combinedData.map((catalog) => (
                    <option key={catalog.id} value={catalog.id}>
                      {catalog.store_name} - {catalog.title || `Catalog ${catalog.id}`}
                      {catalog.valid_from && ` (${new Date(catalog.valid_from).toLocaleDateString()})`}
                    </option>
                  ))}
                </select>
                <button
                  onClick={handleManualParse}
                  disabled={isParsingCatalog || !selectedCatalogId}
                  className="px-6 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white rounded-md font-medium transition-colors"
                >
                  {isParsingCatalog ? 'Parsing...' : 'Parse Now'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 🏗️ ENTERPRISE OPERATION LOGS */}
        {showOperationLogs && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">📊 Enterprise Operation Logs</h2>
              <p className="text-sm text-gray-500">Real-time audit trail of all pipeline operations</p>
            </div>
            <div className="overflow-hidden">
              <div className="max-h-96 overflow-y-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Time</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Operation</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Store</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Duration</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Message</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {operationLogs.map((log) => (
                      <tr key={log.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(log.started_at).toLocaleTimeString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            log.operation_type === 'PARSE' ? 'bg-blue-100 text-blue-800' :
                            log.operation_type === 'SCRAPE' ? 'bg-green-100 text-green-800' :
                            log.operation_type === 'RECONCILE' ? 'bg-purple-100 text-purple-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {log.operation_type}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {log.store_name || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            log.operation_status === 'SUCCESS' ? 'bg-green-100 text-green-800' :
                            log.operation_status === 'FAILED' ? 'bg-red-100 text-red-800' :
                            log.operation_status === 'STARTED' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {log.operation_status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {log.duration_seconds ? `${log.duration_seconds.toFixed(2)}s` : '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                          {log.message || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* 🏗️ ENHANCED CATALOG STATUS TABLE */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">📋 Catalog Processing Status</h2>
            <p className="text-sm text-gray-500">Real-time status of all catalog processing operations</p>
          </div>
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Catalog</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Details</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Last Updated</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {combinedData.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-600">
                              {item.store_name?.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{item.title}</div>
                          <div className="text-sm text-gray-500">ID: {item.id} | {item.store_name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <StatusBadge status={item.parsingStatus?.status || 'unknown'} />
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 max-w-xs">
                      <div className="truncate">{item.parsingStatus?.message}</div>
                      {item.parsingStatus?.products_found && (
                        <div className="text-xs text-green-600 mt-1">
                          {item.parsingStatus.products_found} products found
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.parsingStatus?.last_updated
                        ? new Date(item.parsingStatus.last_updated).toLocaleString()
                        : '-'
                      }
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end gap-2">
                        {['processing', 'queued', 'cancelling'].includes(item.parsingStatus?.status || '') ? (
                          <button
                            onClick={() => handleStop(item.id)}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                          >
                            Stop
                          </button>
                        ) : (
                          <button
                            onClick={() => handleReparse(item.id)}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 transition-colors"
                          >
                            Reparse
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
