// frontend-commerce/next.config.js

const path = require('path');

const apiBase = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';

module.exports = {
  // Removed experimental features for stability
  images: {
    formats: ['image/avif', 'image/webp']
  },
  // Emergency failsafe: Skip TypeScript errors if explicitly requested
  typescript: {
    ignoreBuildErrors: process.env.IGNORE_BUILD_ERRORS === 'true',
  },
  webpack: (config, { dev, isServer }) => {
    // Add debugging logs for Render deployment
    console.log('🔧 Webpack config running:', { dev, isServer });
    console.log('📁 Current working directory:', __dirname);

    // Clean, focused aliases - avoid conflicts
    const baseAliases = config.resolve.alias || {};

    // Only set the @ aliases, remove redundant ones
    config.resolve.alias = {
      ...baseAliases,
      '@': path.resolve(__dirname),
      '@/lib': path.resolve(__dirname, 'lib'),
      '@/components': path.resolve(__dirname, 'components'),
      '@/app': path.resolve(__dirname, 'app'),
    };

    // Ensure proper module resolution extensions
    config.resolve.extensions = ['.ts', '.tsx', '.js', '.jsx', '.json', ...(config.resolve.extensions || [])];

    // Log build environment
    console.log('🛡️ Build environment:', { dev, isServer, nodeEnv: process.env.NODE_ENV });

    console.log('📂 Resolved paths:', {
      '@': path.resolve(__dirname),
      '@/lib': path.resolve(__dirname, 'lib'),
      '@/components': path.resolve(__dirname, 'components'),
      '@/app': path.resolve(__dirname, 'app'),
    });
    console.log('✅ Final webpack resolve aliases:', config.resolve.alias);
    console.log('🔍 Extensions:', config.resolve.extensions);
    return config;
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${apiBase}/api/:path*`
      }
    ];
  }
};
