# Environment variables
.env

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-debug.log*

# Next.js
frontend-commerce/.next/
frontend-commerce/out/
frontend-commerce/.vercel/

# Database
*.db
*.sqlite
*.sqlite3

# Uploaded files
catalogs/*
!catalogs/.gitkeep
images/*
!images/.gitkeep
aviser/*
!aviser/.gitkeep

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
# Only ignore Python lib directories, not frontend lib
/lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.venv/

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS specific
.DS_Store
Thumbs.db

# Large third-party dependencies that should be installed separately
poppler-*/

catalog_offers.db

# Local data storage
local_data/

# Test scripts and artifacts
/test/

# Local catalogs and their processed versions
/catalogs/
/aviser/
/images/

# Test script outputs
/tests/output/
*.pdf

# IDE and OS specific
.vscode/
__pycache__/
*.pyc
.DS_Store

# Environment files
.env
*.env

# SQLite database file (if you decide to version control it elsewhere or regenerate)
# instance/site.db
# app.db

# Log files
*.log 

# Test results & coverage reports
htmlcov/
.coverage
.pytest_cache/

# Playwright specific
playwright/.cache/
playwright-report/
test-results/

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# Scraper specific downloads
scrapers/downloads/

# Lidl specific downloads
lidl_downloads/

# AI Digest output
codebase.md

# Specific to this project if any other temp files are generated
# e.g., temp_output/