# Google Cloud Storage Folder Structure for Avis Scanner

## 📁 **Root Bucket: `tilbudsjaegeren`**

```
tilbudsjaegeren/
├── catalogs/                          # PDF catalog files
│   ├── bilka_20250627_20250703.pdf
│   ├── meny_20250627_20250703.pdf
│   ├── netto_20250628_20250704.pdf
│   └── ...
│
├── images/                            # Extracted catalog page images
│   ├── catalog_1/                     # Images for catalog ID 1
│   │   ├── page_1.png
│   │   ├── page_2.png
│   │   └── ...
│   ├── catalog_2/                     # Images for catalog ID 2
│   │   ├── page_1.png
│   │   └── ...
│   └── ...
│
├── logos/                             # Store logos and branding assets
│   ├── supermarkets/                  # Individual store logos
│   │   ├── bilka.png
│   │   ├── meny.png
│   │   ├── netto.png
│   │   ├── rema1000.png
│   │   └── ...
│   ├── tilbudsjaegeren-logo.png       # Main app logo
│   └── favicon.ico                    # App favicon
│
├── temp/                              # Temporary files (auto-cleanup)
│   ├── processing/                    # Files being processed
│   └── uploads/                       # Temporary uploads
│
└── static/                            # Static web assets
    ├── css/
    ├── js/
    └── fonts/
```

## 🏷️ **Naming Conventions**

### **Catalog PDFs**
- **Pattern**: `{store_name}_{valid_from}_{valid_to}.pdf`
- **Example**: `bilka_20250627_20250703.pdf`
- **Store Names**: Lowercase, underscores for spaces
  - `bilka`, `meny`, `netto`, `rema_1000`, `spar`, `fotex`, `lidl`
  - `brugsen`, `superbrugsen`, `365discount`, `min_kobmand`

### **Catalog Images**
- **Pattern**: `images/catalog_{catalog_id}/page_{page_number}.png`
- **Example**: `images/catalog_123/page_1.png`
- **Format**: Always PNG for consistency
- **Resolution**: Variable DPI based on PDF size (50-300 DPI)

### **Store Logos**
- **Pattern**: `logos/supermarkets/{store_name}.png`
- **Example**: `logos/supermarkets/bilka.png`
- **Format**: PNG with transparent background preferred
- **Size**: Standardized dimensions (e.g., 200x200px)

### **Temporary Files**
- **Pattern**: `temp/{operation}/{timestamp}_{filename}`
- **Example**: `temp/processing/20250630_123456_bilka_temp.pdf`
- **Cleanup**: Auto-delete after 24 hours

## 🔗 **URL Patterns**

### **Public Access URLs**
```
https://storage.googleapis.com/tilbudsjaegeren/logos/supermarkets/bilka.png
https://storage.googleapis.com/tilbudsjaegeren/images/catalog_123/page_1.png
```

### **Signed URLs (for PDFs)**
```python
# Generate signed URL for temporary access
signed_url = blob.generate_signed_url(
    version="v4",
    expiration=datetime.timedelta(hours=1),
    method="GET"
)
```

## 📊 **Database Path Storage**

### **Catalog Table**
```sql
-- Store cloud paths instead of local paths
pdf_path: "catalogs/bilka_20250627_20250703.pdf"
```

### **CatalogPage Table**
```sql
-- Store cloud paths for images
image_path: "images/catalog_123/page_1.png"
```

## 🔧 **Migration Strategy**

### **Phase 1: Upload Existing Files**
1. Upload all PDFs from `./catalogs/` to `catalogs/`
2. Upload all images from `./images/` to `images/catalog_{id}/`
3. Upload all logos from `./logos/` to `logos/supermarkets/`

### **Phase 2: Update Database**
1. Convert local paths to cloud paths in database
2. Update all references to use cloud storage URLs

### **Phase 3: Update Code**
1. Remove all local file system dependencies
2. Update scrapers to upload directly to cloud
3. Update processors to download from cloud

## 🛡️ **Security & Access Control**

### **Public Files**
- Store logos: Public read access
- Static assets: Public read access

### **Private Files**
- Catalog PDFs: Authenticated access only
- Catalog images: Authenticated access only
- Temporary files: Service account access only

### **Permissions**
```python
# Service account permissions needed:
# - Storage Object Admin (for full CRUD operations)
# - Storage Legacy Bucket Reader (for listing)
```

## 🧹 **Cleanup Policies**

### **Automatic Cleanup**
- Temp files: Delete after 24 hours
- Old catalogs: Archive after 90 days (configurable)
- Failed uploads: Delete after 1 hour

### **Manual Cleanup**
- Expired catalogs: Move to archive folder
- Unused images: Clean up orphaned files

## 📈 **Performance Optimization**

### **CDN Integration**
- Use Google Cloud CDN for static assets
- Cache logos and frequently accessed images

### **Compression**
- Compress images using WebP format where supported
- Use appropriate compression for PDFs

### **Parallel Operations**
- Upload/download multiple files concurrently
- Use batch operations for bulk transfers
