"use client";
import Link from 'next/link';
import { useAuth } from '@/lib/auth';

export default function AuthButtons() {
  const { user, logout, isLoading } = useAuth();

  return (
    <div className="flex gap-2">
      {isLoading ? (
        <div className="text-sm">Loading...</div>
      ) : user ? (
        <div className="flex items-center gap-2">
          <span className="text-sm"><PERSON><PERSON><PERSON><PERSON><PERSON>, {user.email || 'bruger'}</span>
          <button 
            onClick={logout}
            className="px-3 py-1.5 bg-white text-gray-700 hover:bg-gray-100 rounded-md border border-gray-200 text-sm font-medium"
          >
            Log ud
          </button>
        </div>
      ) : (
        <>
          <Link 
            href="/login" 
            className="px-3 py-1.5 bg-indigo-600 text-white hover:bg-indigo-700 rounded-md text-sm font-medium"
          >
            Log Ind
          </Link>
          <Link 
            href="/signup" 
            className="px-3 py-1.5 bg-white text-gray-700 hover:bg-gray-100 rounded-md border border-gray-200 text-sm font-medium"
          >
            Opret Bruger
          </Link>
        </>
      )}
    </div>
  );
}
