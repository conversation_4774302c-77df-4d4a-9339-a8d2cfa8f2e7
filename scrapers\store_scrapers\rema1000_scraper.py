# File: scrapers/store_scrapers/rema1000_scraper.py

import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional

from ..scraper_core import BaseScraper, PlaywrightTimeoutError, PlaywrightError, Page


class Rema1000Scraper(BaseScraper):
    """
    Scraper specifically for Rema 1000 catalogs.
    Navigates to /avis, clicks a catalog tile (which contains date info),
    then on the viewer page, clicks a menu and then the PDF download link.
    Viewer opens in the same tab.
    """

    def __init__(self, config: dict):
        super().__init__(config)
        self.logger.info(f"Rema1000Scraper initialized for store: {self.store_name}")

    async def scrape_catalogs(self) -> List[Dict[str, Any]]:
        if not self.page or not self.context or not self.catalog_list_url:
            self.logger.error("Page, context, or catalog_list_url not available for Rema1000Scraper.")
            return []

        # Set default timeouts
        try:
            default_nav_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            default_el_timeout = self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 30000)
            if self.page:
                self.page.set_default_navigation_timeout(default_nav_timeout)
                self.page.set_default_timeout(default_el_timeout)
                self.logger.info(f"Rema1000Scraper: Page timeouts set - Nav: {default_nav_timeout}ms, Element: {default_el_timeout}ms.")
        except Exception as e_timeout_set:
            self.logger.error(f"Rema1000Scraper: Error setting default timeouts: {e_timeout_set}")
            return []

        # 1. Navigate to Rema 1000 /avis page
        self.logger.debug(f"Navigating to Rema 1000 /avis page: {self.catalog_list_url}.")
        nav_success = await self._navigate_to_url(self.page, self.catalog_list_url)
        if not nav_success:
            self.logger.error(f"Failed to navigate to Rema 1000 /avis page: {self.catalog_list_url}")
            return []

        
        # 2. Handle Cookies
        cookie_selectors = self.config.get('selectors', {}).get('cookie_accept_selectors', [])
        if cookie_selectors:
            await self._handle_cookies(self.page, cookie_selectors,
                                       timeout_ms=self.config.get('behavior_flags', {}).get('cookie_consent_timeout_ms', 15000))
        else:
            self.logger.info("No cookie selectors configured for Rema 1000.")


        # 3. Find Catalog Tiles
        tile_selector = self.config.get('selectors', {}).get('catalog_tile_selector')
        if not tile_selector:
            self.logger.error("Rema1000: 'catalog_tile_selector' not configured.")
            return []

        self.logger.debug(f"Looking for catalog tiles with selector: '{tile_selector}'.")
        catalog_tiles = await self.page.query_selector_all(tile_selector)

        if not catalog_tiles:
            self.logger.warning(f"Rema1000: No catalog tiles found with selector '{tile_selector}'. URL: {self.page.url}")
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "rema1000_no_tiles.png"))
            return []
        
        self.logger.info(f"Rema1000: Found {len(catalog_tiles)} catalog tile(s). Processing the first one.")
        
        # Process the first available catalog tile
        tile_element = catalog_tiles[0]
        raw_date_info = "Rema1000_Unknown_Date"
        display_title = f"{self.store_name} Catalog"

        # Extract date from within the tile
        date_in_tile_selector = self.config.get('selectors', {}).get('catalog_date_selector_within_tile')
        if date_in_tile_selector:
            try:
                date_el = await tile_element.query_selector(date_in_tile_selector)
                if date_el:
                    raw_date_info = (await date_el.text_content() or "").strip()
                    self.logger.info(f"Rema1000: Extracted raw_date_info from tile: '{raw_date_info}'")
            except Exception as e:
                self.logger.warning(f"Rema1000: Error extracting date from tile: {e}")

        # Extract title from within the tile
        title_in_tile_selector = self.config.get('selectors', {}).get('catalog_title_selector_within_tile')
        if title_in_tile_selector:
            try:
                title_el = await tile_element.query_selector(title_in_tile_selector)
                if title_el:
                    extracted_tile_title = (await title_el.text_content() or "").strip()
                    if extracted_tile_title: display_title = extracted_tile_title # e.g., "Uge 23"
                    self.logger.info(f"Rema1000: Extracted title from tile: '{display_title}'")
            except Exception as e:
                self.logger.warning(f"Rema1000: Error extracting title from tile: {e}")
        
        # 4. Click the catalog tile to navigate to the viewer (same tab)
        try:
            self.logger.info(f"Rema1000: Clicking catalog tile to open viewer.")
            await tile_element.click(timeout=10000)
            await self.page.wait_for_load_state('domcontentloaded', timeout=self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000))
            self.logger.info(f"Rema1000: Viewer page loaded. URL: {self.page.url}")

        except Exception as e:
            self.logger.error(f"Rema1000: Error clicking catalog tile or waiting for viewer page: {e}", exc_info=True)
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "rema1000_tile_click_fail.png"))
            return []

        # 5. On viewer page, click the menu button
        menu_button_selector = self.config.get('selectors', {}).get('viewer_menu_button_selector')
        if not menu_button_selector:
            self.logger.error("Rema1000: 'viewer_menu_button_selector' not configured.")
            return []
        
        try:
            self.logger.info(f"Rema1000 Viewer: Clicking menu button: '{menu_button_selector}'")
            menu_button = self.page.locator(menu_button_selector).first
            await menu_button.wait_for(state="visible", timeout=10000)
            await menu_button.click(timeout=5000)
            self.logger.info(f"Rema1000 Viewer: Clicked menu button. Waiting for menu to appear...")

        except Exception as e:
            self.logger.error(f"Rema1000 Viewer: Error clicking menu button: {e}", exc_info=True)
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "rema1000_menu_click_fail.png"))
            return []

        # 6. Click the "Hent som PDF" link in the menu
        pdf_link_selector = self.config.get('selectors', {}).get('viewer_pdf_download_link_selector')
        if not pdf_link_selector:
            self.logger.error("Rema1000: 'viewer_pdf_download_link_selector' not configured.")
            return []

        # 6. Click the "Hent som PDF" link, which opens a new tab with the PDF.
        new_page: Optional[Page] = None
        try:
            self.logger.info(f"Rema1000 Viewer: Clicking PDF download link: '{pdf_link_selector}'")
            pdf_link = self.page.locator(pdf_link_selector).first
            await pdf_link.wait_for(state="visible", timeout=10000)

            # Fast path: if the link has a direct href to the PDF, download without opening a new tab.
            direct_href = await pdf_link.get_attribute("href")
            if direct_href and direct_href.lower().endswith(".pdf"):
                self.logger.info(f"Rema1000: Found direct PDF href: {direct_href} – downloading without new tab")
                pdf_content = await self._download_file_content(direct_href)
                if pdf_content:
                    suggested_filename = f"{self._sanitize_filename(display_title)}_{self._sanitize_filename(raw_date_info)}.pdf"
                    if not suggested_filename.lower().endswith(".pdf"):
                        suggested_filename += ".pdf"
                    local_path = self._ensure_download_dir() / suggested_filename
                    with open(local_path, 'wb') as f:
                        f.write(pdf_content)
                    self.logger.info(f"Rema1000: Saved PDF to {local_path} via direct link")
                    self.collected_catalogs.append({
                        "store_name": self.store_name,
                        "title": display_title,
                        "raw_date_info": raw_date_info,
                        "pdf_url": direct_href,
                        "local_path": str(local_path)
                    })
                    return self.collected_catalogs

            # If not a direct link, attempt to click and catch a download event.
            self.logger.info("Rema1000: No direct PDF link found. Attempting click and expect_download.")
            download_timeout = self.config.get('behavior_flags', {}).get('download_event_timeout_ms', 120000) # Increased default to 120s for Rema
            click_timeout = self.config.get('behavior_flags', {}).get('element_click_timeout_ms', 10000)

            async with self.page.expect_download(timeout=download_timeout) as download_info:
                self.logger.info(f"Rema1000: Clicking PDF link '{pdf_link_selector}' and waiting for download event.")
                await pdf_link.click(timeout=click_timeout)
            
            download = await download_info.value
            download_url_origin = download.url # URL from which the download originated
            suggested_filename_from_download = download.suggested_filename
            
            self.logger.info(f"Rema1000: Download event triggered. Origin URL: {download_url_origin}, Suggested Filename: {suggested_filename_from_download}")

            # Construct filename: prefer suggested_filename if it's good, otherwise fallback
            if suggested_filename_from_download and suggested_filename_from_download.lower().endswith(".pdf"):
                final_pdf_filename = self._sanitize_filename(suggested_filename_from_download)
            else:
                self.logger.warning(f"Rema1000: Suggested filename '{suggested_filename_from_download}' not ideal or missing .pdf. Using title/date fallback.")
                base_filename = f"{self._sanitize_filename(display_title)}_{self._sanitize_filename(raw_date_info)}"
                final_pdf_filename = f"{base_filename}.pdf"

            if not final_pdf_filename.lower().endswith(".pdf"): # Ensure .pdf extension
                 final_pdf_filename = f"{self._sanitize_filename(final_pdf_filename)}.pdf" # Sanitize again if we added .pdf manually

            download_dir = self._ensure_download_dir()
            local_pdf_path = download_dir / final_pdf_filename
            
            await download.save_as(local_pdf_path)
            self.logger.info(f"Rema1000: Successfully saved PDF to: {local_pdf_path}")

            catalog_data = {
                "store_name": self.store_name,
                "title": display_title,
                "raw_date_info": raw_date_info,
                "pdf_url": download_url_origin, # This might be a temporary signed URL, or the page URL.
                "local_path": str(local_pdf_path)
            }
            self.collected_catalogs.append(catalog_data)

        except Exception as e:
            self.logger.error(f"Rema1000: Failed during new tab PDF download process. Error: {e}", exc_info=True)
            if self.page and not self.page.is_closed():
                await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "rema1000_new_tab_fail.png"))
            return []
        finally:
            if new_page and not new_page.is_closed():
                await new_page.close()
                self.logger.debug("Rema1000: Closed the new PDF tab.")

        return self.collected_catalogs