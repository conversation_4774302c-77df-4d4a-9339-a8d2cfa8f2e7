#!/usr/bin/env python3
"""
Database migration script to convert local file paths to cloud storage paths.
This script updates existing database entries to use cloud storage paths instead of local paths.
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from database import SessionLocal, Catalog, CatalogPage, init_db
    from config import CLOUD_CATALOG_PREFIX, CLOUD_IMAGE_PREFIX
    from cloud_storage import cloud_storage
except ImportError as e:
    print(f"❌ Missing required modules: {e}")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def convert_catalog_paths():
    """Convert catalog PDF paths from local to cloud storage paths"""
    
    print("📄 Converting catalog PDF paths...")
    
    db = SessionLocal()
    try:
        # Get all catalogs with local paths
        catalogs = db.query(Catalog).all()
        
        converted_count = 0
        skipped_count = 0
        
        for catalog in catalogs:
            if not catalog.pdf_path:
                continue
                
            # Check if path is already a cloud path
            if catalog.pdf_path.startswith(CLOUD_CATALOG_PREFIX):
                logger.debug(f"Catalog {catalog.id} already has cloud path: {catalog.pdf_path}")
                skipped_count += 1
                continue
            
            # Convert local path to cloud path
            # Local paths might be like: "bilka_20250627_20250703.pdf" or "catalogs/bilka_20250627_20250703.pdf"
            filename = Path(catalog.pdf_path).name
            new_cloud_path = f"{CLOUD_CATALOG_PREFIX}{filename}"
            
            # Check if file exists in cloud storage
            if cloud_storage.file_exists(new_cloud_path):
                catalog.pdf_path = new_cloud_path
                logger.info(f"✅ Converted catalog {catalog.id}: {filename} -> {new_cloud_path}")
                converted_count += 1
            else:
                logger.warning(f"⚠️ File not found in cloud storage: {new_cloud_path} (Catalog {catalog.id})")
                skipped_count += 1
        
        # Commit changes
        db.commit()
        print(f"📊 Catalog conversion complete: {converted_count} converted, {skipped_count} skipped")
        return converted_count
        
    except Exception as e:
        logger.error(f"❌ Error converting catalog paths: {e}")
        db.rollback()
        return 0
    finally:
        db.close()

def convert_catalog_page_paths():
    """Convert catalog page image paths from local to cloud storage paths"""
    
    print("🖼️ Converting catalog page image paths...")
    
    db = SessionLocal()
    try:
        # Get all catalog pages with local paths
        pages = db.query(CatalogPage).all()
        
        converted_count = 0
        skipped_count = 0
        
        for page in pages:
            if not page.image_path:
                continue
                
            # Check if path is already a cloud path
            if page.image_path.startswith(CLOUD_IMAGE_PREFIX):
                logger.debug(f"Page {page.id} already has cloud path: {page.image_path}")
                skipped_count += 1
                continue
            
            # Convert local path to cloud path
            # Local paths might be like: "catalog_123/page_1.png" or "images/catalog_123/page_1.png"
            relative_path = page.image_path
            if relative_path.startswith("images/"):
                relative_path = relative_path[7:]  # Remove "images/" prefix
            
            new_cloud_path = f"{CLOUD_IMAGE_PREFIX}{relative_path}"
            
            # Check if file exists in cloud storage
            if cloud_storage.file_exists(new_cloud_path):
                page.image_path = new_cloud_path
                logger.info(f"✅ Converted page {page.id}: {relative_path} -> {new_cloud_path}")
                converted_count += 1
            else:
                logger.warning(f"⚠️ File not found in cloud storage: {new_cloud_path} (Page {page.id})")
                skipped_count += 1
        
        # Commit changes
        db.commit()
        print(f"📊 Page conversion complete: {converted_count} converted, {skipped_count} skipped")
        return converted_count
        
    except Exception as e:
        logger.error(f"❌ Error converting page paths: {e}")
        db.rollback()
        return 0
    finally:
        db.close()

def verify_cloud_storage_availability():
    """Verify that cloud storage is available before migration"""
    
    print("☁️ Verifying cloud storage availability...")
    
    if not cloud_storage.is_available():
        print("❌ Cloud storage is not available. Please check your configuration.")
        return False
    
    # Test basic operations
    try:
        files = cloud_storage.list_files()
        catalog_files = [f for f in files if f.startswith(CLOUD_CATALOG_PREFIX)]
        print(f"✅ Cloud storage is available. Found {len(catalog_files)} catalog files.")
        return True
    except Exception as e:
        print(f"❌ Error accessing cloud storage: {e}")
        return False

def create_backup():
    """Create a backup of current database state"""
    
    print("💾 Creating database backup...")
    
    db = SessionLocal()
    try:
        # Count current records
        catalog_count = db.query(Catalog).count()
        page_count = db.query(CatalogPage).count()
        
        backup_info = {
            "timestamp": datetime.now().isoformat(),
            "catalog_count": catalog_count,
            "page_count": page_count
        }
        
        print(f"📊 Current database state: {catalog_count} catalogs, {page_count} pages")
        
        # Save backup info to file
        backup_file = f"migration_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(backup_file, 'w') as f:
            f.write(f"Database Migration Backup\n")
            f.write(f"Timestamp: {backup_info['timestamp']}\n")
            f.write(f"Catalogs: {backup_info['catalog_count']}\n")
            f.write(f"Pages: {backup_info['page_count']}\n")
        
        print(f"✅ Backup info saved to: {backup_file}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating backup: {e}")
        return False
    finally:
        db.close()

def main():
    """Main migration function"""
    
    print("🚀 Database Path Migration to Cloud Storage")
    print("=" * 60)
    
    # Step 1: Verify cloud storage
    if not verify_cloud_storage_availability():
        print("❌ Migration aborted: Cloud storage not available")
        return False
    
    # Step 2: Create backup
    if not create_backup():
        print("❌ Migration aborted: Backup creation failed")
        return False
    
    # Step 3: Convert catalog paths
    catalog_converted = convert_catalog_paths()
    
    # Step 4: Convert page paths
    page_converted = convert_catalog_page_paths()
    
    # Step 5: Summary
    print("\n🎉 Migration Complete!")
    print("=" * 40)
    print(f"📄 Catalogs converted: {catalog_converted}")
    print(f"🖼️ Pages converted: {page_converted}")
    print(f"📊 Total conversions: {catalog_converted + page_converted}")
    
    if catalog_converted > 0 or page_converted > 0:
        print("\n✅ Database successfully migrated to cloud storage paths!")
        print("\n📋 Next steps:")
        print("   1. Test the application to ensure everything works")
        print("   2. Remove local file cleanup code")
        print("   3. Deploy updated application")
    else:
        print("\n⚠️ No paths were converted. Database may already be migrated.")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
