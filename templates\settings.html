{% extends "base.html" %}

{% block title %}Admin Settings{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="header">
        <h1>Admin Settings</h1>
        <p class="lead">Manage application-wide settings. Changes take effect immediately after saving.</p>
    </div>

    {% if message %}
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {% endif %}

    <form action="/settings" method="post">
        <!-- AI Model and API Settings -->
        <div class="section-container">
            <h2>AI & API Configuration</h2>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="GEMINI_PARSING_MODEL" class="form-label">Gemini Parsing Model</label>
                    <input type="text" class="form-control" id="GEMINI_PARSING_MODEL" name="GEMINI_PARSING_MODEL" value="{{ settings.GEMINI_PARSING_MODEL }}">
                    <div class="form-text">The model used for extracting products from catalogs.</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="GEMINI_QUERY_MODEL" class="form-label">Gemini Query Model</label>
                    <input type="text" class="form-control" id="GEMINI_QUERY_MODEL" name="GEMINI_QUERY_MODEL" value="{{ settings.GEMINI_QUERY_MODEL }}">
                     <div class="form-text">The model used for answering natural language questions.</div>
                </div>
            </div>
        </div>

        <!-- System Prompts -->
        <div class="section-container">
            <h2>AI System Prompts</h2>
            <div class="mb-3">
                <label for="GEMINI_PARSER_SYSTEM_PROMPT" class="form-label">Parser System Prompt</label>
                <textarea class="form-control" id="GEMINI_PARSER_SYSTEM_PROMPT" name="GEMINI_PARSER_SYSTEM_PROMPT" rows="10">{{- settings.GEMINI_PARSER_SYSTEM_PROMPT -}}</textarea>
                <div class="form-text">The master prompt guiding the AI in how to extract product data from catalog pages.</div>
            </div>
            <div class="mb-3">
                <label for="GEMINI_QUERY_SYSTEM_PROMPT" class="form-label">Query System Prompt</label>
                <textarea class="form-control" id="GEMINI_QUERY_SYSTEM_PROMPT" name="GEMINI_QUERY_SYSTEM_PROMPT" rows="10">{{- settings.GEMINI_QUERY_SYSTEM_PROMPT -}}</textarea>
                <div class="form-text">The master prompt guiding the AI in how to answer user questions based on available products.</div>
            </div>
        </div>

        <!-- Parser and Processing Settings -->
        <div class="section-container">
            <h2>Parser & Queue Settings</h2>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="PARSER_BATCH_SIZE" class="form-label">Parser Batch Size</label>
                    <input type="number" class="form-control" id="PARSER_BATCH_SIZE" name="PARSER_BATCH_SIZE" value="{{ settings.PARSER_BATCH_SIZE }}">
                    <div class="form-text">Number of catalogs to process in one go.</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="PARSER_MAX_RETRIES" class="form-label">Parser Max Retries</p>
                    <input type="number" class="form-control" id="PARSER_MAX_RETRIES" name="PARSER_MAX_RETRIES" value="{{ settings.PARSER_MAX_RETRIES }}">
                    <div class="form-text">Max retries for a failed page.</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="PARSER_SLEEP_INTERVAL" class="form-label">Queue Sleep Interval (s)</label>
                    <input type="number" class="form-control" id="PARSER_SLEEP_INTERVAL" name="PARSER_SLEEP_INTERVAL" value="{{ settings.PARSER_SLEEP_INTERVAL }}">
                    <div class="form-text">Seconds to wait between checking for new tasks.</div>
                </div>
                 <div class="col-md-4 mb-3">
                    <label for="GEMINI_API_DELAY_SECONDS" class="form-label">Gemini API Delay (s)</label>
                    <input type="number" class="form-control" id="GEMINI_API_DELAY_SECONDS" name="GEMINI_API_DELAY_SECONDS" value="{{ settings.GEMINI_API_DELAY_SECONDS }}">
                    <div class="form-text">Seconds to wait between individual Gemini API calls to avoid rate limits.</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="AI_DEBUG_MODE" class="form-label">AI Debug Mode</label>
                    <select class="form-select" id="AI_DEBUG_MODE" name="AI_DEBUG_MODE">
                        <option value="True" {% if settings.AI_DEBUG_MODE %}selected{% endif %}>Enabled</option>
                        <option value="False" {% if not settings.AI_DEBUG_MODE %}selected{% endif %}>Disabled</option>
                    </select>
                    <div class="form-text">Show AI debug info on the query page.</div>
                </div>
            </div>
        </div>

        <div class="text-center mb-5">
            <button type="submit" class="btn btn-primary btn-lg">Save All Settings</button>
        </div>
    </form>
</div>
{% endblock %}        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 1px solid rgba(226, 232, 240, 0.8);
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.15);
        }
        .form-label {
            font-weight: 500;
            color: #475569;
            margin-bottom: 0.5rem;
        }
        .api-key-info {
            font-size: 0.9rem;
            color: #64748b;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Application Settings</h1>
            <p class="lead">Configure your API keys and application settings</p>
        </div>

        <div class="d-flex justify-content-between mb-4">
            <a href="/" class="btn btn-outline-primary">← Back to Application</a>
        </div>

        <div class="section-container">
            <h2>API Keys</h2>
            <div id="apiStatus" class="alert {{ api_status_type }} mb-4">
                <strong>Current Status:</strong> {{ api_status }}
            </div>

            <form id="settingsForm">
                <div class="mb-3">
                    <label for="querySystemPrompt" class="form-label">Query System Prompt</label>
                    <textarea class="form-control" id="querySystemPrompt" name="query_system_prompt" rows="10" placeholder="Enter the system prompt for the query AI...">{{ current_query_system_prompt }}</textarea>
                    <small class="form-text text-muted">The base instructions given to the AI when answering user questions.</small>
                </div>

                <div class="mb-3">
                    <label for="parserSystemPrompt" class="form-label">Parser System Prompt</label>
                    <textarea class="form-control" id="parserSystemPrompt" name="parser_system_prompt" rows="10" placeholder="Enter the system prompt for the catalog parser AI...">{{ current_parser_system_prompt }}</textarea>
                    <small class="form-text text-muted">The instructions given to the AI when extracting product data from catalog pages.</small>
                </div>

                <h3 class="mt-5 mb-3">Model Settings</h3>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="defaultParsingModel" class="form-label">Default Parsing Model</label>
                        <select class="form-select" id="defaultParsingModel" name="parsing_model">
                            <option value="">Loading models...</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="defaultQueryModel" class="form-label">Default Query Model</label>
                        <select class="form-select" id="defaultQueryModel" name="query_model">
                            <option value="">Loading models...</option>
                        </select>
                    </div>
                </div>

                <h3 class="mt-5 mb-3">Rate Limiting</h3>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="initialRate" class="form-label">Initial Rate (RPM)</label>
                        <input type="number" class="form-control" id="initialRate" name="initial_rate" min="1" max="60" value="{{ current_initial_rate }}">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="maxRate" class="form-label">Maximum Rate (RPM)</label>
                        <input type="number" class="form-control" id="maxRate" name="max_rate" min="1" max="120" value="{{ current_max_rate }}">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="minRate" class="form-label">Minimum Rate (RPM)</label>
                        <input type="number" class="form-control" id="minRate" name="min_rate" min="1" max="30" value="{{ current_min_rate }}">
                    </div>
                </div>

                <!-- NEW Debugging Section -->
                <h3 class="mt-5 mb-3">Debugging</h3>
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" role="switch" id="showAiDebugOutput" name="show_ai_debug_output" {% if show_ai_debug_output %}checked{% endif %}>
                            <label class="form-check-label" for="showAiDebugOutput">Show AI Debug Output in Responses</label>
                            <small class="form-text text-muted d-block">If enabled, the raw `RELEVANT_PRODUCTS` JSON block from the AI will be visible in the chat response. Useful for debugging AI filtering.</small>
                        </div>
                    </div>
                </div>
                <!-- END NEW Debugging Section -->

                <div class="mt-5 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">Save Settings</button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Function to populate model selectors (if it exists here or is called)
            async function populateSettingsModelSelectors() {
                const parsingSelect = $('#defaultParsingModel');
                const querySelect = $('#defaultQueryModel');
                const currentParsingModel = "{{ current_parsing_model }}"; // Get from template
                const currentQueryModel = "{{ current_query_model }}";   // Get from template

                // Clear existing options but keep the first "Loading..." or placeholder
                parsingSelect.find('option:not(:first-child)').remove();
                querySelect.find('option:not(:first-child)').remove();

                try {
                    const response = await fetch('/models');
                    if (!response.ok) throw new Error('Failed to load models');
                    const models = await response.json();

                    if (models.length === 0) {
                        parsingSelect.find('option:first-child').text('No models available');
                        querySelect.find('option:first-child').text('No models available');
                    } else {
                        parsingSelect.find('option:first-child').text('Select a parsing model'); // Change placeholder
                        querySelect.find('option:first-child').text('Select a query model');   // Change placeholder
                        models.forEach(modelName => {
                            parsingSelect.append(new Option(modelName, modelName));
                            querySelect.append(new Option(modelName, modelName));
                        });
                        // Set selected based on current values from template
                        if (currentParsingModel) parsingSelect.val(currentParsingModel);
                        if (currentQueryModel) querySelect.val(currentQueryModel);
                    }
                } catch (error) {
                    console.error('Error populating model selectors on settings page:', error);
                    parsingSelect.find('option:first-child').text('Error loading models');
                    querySelect.find('option:first-child').text('Error loading models');
                }
            }

            // Save settings form submission
            $('#settingsForm').on('submit', async function(e) {
                e.preventDefault();
                const saveButton = $(this).find('button[type="submit"]');
                const statusSpan = $('#saveStatus');

                saveButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2"></span> Saving...');
                statusSpan.text('').removeClass('text-success text-danger');

                try {
                    // Get values from the form
                    const parsingModel = $('#defaultParsingModel').val();
                    const queryModel = $('#defaultQueryModel').val();
                    const initialRate = $('#initialRate').val();
                    const maxRate = $('#maxRate').val();
                    const minRate = $('#minRate').val();
                    const querySystemPrompt = $('#querySystemPrompt').val();
                    const parserSystemPrompt = $('#parserSystemPrompt').val();

                    // Construct payload - initially without toggle or api key
                    const payload = {
                        parsing_model: parsingModel,
                        query_model: queryModel,
                        initial_rate: initialRate,
                        max_rate: maxRate,
                        min_rate: minRate,
                        query_system_prompt: querySystemPrompt,
                        parser_system_prompt: parserSystemPrompt
                    };
                    
                    // --- NEW: Conditionally add toggle state ---
                    const showAiDebugOutputChecked = $('#showAiDebugOutput').is(':checked');
                    if (showAiDebugOutputChecked) {
                         payload.show_ai_debug_output = 'on'; // Only add if checked, send 'on'
                    }
                    // If unchecked, the key remains absent from payload, backend gets None.
                    // --- END NEW ---

                    console.log("DEBUG: Sending payload:", payload); // Optional: Debug log payload

                    const response = await fetch('/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            // Include Authorization header if needed for settings endpoint
                            'Authorization': `Bearer ${localStorage.getItem('authToken')}` 
                        },
                        body: JSON.stringify(payload)
                    });

                    const data = await response.json();

                    if (!response.ok) {
                        throw new Error(data.detail || data.message || 'Failed to save settings');
                    }

                    statusSpan.text(data.message || 'Settings saved successfully!').addClass('text-success');
                    
                    // Optionally: Reload models if API key was successfully changed/tested
                    // This might be handled by the test button logic already

                } catch (error) {
                    console.error('Error saving settings:', error);
                    statusSpan.text(`Error: ${error.message}`).addClass('text-danger');
                } finally {
                    saveButton.prop('disabled', false).text('Save Settings');
                }
            });

            // Call the function to populate model selectors when the document is ready
            populateSettingsModelSelectors();
        });
    </script>
</body>
</html> 