"use client";

import Link from 'next/link';
import { useEffect, useState } from 'react';
import { BackendStore } from 'lib/backend';

export default function Collections() {
  const [stores, setStores] = useState<BackendStore[]>([]);

  useEffect(() => {
    async function loadStores() {
      try {
        const res = await fetch('/api/stores');
        if (!res.ok) return;
        const data: BackendStore[] = await res.json();
        setStores(data);
      } catch (err) {
        console.error('Failed to fetch stores', err);
      }
    }
    loadStores();
  }, []);

  return (
    <aside className="space-y-2 text-sm">
      <h3 className="font-semibold uppercase tracking-wide text-neutral-500 dark:text-neutral-400">
        Stores
      </h3>
      <ul className="space-y-1">
        {stores.map((store) => (
          <li key={store.id}>
            <Link
              href={`/search/${encodeURIComponent(store.name)}`}
              className="text-neutral-600 hover:text-black dark:text-neutral-300 dark:hover:text-white"
            >
              {store.name}
            </Link>
          </li>
        ))}
      </ul>
    </aside>
  );
}
