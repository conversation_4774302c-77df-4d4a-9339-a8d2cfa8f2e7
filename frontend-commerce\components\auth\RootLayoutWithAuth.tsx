"use client";

import { ReactNode, useState, useEffect } from 'react';
import { AuthProvider } from '@/lib/auth';

export default function RootLayoutWithAuth({ children }: { children: ReactNode }) {
  const [mounted, setMounted] = useState(false);

  // Only render on client-side to prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  );
}
