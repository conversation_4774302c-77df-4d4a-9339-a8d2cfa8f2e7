import os
import shutil
import logging
import tempfile
from datetime import datetime, date
from sqlalchemy.orm import Session
from database import Catalog, CatalogPage, Store, SessionLocal
import fitz  # PyMuPDF
import io
from PIL import Image, UnidentifiedImageError
from google import genai
from google.genai import types
import json
from typing import Tuple, Optional, List, Dict
import re
from cloud_storage import cloud_storage, download_catalog_pdf, upload_catalog_images

# --- Import config FIRST ---
import config
from storage_factory import get_storage, StoragePaths

# Use logger from config if needed, or configure locally
logger = config.logger # Or keep local: logging.getLogger(__name__)

# load_dotenv() is now handled by config.py

# Configure local logging if not using api_logger
# if logger.name == __name__: # Check if we are using the fallback logger
#     logging.basicConfig(level=logging.INFO)

# Gemini API Key (now mainly used for date extraction fallback if needed)
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
    logger.warning("GOOGLE_API_KEY not found for pdf_extractor specific functions (e.g., date extraction fallback).")
# Configuration is primarily handled by api.py's reconfigure_gemini

# --- DPI Helper Function ---

def choose_dpi(file_size_bytes: int) -> int:
    """Return an appropriate DPI given the original PDF size.

    Size tiers (optimized for AI parsing quality while managing storage):
    – > 50 MB  → 200 DPI  (very large catalogs - minimum for AI readability)
    – > 25 MB  → 250 DPI  (large catalogs like Føtex)
    – > 10 MB  → 300 DPI  (medium catalogs)
    – else     → 350 DPI  (small catalogs - maximum quality)

    Note: AI parsing requires high resolution to read product names, prices, and details.
    Previous DPI settings (50-150) were too low for reliable text recognition.
    """
    if file_size_bytes > 50 * 1024 * 1024:
        return 200  # Minimum for AI parsing
    if file_size_bytes > 25 * 1024 * 1024:
        return 250  # Good balance for large files
    if file_size_bytes > 10 * 1024 * 1024:
        return 300  # High quality for medium files
    return 350      # Maximum quality for small files

# --- Poppler Path (Only relevant if pdf2image is actually used) ---
poppler_path = None
# --- End Poppler Path ---

DATE_EXTRACTION_PROMPT = """
You are an expert data extractor specializing in Danish supermarket catalogs.
Analyze the provided image, which is the first page of a catalog.
Extract the validity period for the offers shown.
Look for phrases like 'Gælder fra', 'Tilbudene gælder', 'Periode', etc., followed by dates.
Return the start date and end date in 'YYYY-MM-DD' format.
If only one date is found, assume it's the start date and leave the end date null.
If a week number is mentioned (e.g., 'Uge 35'), calculate the corresponding dates for the current year.
If no dates are found, return null for both.

Return ONLY a valid JSON object with keys 'valid_from' and 'valid_to'.
Example 1: {"valid_from": "2024-08-19", "valid_to": "2024-08-25"}
Example 2: {"valid_from": "2024-08-19", "valid_to": null}
Example 3: {"valid_from": null, "valid_to": null}
"""

def extract_pdf_pages(pdf_path, output_dir=None, dpi=150):
    """
    Extract pages from a PDF file and save them as images using PyMuPDF (fitz).

    Args:
        pdf_path: FULL path to the PDF file to process.
        output_dir: Base directory to save the images (Defaults to IMAGE_DIR).
        dpi: Resolution of the output images.

    Returns:
        Tuple of (relative path to the first page image, list of relative paths to all extracted images, unique subdirectory name)
        Returns (None, [], None) on failure.
    """
    if not output_dir:
        # Use a temporary directory for cloud storage processing
        import tempfile
        output_dir = tempfile.mkdtemp(prefix="pdf_extract_")
        logger.info(f"Using temporary output directory: {output_dir}")

    os.makedirs(output_dir, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    pdf_name_base = os.path.splitext(os.path.basename(pdf_path))[0]
    safe_pdf_name = re.sub(r'\s+', '_', pdf_name_base)
    subdir_name = f"{timestamp}_{safe_pdf_name}" # Relative subdirectory name

    # Create the full absolute path for the subdirectory where images will be saved
    pdf_output_dir_full_path = os.path.join(output_dir, subdir_name)
    os.makedirs(pdf_output_dir_full_path, exist_ok=True)

    image_paths_relative = []
    first_page_path_relative = None

    try:
        logger.info(f"Extracting pages from {pdf_path} using PyMuPDF to {pdf_output_dir_full_path}")
        pdf_document = fitz.open(pdf_path)
        zoom = dpi / 72

        for page_num in range(pdf_document.page_count):
            page = pdf_document[page_num]
            pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))
            image_filename = f"page_{page_num + 1}.jpg"

            # --- Store RELATIVE path for DB ---
            # Path relative to IMAGE_DIR (e.g., "2024..._catalog/page_1.jpg")
            image_path_relative = os.path.join(subdir_name, image_filename)
            # --- START FIX ---
            image_path_relative = image_path_relative.replace('\\', '/')
            # --- END FIX ---
            # --- Construct FULL path for saving the file ---
            image_path_full = os.path.join(pdf_output_dir_full_path, image_filename)

            pix.save(image_path_full, "JPEG", jpg_quality=85)

            image_paths_relative.append(image_path_relative) # Store the relative path
            if page_num == 0:
                first_page_path_relative = image_path_relative

        pdf_document.close()
        logger.info(f"Successfully extracted {len(image_paths_relative)} pages to {pdf_output_dir_full_path}")
        # Return RELATIVE paths and the SUBDIR NAME
        return first_page_path_relative, image_paths_relative, subdir_name

    except Exception as e:
        logger.error(f"An error occurred during PDF extraction from {pdf_path}: {e}", exc_info=True)
        if os.path.exists(pdf_output_dir_full_path):
             try:
                 shutil.rmtree(pdf_output_dir_full_path)
             except OSError as rm_error:
                 logger.error(f"Error cleaning up directory {pdf_output_dir_full_path}: {rm_error}")
        return None, [], None

def process_pdf_catalog(
    temp_pdf_path: str, # Path to the temporary uploaded PDF file
    store_name: str,
    original_filename: str, # The original name of the uploaded file
    title: Optional[str] = None,
    valid_from: Optional[datetime] = None,
    valid_to: Optional[datetime] = None,
    attempt_date_extraction: bool = True,
    parsing_model: Optional[str] = None,
    dpi: int = 150,
    db: Optional[Session] = None
) -> Tuple[Optional[Catalog], List[str]]:
    """
    Processes an uploaded PDF catalog:
    1. Copies the temp PDF to the persistent CATALOG_DIR with a unique name.
    2. Extracts pages as images into a unique subdirectory within IMAGE_DIR.
       Uses tiered DPI settings based on original PDF size.
    3. Optionally attempts date extraction from the first page image using Gemini.
    4. Registers the Catalog and CatalogPage entries in the database, storing RELATIVE paths.
    5. Cleans up files on failure.

    Args:
        temp_pdf_path: Path to the temporary uploaded PDF file.
        store_name: Name of the associated store.
        original_filename: The original filename from the upload.
        title: Optional title for the catalog.
        valid_from: Optional user-provided start date.
        valid_to: Optional user-provided end date.
        attempt_date_extraction: Whether to try Gemini date extraction if dates are missing.
        parsing_model: Specific Gemini model to use for date extraction.
        dpi: Resolution for extracted images.
        db: Optional database session.

    Returns:
        Tuple of (Catalog object, list of relative image paths) or (None, []) on failure.
    """
    close_db = False
    if db is None:
        db = SessionLocal()
        close_db = True

    # Define paths for cleanup in case of errors
    catalog_path_full = None
    pdf_image_dir_full = None # This will be the full path to the specific catalog's image subdir
    pdf_image_subdir_name = None # Relative name of the image subdir, already captured by extract_pdf_pages return

    try:
        # 1. Upload PDF to cloud storage instead of local CATALOG_DIR
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        safe_original_filename = re.sub(r'[^a-zA-Z0-9._-]', '_', original_filename)
        # --- Relative path/filename for DB ---
        catalog_filename_relative = f"{timestamp}_{safe_original_filename}"

        # --- Enterprise Cloud Storage Path ---
        # Use new country-aware structure: dk/fotex/2025-w30/catalog.pdf
        country_code = StoragePaths.detect_country_code(store_name)
        date_period = StoragePaths.generate_date_period(start_date, end_date)
        cloud_catalog_path = StoragePaths.catalog_path(
            filename=catalog_filename_relative,
            country_code=country_code,
            store_name=store_name,
            date_period=date_period
        )
        logger.info(f"📁 Using enterprise storage path: {cloud_catalog_path}")

        try:
            # Upload PDF to cloud storage using new storage abstraction
            storage = get_storage()
            if not storage.upload_file(temp_pdf_path, cloud_catalog_path):
                raise Exception(f"Failed to upload PDF to cloud storage: {cloud_catalog_path}")
            logger.info(f"Uploaded PDF {temp_pdf_path} to cloud storage {cloud_catalog_path}")
        except Exception as e:
            logger.error(f"Failed to upload PDF to cloud storage: {e}")
            raise # Re-raise to be caught by the main try-except block

        # --- Determine DPI based on original PDF file size ---
        original_pdf_size_bytes = os.path.getsize(temp_pdf_path)
        selected_dpi = choose_dpi(original_pdf_size_bytes)
        logger.info(
            f"Original PDF size: {original_pdf_size_bytes / (1024*1024):.2f}MB – using DPI {selected_dpi}"
        )
        # --- End DPI Determination ---

        # 2. Extract pages as images (using the *full* path of the copied catalog and selected DPI)
        first_page_path_relative, all_image_paths_relative, pdf_image_subdir_name = extract_pdf_pages(
            catalog_path_full, 
            dpi=selected_dpi # Pass the determined DPI
        )

        # --- Enhanced Validation --- 
        # Log exactly what was returned from the extraction function.
        logger.debug(f"extract_pdf_pages returned: {{'pages': len(all_image_paths_relative) if all_image_paths_relative is not None else 'None', 'subdir': pdf_image_subdir_name}}")

        # Stricter check: If the list of pages is empty or None, it's a critical failure.
        if not all_image_paths_relative:
            logger.error(f"CRITICAL: No images were extracted from {catalog_path_full}. Aborting catalog creation.")
            raise ValueError(f"Image extraction failed for {catalog_path_full}, no pages were returned.") # Raise to trigger cleanup

        # Use config.IMAGE_DIR for constructing full path for cleanup
        pdf_image_dir_full = os.path.join(config.IMAGE_DIR, pdf_image_subdir_name) 

        # 3. Attempt Date Extraction (if needed)
        final_valid_from = valid_from
        final_valid_to = valid_to
        # Construct full path for date extraction helper
        first_page_path_full = get_full_image_path(first_page_path_relative) if first_page_path_relative else None

        if attempt_date_extraction and first_page_path_full and (final_valid_from is None or final_valid_to is None):
            logger.info(f"Attempting date extraction from {first_page_path_full} using model {parsing_model or 'default'}")
            extracted_dates = extract_dates_from_image(first_page_path_full, model_name=parsing_model)
            if extracted_dates:
                logger.info(f"Extracted dates: {extracted_dates}")
                if final_valid_from is None and extracted_dates.get("valid_from"):
                    try: final_valid_from = datetime.strptime(extracted_dates["valid_from"], "%Y-%m-%d").date()
                    except (ValueError, TypeError): logger.warning(f"Could not parse extracted valid_from date: {extracted_dates['valid_from']}")
                if final_valid_to is None and extracted_dates.get("valid_to"):
                    try: final_valid_to = datetime.strptime(extracted_dates["valid_to"], "%Y-%m-%d").date()
                    except (ValueError, TypeError): logger.warning(f"Could not parse extracted valid_to date: {extracted_dates['valid_to']}")
            else:
                logger.warning("Date extraction attempted but failed or returned no dates.")

        # 4. Register in Database
        store = db.query(Store).filter(Store.name == store_name).first()
        if not store:
             logger.error(f"Store '{store_name}' not found during catalog registration.")
             raise ValueError(f"Store not found: {store_name}") # Trigger cleanup

        catalog = Catalog(
            store_id=store.id,
            title=title or f"{store_name} Catalog {datetime.now().strftime('%Y-%m-%d')}",
            # Ensure dates are stored as date objects if not None
            valid_from=(final_valid_from.date() if isinstance(final_valid_from, datetime) else final_valid_from) or datetime.now().date(), 
            valid_to=final_valid_to.date() if isinstance(final_valid_to, datetime) else final_valid_to,
            pdf_path=catalog_filename_relative # Store the RELATIVE path/filename
        )
        db.add(catalog)
        db.flush() # Get catalog ID before adding pages

        pages_to_add = []
        for i, img_path_relative in enumerate(all_image_paths_relative):
            page = CatalogPage(
                catalog_id=catalog.id,
                page_number=i + 1,
                image_path=img_path_relative # Store the RELATIVE path
            )
            pages_to_add.append(page)
        db.add_all(pages_to_add)

        db.commit()
        db.refresh(catalog) # Refresh to load relationships if needed
        logger.info(f"Registered catalog '{catalog.title}' (ID: {catalog.id}) with {len(all_image_paths_relative)} pages.")

        # Success - return catalog and relative image paths
        return catalog, all_image_paths_relative

    except Exception as e:
        logger.error(f"Error processing catalog '{original_filename}': {e}", exc_info=True)
        if db: db.rollback() # Rollback DB changes if any occurred

        # Clean up files
        logger.info(f"Cleaning up files due to error processing {original_filename}")
        if catalog_path_full and os.path.exists(catalog_path_full):
            try: os.remove(catalog_path_full)
            except OSError as rm_err: logger.error(f"Error cleaning up PDF {catalog_path_full}: {rm_err}")
        if pdf_image_dir_full and os.path.exists(pdf_image_dir_full):
            try: shutil.rmtree(pdf_image_dir_full)
            except OSError as rm_err: logger.error(f"Error cleaning up image dir {pdf_image_dir_full}: {rm_err}")

        return None, [] # Indicate failure

    finally:
        if close_db and db:
            db.close()

def extract_dates_from_image(image_path: str, model_name: Optional[str] = None) -> Optional[Dict[str, Optional[str]]]:
    """
    Uses Gemini to extract validity dates from a catalog page image.

    Args:
        image_path: FULL path to the image file.
        model_name: Specific Gemini model name to use (optional).

    Returns:
        A dictionary {'valid_from': 'YYYY-MM-DD', 'valid_to': 'YYYY-MM-DD'} or None.
        Dates can be None if not found.
    """
    if not os.path.exists(image_path):
        logger.error(f"Image file not found for date extraction: {image_path}")
        return None

    # Check if the global Gemini object is configured (implicitly by api.py)
    # Attempting to use genai directly might fail if api.py hasn't configured it.
    # A better approach might be to pass the configured genai object or model instance.
    # For now, assume api.py's configure step worked.
    
    try:
        # Ensure genai is available (check if imported and configured)

        # Fallback genai.configure calls removed as they are based on old SDK and problematic.

        img = Image.open(image_path)

        # Use default parsing model from env if not specified
        # This model is for date extraction, can be different from main product parsing model
        effective_model_name = model_name or os.getenv("GEMINI_DATE_EXTRACTION_MODEL", os.getenv("GEMINI_PARSING_MODEL", "models/gemini-2.5-flash-preview-04-17"))

        logger.info(f"Using model {effective_model_name} for date extraction from {os.path.basename(image_path)}")

        # --- NEW WAY using unified google-genai SDK ---
        client = genai.Client(api_key=os.getenv("GOOGLE_API_KEY"))

        # Get generation parameters from database
        temperature = config.get_float_setting_from_db("GEMINI_TEMPERATURE", 0.7)
        top_p = config.get_float_setting_from_db("GEMINI_TOP_P", 0.95)
        top_k = config.get_int_setting_from_db("GEMINI_TOP_K", 20)
        max_output_tokens = config.get_int_setting_from_db("GEMINI_MAX_OUTPUT_TOKENS", 2048)

        # Get safety settings from database
        safety_settings = [
            types.SafetySetting(
                category="HARM_CATEGORY_HATE_SPEECH",
                threshold=config.get_setting_from_db("GEMINI_SAFETY_HATE_SPEECH", "BLOCK_ONLY_HIGH")
            ),
            types.SafetySetting(
                category="HARM_CATEGORY_DANGEROUS_CONTENT",
                threshold=config.get_setting_from_db("GEMINI_SAFETY_DANGEROUS_CONTENT", "BLOCK_ONLY_HIGH")
            ),
            types.SafetySetting(
                category="HARM_CATEGORY_HARASSMENT",
                threshold=config.get_setting_from_db("GEMINI_SAFETY_HARASSMENT", "BLOCK_ONLY_HIGH")
            ),
            types.SafetySetting(
                category="HARM_CATEGORY_SEXUALLY_EXPLICIT",
                threshold=config.get_setting_from_db("GEMINI_SAFETY_SEXUALLY_EXPLICIT", "BLOCK_ONLY_HIGH")
            )
        ]

        # Convert PIL Image to bytes for the new GenAI SDK
        import io
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        img_bytes = img_bytes.getvalue()

        contents = [
            types.Content(
                role='user',
                parts=[
                    types.Part.from_text(text=f"System: {DATE_EXTRACTION_PROMPT}"),
                    types.Part.from_text(text="Extract validity dates from this catalog page image based on your instructions."),
                    types.Part.from_bytes(data=img_bytes, mime_type='image/png')
                ]
            )
        ]

        response = client.models.generate_content(
            model=effective_model_name,
            contents=contents,
            config=types.GenerateContentConfig(
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                max_output_tokens=max_output_tokens,
                safety_settings=safety_settings,
                response_mime_type="application/json"
            )
        )
        # --- END NEW WAY ---

        response_text = response.text.strip()
        logger.debug(f"Date extraction raw response: {response_text}")

        try:
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            if start_idx != -1 and end_idx != -1:
                json_text = response_text[start_idx:end_idx]
                dates = json.loads(json_text)
                if isinstance(dates, dict) and ('valid_from' in dates or 'valid_to' in dates):
                     dates.setdefault('valid_from', None)
                     dates.setdefault('valid_to', None)
                     if not (dates['valid_from'] is None or isinstance(dates['valid_from'], str)): dates['valid_from'] = None
                     if not (dates['valid_to'] is None or isinstance(dates['valid_to'], str)): dates['valid_to'] = None
                     # Simple regex check for YYYY-MM-DD format (or null)
                     date_pattern = re.compile(r"^(\d{4}-\d{2}-\d{2})?$")
                     if (dates['valid_from'] is not None and not date_pattern.match(dates['valid_from'])) or \
                        (dates['valid_to'] is not None and not date_pattern.match(dates['valid_to'])):
                         logger.warning(f"Extracted dates do not match YYYY-MM-DD format: {dates}")
                         # Decide whether to return potentially invalid dates or None
                         # Returning None for safer parsing downstream
                         return None 
                     return dates
                else:
                    logger.warning(f"Extracted JSON not in expected format: {json_text}")
                    return None
            else:
                logger.warning(f"No JSON object found in date extraction response: {response_text}")
                return None
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from date extraction response: {e} | Response: {response_text}")
            return None

    except UnidentifiedImageError:
        logger.error(f"Cannot identify image file: {image_path}")
        return None
    except Exception as e:
        # Catch potential genai API errors (e.g., key invalid, quota exceeded)
        logger.error(f"Error during Gemini date extraction from {image_path}: {e}", exc_info=True)
        return None

def _process_pdf_unified(
    pdf_source: str,  # Can be local path or cloud path (gs://...)
    store_name: str,
    title: Optional[str] = None,
    valid_from: Optional[datetime] = None,
    valid_to: Optional[datetime] = None,
    attempt_date_extraction: bool = True,
    parsing_model: Optional[str] = None,
    dpi: Optional[int] = None,  # None = auto-select based on file size
    db: Optional[Session] = None
) -> Tuple[Optional[Catalog], List[str]]:
    """
    Unified PDF processing function that handles all sources and preserves all functionality.

    Features preserved:
    - AI-powered date extraction
    - Intelligent DPI selection based on file size
    - StoragePaths abstraction
    - Comprehensive error handling
    - Cloud-first architecture

    Args:
        pdf_source: Local file path OR cloud storage path (gs://...)
        store_name: Name of the store
        title: Optional catalog title
        valid_from: Optional start date (datetime object)
        valid_to: Optional end date (datetime object)
        attempt_date_extraction: Whether to extract dates using Gemini AI
        parsing_model: Gemini model for date extraction
        dpi: DPI for extraction (None = auto-select based on file size)
        db: Database session

    Returns:
        Tuple of (Catalog object or None, list of cloud image paths)
    """
    close_db = False
    if db is None:
        db = SessionLocal()
        close_db = True

    # Paths for cleanup
    temp_pdf_path = None
    temp_image_dir = None

    try:
        # 🚨 FAIL FAST: Verify cloud storage is available
        if not cloud_storage.is_available():
            raise Exception("Cloud storage unavailable - cannot process PDF")

        # 1. GET LOCAL PDF FILE (download if needed)
        if pdf_source.startswith("gs://") or pdf_source.startswith("https://storage.googleapis.com/") or pdf_source.startswith("catalogs/") or pdf_source.startswith("images/"):
            # Cloud source - download to temp location
            if not cloud_storage.file_exists(pdf_source):
                raise Exception(f"PDF not found in cloud storage: {pdf_source}")

            temp_pdf_path = cloud_storage.get_temp_download_path(pdf_source)
            if not cloud_storage.download_file(pdf_source, temp_pdf_path):
                raise Exception(f"Failed to download PDF from cloud: {pdf_source}")

            local_pdf_path = temp_pdf_path
            cloud_pdf_path = pdf_source
            logger.info(f"📥 Downloaded PDF from cloud: {pdf_source}")
        else:
            # Local source
            if not os.path.exists(pdf_source):
                raise Exception(f"Local PDF file not found: {pdf_source}")

            local_pdf_path = pdf_source
            # For local files, we'll upload to cloud storage for consistency
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            safe_store_name = re.sub(r'[^a-zA-Z0-9._-]', '_', store_name)
            cloud_filename = f"{timestamp}_{safe_store_name}.pdf"

            # Use enterprise storage structure
            country_code = StoragePaths.detect_country_code(store_name)
            date_period = StoragePaths.generate_date_period(final_valid_from_date, final_valid_to_date)
            cloud_pdf_path = StoragePaths.catalog_path(
                filename=cloud_filename,
                country_code=country_code,
                store_name=store_name,
                date_period=date_period
            )
            logger.info(f"📁 Enterprise storage path: {cloud_pdf_path}")

            if not cloud_storage.upload_file(local_pdf_path, cloud_pdf_path):
                raise Exception(f"Failed to upload PDF to cloud storage: {cloud_pdf_path}")
            logger.info(f"📤 Uploaded PDF to cloud: {cloud_pdf_path}")

        # 2. INTELLIGENT DPI SELECTION (preserve existing logic)
        file_size_bytes = os.path.getsize(local_pdf_path)
        selected_dpi = dpi if dpi is not None else choose_dpi(file_size_bytes)
        logger.info(f"PDF size: {file_size_bytes / (1024*1024):.2f}MB → using DPI {selected_dpi}")

        # 3. CREATE TEMPORARY EXTRACTION DIRECTORY
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        temp_image_dir = tempfile.mkdtemp(prefix=f"avis_extract_{timestamp}_")

        # 4. EXTRACT PAGES TO TEMP DIRECTORY
        logger.info(f"Extracting pages from PDF using PyMuPDF at {selected_dpi} DPI")
        pdf_document = fitz.open(local_pdf_path)
        zoom = selected_dpi / 72
        local_image_paths = []

        for page_num in range(pdf_document.page_count):
            page = pdf_document[page_num]
            pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))
            image_filename = f"page_{page_num + 1}.jpg"
            local_image_path = os.path.join(temp_image_dir, image_filename)

            pix.save(local_image_path, "JPEG", jpg_quality=85)
            local_image_paths.append(local_image_path)

        pdf_document.close()

        if not local_image_paths:
            raise Exception("No pages extracted from PDF")

        logger.info(f"✅ Extracted {len(local_image_paths)} pages")

        # 5. RESOLVE STORE (create if needed)
        store = db.query(Store).filter(Store.name == store_name).first()
        if not store:
            store = Store(name=store_name)
            db.add(store)
            db.flush()

        # 6. AI DATE EXTRACTION (preserve existing functionality)
        final_valid_from = valid_from
        final_valid_to = valid_to

        if attempt_date_extraction and (final_valid_from is None or final_valid_to is None):
            first_page_path = local_image_paths[0]
            logger.info(f"🤖 Attempting AI date extraction from first page using model {parsing_model or 'default'}")

            extracted_dates = extract_dates_from_image(first_page_path, model_name=parsing_model)
            if extracted_dates:
                logger.info(f"📅 Extracted dates: {extracted_dates}")

                if final_valid_from is None and extracted_dates.get("valid_from"):
                    try:
                        final_valid_from = datetime.strptime(extracted_dates["valid_from"], "%Y-%m-%d")
                    except (ValueError, TypeError):
                        logger.warning(f"Could not parse extracted valid_from: {extracted_dates['valid_from']}")

                if final_valid_to is None and extracted_dates.get("valid_to"):
                    try:
                        final_valid_to = datetime.strptime(extracted_dates["valid_to"], "%Y-%m-%d")
                    except (ValueError, TypeError):
                        logger.warning(f"Could not parse extracted valid_to: {extracted_dates['valid_to']}")
            else:
                logger.warning("Date extraction attempted but failed")

        # 7. CREATE OR UPDATE CATALOG RECORD
        final_valid_from_date = (final_valid_from.date() if isinstance(final_valid_from, datetime) else final_valid_from) or date.today()
        final_valid_to_date = final_valid_to.date() if isinstance(final_valid_to, datetime) else final_valid_to

        # Check for existing catalog with same store and date range
        existing_catalog = db.query(Catalog).filter(
            Catalog.store_id == store.id,
            Catalog.valid_from == final_valid_from_date,
            Catalog.valid_to == final_valid_to_date
        ).first()

        if existing_catalog:
            logger.info(f"Found existing catalog (ID: {existing_catalog.id}) - updating PDF path and reusing")
            existing_catalog.pdf_path = cloud_pdf_path
            existing_catalog.title = title or existing_catalog.title
            catalog = existing_catalog
            db.flush()
        else:
            catalog = Catalog(
                store_id=store.id,
                title=title or f"{store_name} Catalog {datetime.now().strftime('%Y-%m-%d')}",
                valid_from=final_valid_from_date,
                valid_to=final_valid_to_date,
                pdf_path=cloud_pdf_path
            )
            db.add(catalog)
            db.flush()  # Get catalog ID

        # 8. CLEAN UP EXISTING PAGES (if reusing catalog) AND UPLOAD NEW IMAGES
        if existing_catalog:
            # Remove existing catalog pages for this catalog
            existing_pages = db.query(CatalogPage).filter(CatalogPage.catalog_id == catalog.id).all()
            for page in existing_pages:
                # Optionally delete old images from cloud storage
                if page.image_path:
                    try:
                        cloud_storage.delete_file(page.image_path)
                    except Exception as e:
                        logger.warning(f"Failed to delete old image {page.image_path}: {e}")
                db.delete(page)
            logger.info(f"🧹 Cleaned up {len(existing_pages)} existing pages for catalog {catalog.id}")

        cloud_image_paths = []

        for i, local_image_path in enumerate(local_image_paths):
            # Use enterprise storage structure for images
            image_filename = f"page_{i + 1:03d}.jpg"
            country_code = StoragePaths.detect_country_code(store.name)
            date_period = StoragePaths.generate_date_period(catalog.valid_from, catalog.valid_to)
            cloud_image_path = StoragePaths.image_path(
                filename=image_filename,
                country_code=country_code,
                store_name=store.name,
                date_period=date_period
            )

            # Upload to cloud storage
            if cloud_storage.upload_file(local_image_path, cloud_image_path):
                cloud_image_paths.append(cloud_image_path)

                # Create database record
                catalog_page = CatalogPage(
                    catalog_id=catalog.id,
                    page_number=i + 1,
                    image_path=cloud_image_path
                )
                db.add(catalog_page)

                logger.info(f"📤 Uploaded page {i + 1} to cloud")
            else:
                logger.error(f"❌ Failed to upload page {i + 1}")
                raise Exception(f"Failed to upload page {i + 1} to cloud storage")

        # 9. COMMIT TRANSACTION
        db.commit()
        db.refresh(catalog)

        logger.info(f"✅ Successfully processed catalog {catalog.id} with {len(cloud_image_paths)} pages")
        return catalog, cloud_image_paths

    except Exception as e:
        logger.error(f"❌ PDF processing failed: {e}", exc_info=True)
        if db:
            db.rollback()
        return None, []

    finally:
        # 10. CLEANUP (always runs)
        if temp_pdf_path and os.path.exists(temp_pdf_path):
            try:
                os.remove(temp_pdf_path)
                logger.info(f"🧹 Cleaned up temp PDF: {temp_pdf_path}")
            except Exception as e:
                logger.warning(f"⚠️ Failed to clean up temp PDF: {e}")

        if temp_image_dir and os.path.exists(temp_image_dir):
            try:
                shutil.rmtree(temp_image_dir)
                logger.info(f"🧹 Cleaned up temp image directory")
            except Exception as e:
                logger.warning(f"⚠️ Failed to clean up temp directory: {e}")

        if close_db and db:
            db.close()

def extract_text_from_image(image_path: str) -> Optional[str]:
    """Extract text from an image file using Gemini Vision. (Seems unused currently)"""
    logger.warning("extract_text_from_image function was called but seems unused.")
    # Add implementation if needed, ensuring full path `image_path` is used
    # and genai is configured.
    return None

# --- Legacy helper functions removed ---
# These functions are no longer needed as we use cloud storage URLs directly
# get_full_image_path() and get_full_catalog_path() have been replaced with cloud storage URLs

# --- Standalone Execution ---
if __name__ == "__main__":
    import sys
    if len(sys.argv) < 3: # Need at least pdf and store name
        print("Usage: python pdf_extractor.py <temp_pdf_file_path> <store_name> [title] [YYYY-MM-DD_from] [YYYY-MM-DD_to]")
        sys.exit(1)

    temp_pdf = sys.argv[1]
    store = sys.argv[2]
    original_name = os.path.basename(temp_pdf) # Use filename as original name for testing
    cli_title = sys.argv[3] if len(sys.argv) > 3 else None
    cli_valid_from = datetime.strptime(sys.argv[4], "%Y-%m-%d") if len(sys.argv) > 4 else None
    cli_valid_to = datetime.strptime(sys.argv[5], "%Y-%m-%d") if len(sys.argv) > 5 else None
    
    # Ensure the temp file exists
    if not os.path.exists(temp_pdf):
        print(f"Error: Input PDF file not found: {temp_pdf}")
        sys.exit(1)

    print(f"Processing {original_name} for store '{store}'...")
    # Use standalone DB session
    db_session = SessionLocal()
    try:
        catalog, image_paths = process_pdf_catalog(
            temp_pdf_path=temp_pdf,
            store_name=store,
            original_filename=original_name,
            title=cli_title,
            valid_from=cli_valid_from,
            valid_to=cli_valid_to,
            attempt_date_extraction=True, # Default to attempt extraction
            db=db_session
        )

        if catalog:
            print("\n--- Success ---")
            print(f"Catalog processed successfully!")
            print(f"  ID: {catalog.id}")
            print(f"  Title: {catalog.title}")
            print(f"  Store: {catalog.store.name}")
            print(f"  Valid From: {catalog.valid_from}")
            print(f"  Valid To: {catalog.valid_to}")
            print(f"  PDF Path (Relative): {catalog.pdf_path}")
            print(f"  Pages Extracted: {len(image_paths)}")
            print(f"  First Page Image (Relative): {image_paths[0] if image_paths else 'N/A'}")
            # Test helper functions
            print(f"  Full PDF Path (Helper): {get_full_catalog_path(catalog.pdf_path)}")
            if image_paths:
                 print(f"  Full First Image Path (Helper): {get_full_image_path(image_paths[0])}")

        else:
            print("\n--- Failure ---")
            print("Failed to process catalog.")
    finally:
        db_session.close()
        print("\nDB Session closed.")

# Legacy function removed - now handled by _process_pdf_unified()

def process_pdf_catalog_from_cloud(
    cloud_pdf_path: str,
    store_name: str,
    title: Optional[str] = None,
    valid_from: Optional[datetime] = None,
    valid_to: Optional[datetime] = None,
    attempt_date_extraction: bool = True,
    parsing_model: Optional[str] = None,
    dpi: Optional[int] = None,
    db: Optional[Session] = None
) -> Tuple[Optional[Catalog], List[str]]:
    """
    Process PDF from cloud storage - now uses unified core function.
    Signature unchanged for backward compatibility.
    """
    return _process_pdf_unified(
        pdf_source=cloud_pdf_path,
        store_name=store_name,
        title=title,
        valid_from=valid_from,
        valid_to=valid_to,
        attempt_date_extraction=attempt_date_extraction,
        parsing_model=parsing_model,
        dpi=dpi,
        db=db
    )

def process_pdf_catalog(
    temp_pdf_path: str,
    store_name: str,
    original_filename: str,
    title: Optional[str] = None,
    valid_from: Optional[datetime] = None,
    valid_to: Optional[datetime] = None,
    attempt_date_extraction: bool = True,
    parsing_model: Optional[str] = None,
    dpi: Optional[int] = None,
    db: Optional[Session] = None
) -> Tuple[Optional[Catalog], List[str]]:
    """
    Process PDF from local upload - now uses unified core function.
    Signature unchanged for backward compatibility.
    """
    return _process_pdf_unified(
        pdf_source=temp_pdf_path,
        store_name=store_name,
        title=title,
        valid_from=valid_from,
        valid_to=valid_to,
        attempt_date_extraction=attempt_date_extraction,
        parsing_model=parsing_model,
        dpi=dpi,
        db=db
    )

