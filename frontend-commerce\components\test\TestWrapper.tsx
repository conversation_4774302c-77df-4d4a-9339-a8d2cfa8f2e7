"use client";
import { ReactNode } from 'react';

interface TestWrapperProps {
  children: ReactNode;
  fallbackTitle?: string;
  fallbackMessage?: string;
}

/**
 * Simple failsafe wrapper for test components
 * Prevents test components from breaking production builds
 */
export default function TestWrapper({
  children,
  fallbackTitle = "Test Component",
  fallbackMessage = "Test components are disabled in production"
}: TestWrapperProps) {
  // Simple check: only enable in development
  const isTestEnabled = process.env.NODE_ENV === 'development';

  // Production fallback - simple and safe
  if (!isTestEnabled) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">{fallbackTitle}</h1>
            <p className="text-gray-600 mb-4">{fallbackMessage}</p>
            <div className="bg-gray-50 rounded p-3 text-sm text-gray-500">
              <p>This is a development-only component</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // In development, render the test component
  return <>{children}</>;
}
