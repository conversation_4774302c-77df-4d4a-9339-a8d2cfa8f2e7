#!/usr/bin/env python3
"""
Debug script to inspect Netto download button issue
"""

import asyncio
import logging
from pathlib import Path
from playwright.async_api import async_playwright

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_netto_download():
    """Debug the Netto download button issue"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, channel="chrome")
        context = await browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        page = await context.new_page()
        
        try:
            # Navigate to Netto
            logger.info("Navigating to Netto...")
            await page.goto("https://netto.dk/netto-avisen/", wait_until="load", timeout=70000)
            await asyncio.sleep(2)
            
            # Handle cookies
            logger.info("Handling cookies...")
            try:
                cookie_button = page.locator("#coiPage-1 button.coi-banner__accept").first
                await cookie_button.wait_for(state="visible", timeout=15000)
                await cookie_button.click()
                await asyncio.sleep(1)
                logger.info("Cookies accepted")
            except Exception as e:
                logger.warning(f"Cookie handling failed: {e}")
            
            # Find and click catalog tile
            logger.info("Looking for catalog tile...")
            tile_selector = "div.flex.items-center.justify-center.gap-2.capitalize"
            tile = page.locator(tile_selector).first
            await tile.wait_for(state="visible", timeout=20000)
            
            # Get date info
            date_selector = "span.text-paragraph-sm-bold"
            date_element = tile.locator(date_selector).first
            date_text = await date_element.text_content()
            logger.info(f"Found date: {date_text}")
            
            # Click tile
            logger.info("Clicking catalog tile...")
            await tile.click()
            await asyncio.sleep(2)
            
            # Check what's on the page now
            logger.info("Checking page state after click...")
            
            # Look for download button
            download_selector = "button[aria-label='Download leaflet']"
            download_buttons = await page.locator(download_selector).all()
            logger.info(f"Found {len(download_buttons)} download buttons with selector: {download_selector}")
            
            # Try alternative selectors
            alt_selectors = [
                "button[aria-label*='Download']",
                "button[aria-label*='download']",
                "button:has-text('Download')",
                "button:has-text('download')",
                "a[href*='.pdf']",
                "[download]",
                "button[data-testid*='download']",
                ".download-button",
                "#download-button"
            ]
            
            for selector in alt_selectors:
                elements = await page.locator(selector).all()
                if elements:
                    logger.info(f"Found {len(elements)} elements with selector: {selector}")
                    for i, elem in enumerate(elements):
                        try:
                            text = await elem.text_content()
                            aria_label = await elem.get_attribute('aria-label')
                            href = await elem.get_attribute('href')
                            logger.info(f"  Element {i}: text='{text}', aria-label='{aria_label}', href='{href}'")
                        except Exception as e:
                            logger.warning(f"  Element {i}: Could not get attributes: {e}")
            
            # Take a screenshot for inspection
            screenshot_path = Path("debug_netto_overlay.png")
            await page.screenshot(path=str(screenshot_path))
            logger.info(f"Screenshot saved: {screenshot_path}")
            
            # Wait for manual inspection
            logger.info("Waiting 30 seconds for manual inspection...")
            await asyncio.sleep(30)
            
        except Exception as e:
            logger.error(f"Error during debug: {e}", exc_info=True)
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_netto_download())
