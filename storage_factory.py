"""
Storage Factory - Creates appropriate storage backend based on configuration
Provides a single entry point for all storage operations
"""

import os
import logging
from typing import Op<PERSON>
from storage_interface import StorageInterface
from google_cloud_storage import GoogleCloudStorage
from local_file_storage import LocalFileStorage

logger = logging.getLogger(__name__)


class StorageFactory:
    """Factory class to create appropriate storage backend"""
    
    _instance: Optional[StorageInterface] = None
    
    @classmethod
    def create_storage(cls, force_recreate: bool = False) -> StorageInterface:
        """
        Create and return appropriate storage backend based on configuration
        Uses singleton pattern to avoid recreating storage clients
        
        Args:
            force_recreate: Force creation of new storage instance
            
        Returns:
            StorageInterface implementation (GoogleCloudStorage or LocalFileStorage)
        """
        if cls._instance is None or force_recreate:
            cls._instance = cls._create_storage_backend()
        
        return cls._instance
    
    @classmethod
    def _create_storage_backend(cls) -> StorageInterface:
        """Create the appropriate storage backend based on environment"""
        
        # Check environment variables to determine storage type
        use_cloud_storage = cls._should_use_cloud_storage()
        
        if use_cloud_storage:
            try:
                logger.info("Creating Google Cloud Storage backend")
                bucket_name = os.getenv('GOOGLE_CLOUD_BUCKET', 'tilbudsjaegeren')
                return GoogleCloudStorage(bucket_name=bucket_name)
            except Exception as e:
                logger.error(f"Failed to create Google Cloud Storage backend: {e}")
                logger.warning("Falling back to local file storage")
                return cls._create_local_storage()
        else:
            logger.info("Creating Local File Storage backend")
            return cls._create_local_storage()
    
    @classmethod
    def _should_use_cloud_storage(cls) -> bool:
        """Determine if cloud storage should be used based on environment"""
        
        # Explicit environment variable
        use_cloud = os.getenv('USE_CLOUD_STORAGE', '').lower()
        if use_cloud in ('true', '1', 'yes', 'on'):
            return True
        elif use_cloud in ('false', '0', 'no', 'off'):
            return False
        
        # Auto-detect based on environment
        # Use cloud storage if:
        # 1. Running on Render (has RENDER_SERVICE_NAME or RENDER_DISK_MOUNT_PATH)
        # 2. Google Cloud credentials are available
        # 3. Production environment

        if (os.getenv('RENDER_SERVICE_NAME') or
            os.getenv('RENDER_DISK_MOUNT_PATH') or
            os.getenv('RENDER')):
            logger.info("Detected Render environment - using cloud storage")
            return True
        
        if os.getenv('NODE_ENV') == 'production' or os.getenv('ENVIRONMENT') == 'production':
            logger.info("Detected production environment - using cloud storage")
            return True
        
        # Check for Google Cloud credentials
        if (os.getenv('GOOGLE_APPLICATION_CREDENTIALS') or
            os.getenv('GOOGLE_CLOUD_PROJECT') or
            os.getenv('GCLOUD_PROJECT') or
            os.getenv('GOOGLE_CLOUD_CREDENTIALS_JSON')):
            logger.info("Detected Google Cloud credentials - using cloud storage")
            return True
        
        # Default to local storage for development
        logger.info("No cloud storage indicators found - using local storage")
        return False
    
    @classmethod
    def _create_local_storage(cls) -> LocalFileStorage:
        """Create local file storage backend"""
        base_dir = os.getenv('LOCAL_STORAGE_DIR', 'local_storage')
        return LocalFileStorage(base_dir=base_dir)
    
    @classmethod
    def get_storage_info(cls) -> dict:
        """Get information about the current storage configuration"""
        storage = cls.create_storage()
        
        info = {
            'type': type(storage).__name__,
            'use_cloud_storage': cls._should_use_cloud_storage(),
        }
        
        if isinstance(storage, GoogleCloudStorage):
            info.update({
                'bucket_name': storage.bucket_name,
                'project_id': storage.client.project if hasattr(storage.client, 'project') else 'unknown'
            })
        elif isinstance(storage, LocalFileStorage):
            info.update({
                'base_directory': str(storage.base_dir),
                'absolute_path': str(storage.base_dir.absolute())
            })
        
        return info
    
    @classmethod
    def reset_storage(cls):
        """Reset the storage instance (useful for testing)"""
        cls._instance = None


# Convenience function for easy access
def get_storage() -> StorageInterface:
    """Get the configured storage backend"""
    return StorageFactory.create_storage()


# Storage path helpers
class StoragePaths:
    """
    Helper class for consistent storage path management
    Implements battle-tested enterprise patterns for multi-country expansion

    Structure: {country_code}/{store_name}/{date_period}/{filename}
    Example: dk/fotex/2025-w27/catalog.pdf
    """

    # Legacy paths for backward compatibility
    CATALOGS = "catalogs/"
    IMAGES = "images/"
    LOGOS = "logos/"
    STATIC = "static/"
    TEMP = "temp/"

    @classmethod
    def catalog_path(cls, filename: str, country_code: str = None, store_name: str = None, date_period: str = None) -> str:
        """
        Generate catalog storage path with country-aware structure

        Args:
            filename: The catalog filename (e.g., "catalog.pdf")
            country_code: ISO country code (e.g., "dk", "se", "no")
            store_name: Store name (e.g., "fotex", "netto")
            date_period: Date period (e.g., "2025-w27" for week 27)

        Returns:
            Cloud storage path following enterprise pattern
        """
        if country_code and store_name and date_period:
            # New enterprise structure: dk/fotex/2025-w27/catalog.pdf
            safe_store_name = cls._sanitize_name(store_name)
            return f"{country_code}/{safe_store_name}/{date_period}/{filename}"
        else:
            # Legacy fallback for backward compatibility
            return f"{cls.CATALOGS}{filename}"

    @classmethod
    def image_path(cls, filename: str, country_code: str = None, store_name: str = None, date_period: str = None) -> str:
        """
        Generate image storage path with country-aware structure

        Args:
            filename: The image filename (e.g., "page_001.png")
            country_code: ISO country code (e.g., "dk", "se", "no")
            store_name: Store name (e.g., "fotex", "netto")
            date_period: Date period (e.g., "2025-w27" for week 27)

        Returns:
            Cloud storage path following enterprise pattern
        """
        if country_code and store_name and date_period:
            # New enterprise structure: dk/fotex/2025-w27/images/page_001.png
            safe_store_name = cls._sanitize_name(store_name)
            return f"{country_code}/{safe_store_name}/{date_period}/images/{filename}"
        else:
            # Legacy fallback for backward compatibility
            return f"{cls.IMAGES}{filename}"

    @classmethod
    def logo_path(cls, filename: str) -> str:
        """Generate logo storage path (logos are global, not country-specific)"""
        return f"{cls.LOGOS}{filename}"

    @classmethod
    def temp_path(cls, filename: str) -> str:
        """Generate temporary file storage path"""
        return f"{cls.TEMP}{filename}"

    @classmethod
    def _sanitize_name(cls, name: str) -> str:
        """
        Sanitize store/country names for safe cloud storage paths

        Args:
            name: Raw name to sanitize

        Returns:
            Safe name for cloud storage (lowercase, no special chars)
        """
        import re
        # Convert to lowercase and replace special chars with hyphens
        safe_name = re.sub(r'[^a-z0-9]', '-', name.lower())
        # Remove multiple consecutive hyphens
        safe_name = re.sub(r'-+', '-', safe_name)
        # Remove leading/trailing hyphens
        safe_name = safe_name.strip('-')
        return safe_name

    @classmethod
    def generate_date_period(cls, start_date, end_date) -> str:
        """
        Generate date period string for storage organization

        Args:
            start_date: datetime.date object for catalog start
            end_date: datetime.date object for catalog end

        Returns:
            Date period string (e.g., "2025-w27" for week-based, "2025-07" for month-based)
        """
        from datetime import date

        # Convert to date objects if needed
        if hasattr(start_date, 'date'):
            start_date = start_date.date()
        if hasattr(end_date, 'date'):
            end_date = end_date.date()

        # For retail catalogs, use week-based organization (matches retail cycles)
        year, week, _ = start_date.isocalendar()
        return f"{year}-w{week:02d}"

    @classmethod
    def detect_country_code(cls, store_name: str = None, scraper_config: dict = None) -> str:
        """
        Detect country code from store name or scraper configuration
        Currently focused on Denmark only - expansion logic prepared for future

        Args:
            store_name: Name of the store
            scraper_config: Scraper configuration dictionary

        Returns:
            ISO country code ("dk" for Denmark - current focus)
        """
        # For now, always return Denmark since that's our current market
        # Future expansion: Add logic for SE/NO when needed
        return "dk"


# Initialize storage on module import for convenience
storage = get_storage()

# Log storage configuration
logger.info(f"Storage system initialized: {StorageFactory.get_storage_info()}")
