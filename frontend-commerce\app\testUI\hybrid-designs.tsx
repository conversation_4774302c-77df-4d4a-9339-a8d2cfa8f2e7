"use client";

import { useState, useEffect } from 'react';
import Image from "next/image";
import { getCatalogs, BackendCatalog } from '@/lib/backend';

export default function HybridDesignsPage() {
  const [catalogs, setCatalogs] = useState<BackendCatalog[]>([]);
  const [question, setQuestion] = useState('');
  const [selectedCatalogs, setSelectedCatalogs] = useState<number[]>([]);
  const [showResults, setShowResults] = useState(false);

  useEffect(() => {
    const fetchCatalogs = async () => {
      try {
        const data = await getCatalogs();
        setCatalogs(data.slice(0, 6));
      } catch (err) {
        console.error('Failed to load catalogs:', err);
      }
    };
    fetchCatalogs();
  }, []);

  const toggleCatalog = (id: number) => {
    setSelectedCatalogs(prev => 
      prev.includes(id) ? prev.filter(c => c !== id) : [...prev, id]
    );
  };

  const handleSearch = () => {
    setShowResults(true);
  };

  // Mock search results for demonstration
  const mockResults = [
    {
      id: 1,
      name: "Arla Økologisk Mælk",
      price: "12,95 kr",
      originalPrice: "15,95 kr",
      store: "Netto",
      image: "/placeholder.png",
      savings: "3,00 kr",
      unit: "per liter"
    },
    {
      id: 2,
      name: "Thise Økologisk Mælk",
      price: "13,50 kr",
      originalPrice: "16,50 kr",
      store: "SuperBrugsen",
      image: "/placeholder.png",
      savings: "3,00 kr",
      unit: "per liter"
    },
    {
      id: 3,
      name: "Kærgården Smør",
      price: "18,95 kr",
      originalPrice: "22,95 kr",
      store: "Føtex",
      image: "/placeholder.png",
      savings: "4,00 kr",
      unit: "per 500g"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto space-y-16">
        
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">🎨 Hybrid Design Concepts</h1>
          <p className="text-lg text-gray-600">Combining Google-like simplicity with senior-friendly guidance</p>
        </div>

        {/* ===== HYBRID 1: CLEAN GUIDANCE ===== */}
        <div className="border-t-8 border-blue-500 pt-8">
          <h2 className="text-2xl font-bold text-blue-600 mb-6">1. 🎯 Clean Guidance</h2>
          <p className="text-gray-600 mb-8"><strong>Concept:</strong> Google's minimalism + gentle guidance without overwhelming</p>
          
          <div className="bg-white min-h-96 p-8 rounded-lg shadow-sm">
            <div className="max-w-2xl mx-auto">
              {/* Simple Logo */}
              <div className="w-24 h-24 relative mx-auto mb-6">
                <Image 
                  src="/logos/logo-samlet.png" 
                  alt="Tilbudsjægeren"
                  fill
                  className="object-contain"
                />
              </div>
              
              {/* Helpful but minimal guidance */}
              <div className="text-center mb-8">
                <h3 className="text-xl font-medium text-gray-800 mb-2">Find de bedste tilbud</h3>
                <p className="text-gray-600">Skriv hvad du søger, og vi finder de billigste priser</p>
              </div>
              
              {/* Clean search with subtle help */}
              <div className="relative mb-6">
                <input
                  type="text"
                  placeholder="Søg efter produkter... (f.eks. 'økologisk mælk')"
                  className="w-full px-6 py-4 text-lg border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                />
                <button 
                  onClick={handleSearch}
                  className="absolute right-2 top-2 px-6 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
                >
                  Søg
                </button>
              </div>
              
              {/* Gentle store selection */}
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-4">Vælg dine foretrukne butikker (valgfrit)</p>
                <div className="flex justify-center space-x-3">
                  {catalogs.slice(0, 4).map((catalog) => (
                    <div
                      key={catalog.id}
                      onClick={() => toggleCatalog(catalog.id)}
                      className={`w-14 h-14 rounded-lg overflow-hidden cursor-pointer transition-all border-2 ${
                        selectedCatalogs.includes(catalog.id) 
                          ? 'border-blue-500 scale-105 shadow-md' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <img
                        src={catalog.store_logo_url || '/placeholder.png'}
                        alt={catalog.title}
                        className="w-full h-full object-contain bg-white"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* ===== HYBRID 2: SMART MINIMALISM ===== */}
        <div className="border-t-8 border-green-500 pt-8">
          <h2 className="text-2xl font-bold text-green-600 mb-6">2. 🧠 Smart Minimalism</h2>
          <p className="text-gray-600 mb-8"><strong>Concept:</strong> Clean interface that reveals helpful hints when needed</p>
          
          <div className="bg-white min-h-96 p-8 rounded-lg shadow-sm">
            <div className="max-w-2xl mx-auto">
              {/* Logo with friendly greeting */}
              <div className="text-center mb-8">
                <div className="w-20 h-20 relative mx-auto mb-4">
                  <Image 
                    src="/logos/logo-samlet.png" 
                    alt="Tilbudsjægeren"
                    fill
                    className="object-contain"
                  />
                </div>
                <h3 className="text-2xl font-light text-gray-800">Tilbudsjægeren</h3>
              </div>
              
              {/* Progressive disclosure search */}
              <div className="space-y-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Hvad skal du bruge?"
                    className="w-full px-6 py-4 text-lg border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    value={question}
                    onChange={(e) => setQuestion(e.target.value)}
                    onFocus={() => {/* Could show examples */}}
                  />
                  <button 
                    onClick={handleSearch}
                    className="absolute right-2 top-2 px-6 py-2 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors"
                  >
                    Find
                  </button>
                </div>
                
                {/* Smart suggestions that appear on focus */}
                {question.length === 0 && (
                  <div className="text-center">
                    <p className="text-sm text-gray-500 mb-3">Populære søgninger:</p>
                    <div className="flex flex-wrap justify-center gap-2">
                      {['mælk', 'brød', 'kaffe', 'æg'].map((suggestion) => (
                        <button
                          key={suggestion}
                          onClick={() => setQuestion(suggestion)}
                          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              {/* Expandable store selection */}
              <div className="mt-8 text-center">
                <details className="group">
                  <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800 transition-colors">
                    ⚙️ Vælg specifikke butikker (valgfrit)
                  </summary>
                  <div className="mt-4 flex justify-center space-x-3">
                    {catalogs.slice(0, 6).map((catalog) => (
                      <div
                        key={catalog.id}
                        onClick={() => toggleCatalog(catalog.id)}
                        className={`w-12 h-12 rounded-lg overflow-hidden cursor-pointer transition-all border ${
                          selectedCatalogs.includes(catalog.id) 
                            ? 'border-green-500 scale-110 shadow-md' 
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <img
                          src={catalog.store_logo_url || '/placeholder.png'}
                          alt={catalog.title}
                          className="w-full h-full object-contain bg-white"
                        />
                      </div>
                    ))}
                  </div>
                </details>
              </div>
            </div>
          </div>
        </div>

        {/* ===== HYBRID 3: GUIDED SIMPLICITY ===== */}
        <div className="border-t-8 border-purple-500 pt-8">
          <h2 className="text-2xl font-bold text-purple-600 mb-6">3. 🎯 Guided Simplicity</h2>
          <p className="text-gray-600 mb-8"><strong>Concept:</strong> Senior-friendly guidance in a clean, uncluttered layout</p>

          <div className="bg-gradient-to-b from-white to-purple-50 min-h-96 p-8 rounded-lg shadow-sm">
            <div className="max-w-3xl mx-auto">
              {/* Friendly header */}
              <div className="text-center mb-8">
                <div className="w-16 h-16 relative mx-auto mb-3">
                  <Image
                    src="/logos/logo-samlet.png"
                    alt="Tilbudsjægeren"
                    fill
                    className="object-contain"
                  />
                </div>
                <h3 className="text-xl font-medium text-purple-800 mb-2">Velkommen! 👋</h3>
                <p className="text-purple-700">Vi hjælper dig med at finde de bedste tilbud</p>
              </div>

              {/* Step-by-step but clean */}
              <div className="space-y-6">
                {/* Step 1 - Clean */}
                <div className="bg-white rounded-lg p-6 border-l-4 border-purple-500 shadow-sm">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold text-sm">1</div>
                    <div className="flex-1">
                      <h4 className="font-medium text-purple-800 mb-2">Skriv hvad du søger</h4>
                      <div className="relative">
                        <input
                          type="text"
                          placeholder="F.eks. 'billig kaffe' eller 'økologiske æg'"
                          className="w-full px-4 py-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:border-purple-500"
                          value={question}
                          onChange={(e) => setQuestion(e.target.value)}
                        />
                        <button
                          onClick={handleSearch}
                          className="absolute right-2 top-2 px-4 py-1 bg-purple-500 text-white text-sm rounded hover:bg-purple-600 transition-colors"
                        >
                          Søg
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 2 - Optional stores */}
                <div className="bg-white rounded-lg p-6 border-l-4 border-blue-500 shadow-sm">
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold text-sm">2</div>
                    <div className="flex-1">
                      <h4 className="font-medium text-blue-800 mb-2">Vælg butikker (valgfrit)</h4>
                      <p className="text-blue-700 text-sm mb-4">Klik på de butikker du handler i</p>
                      <div className="grid grid-cols-3 gap-3">
                        {catalogs.slice(0, 6).map((catalog) => (
                          <div
                            key={catalog.id}
                            onClick={() => toggleCatalog(catalog.id)}
                            className={`p-3 rounded-lg border-2 cursor-pointer transition-all text-center ${
                              selectedCatalogs.includes(catalog.id)
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-300 hover:border-blue-300 bg-white'
                            }`}
                          >
                            <img
                              src={catalog.store_logo_url || '/placeholder.png'}
                              alt={catalog.title}
                              className="w-10 h-10 object-contain mx-auto mb-1"
                            />
                            <div className="text-xs font-medium text-gray-700">{catalog.title.split(' ')[0]}</div>
                            {selectedCatalogs.includes(catalog.id) && (
                              <div className="text-blue-600 text-xs mt-1">✓</div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* ===== SEARCH RESULTS EXAMPLES ===== */}
        {showResults && (
          <div className="border-t-8 border-orange-500 pt-8">
            <h2 className="text-2xl font-bold text-orange-600 mb-6">📊 Search Results Examples</h2>
            <p className="text-gray-600 mb-8">How search results would look in each design style</p>

            {/* Clean Results (Style 1) */}
            <div className="mb-12">
              <h3 className="text-lg font-semibold text-blue-600 mb-4">Clean Results Style</h3>
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="text-sm text-gray-600 mb-4">Fandt 3 produkter for "økologisk mælk"</div>
                <div className="space-y-3">
                  {mockResults.map((result) => (
                    <div key={result.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                      <img src={result.image} alt={result.name} className="w-16 h-16 object-contain bg-gray-50 rounded" />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{result.name}</h4>
                        <p className="text-sm text-gray-600">{result.store}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-green-600">{result.price}</div>
                        <div className="text-sm text-gray-500 line-through">{result.originalPrice}</div>
                        <div className="text-xs text-green-600">Spar {result.savings}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Guided Results (Style 3) */}
            <div className="mb-12">
              <h3 className="text-lg font-semibold text-purple-600 mb-4">Guided Results Style</h3>
              <div className="bg-purple-50 rounded-lg p-6">
                <div className="bg-white rounded-lg p-4 mb-4 border-l-4 border-purple-500">
                  <h4 className="font-medium text-purple-800 mb-2">🎉 Vi fandt 3 gode tilbud til dig!</h4>
                  <p className="text-purple-700 text-sm">Her er de billigste priser på økologisk mælk:</p>
                </div>

                <div className="space-y-4">
                  {mockResults.map((result, index) => (
                    <div key={result.id} className="bg-white rounded-lg p-4 shadow-sm border-2 border-gray-200">
                      <div className="flex items-center space-x-4">
                        <div className="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold text-sm">
                          {index + 1}
                        </div>
                        <img src={result.image} alt={result.name} className="w-16 h-16 object-contain bg-gray-50 rounded" />
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{result.name}</h4>
                          <p className="text-sm text-gray-600">📍 {result.store}</p>
                          <p className="text-xs text-green-600">💰 Du sparer {result.savings} ({result.unit})</p>
                        </div>
                        <div className="text-right">
                          <div className="text-xl font-bold text-green-600">{result.price}</div>
                          <div className="text-sm text-gray-500 line-through">{result.originalPrice}</div>
                          <button className="mt-2 px-3 py-1 bg-purple-500 text-white text-xs rounded hover:bg-purple-600 transition-colors">
                            Se tilbud
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 bg-green-100 border-2 border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">💡</span>
                    <div>
                      <h4 className="font-medium text-green-800">Godt tip!</h4>
                      <p className="text-green-700 text-sm">Arla Økologisk Mælk har den bedste pris lige nu. Klik på "Se tilbud" for at se hvor du kan købe den.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
