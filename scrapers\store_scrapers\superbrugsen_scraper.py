# File: scrapers/store_scrapers/superbrugsen_scraper.py

import asyncio
import re
from pathlib import Path
from typing import List, Dict, Any, Optional


from ..scraper_core import BaseScraper, PlaywrightTimeoutError, PlaywrightError, Page


class SuperbrugsenScraper(BaseScraper):
    """
    Scraper for SuperBrugsen catalogs - streamlined to match working Brugsen flow.
    """

    def __init__(self, config: dict):
        super().__init__(config)
        self.logger.info(f"SuperbrugsenScraper initialized for store: {self.store_name}")

    async def scrape_catalogs(self) -> List[Dict[str, Any]]:
        if not self.page or not self.context or not self.catalog_list_url:
            self.logger.error("Page, context, or catalog_list_url not available for SuperbrugsenScraper.")
            return []

        # Set timeouts
        try:
            default_nav_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            default_el_timeout = self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 30000)
            if self.page:
                self.page.set_default_navigation_timeout(default_nav_timeout)
                self.page.set_default_timeout(default_el_timeout)
                self.logger.info(f"{self.store_name}: Page timeouts set.")
        except Exception as e_timeout_set:
            self.logger.error(f"SuperbrugsenScraper: Error setting default timeouts: {e_timeout_set}")
            return []

        # 1. Navigate to the /avis/ page
        self.logger.info(f"SuperBrugsen: Navigating to /avis/ page: {self.catalog_list_url}")
        nav_success = await self._navigate_to_url(self.page, self.catalog_list_url)
        if not nav_success:
            self.logger.error("SuperBrugsen: Navigation failed")
            return []
        
        self.logger.info("SuperBrugsen: Navigation successful, checking page state...")
        if self.page.is_closed():
            self.logger.error("SuperBrugsen: Page was closed after navigation")
            return []

        # 2. Handle Cookies
        cookie_selectors = self.config.get('selectors', {}).get('cookie_accept_selectors', [])
        if cookie_selectors:
            await self._handle_cookies(self.page, cookie_selectors)

        # 3. Wait for page to settle after cookie handling
        self.logger.info("SuperBrugsen: Waiting for page to settle after cookie handling...")
        await asyncio.sleep(3)  # Give time for cookie popup to fully disappear

        # Extract date info from the main page (same as other COOP stores)
        raw_date_info = "SuperBrugsen_Unknown_Date"
        display_title = f"{self.store_name} Catalog" # Default title

        # Try to extract date from .date-periode element (same as Brugsen/365discount)
        try:
            date_el = self.page.locator(".date-periode").first
            await date_el.wait_for(state="visible", timeout=10000)
            text_content = await date_el.text_content()
            if text_content:
                raw_date_info = text_content.strip()
                self.logger.info(f"SuperBrugsen: Date from viewer page: '{raw_date_info}'")
                if "Unknown_Date" not in raw_date_info:
                    display_title = f"{self.store_name} Catalog ({raw_date_info.split('-')[0].strip()})"
        except Exception as e:
            self.logger.warning(f"SuperBrugsen: Error extracting date from viewer page: {e}")
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "superbrugsen_date_extract_fail.png"))

        # 5. Click the ShopGun PDF trigger (same as Brugsen)
        shopgun_pdf_trigger_selector = self.config.get('selectors', {}).get('shopgun_viewer_pdf_trigger_selector')
        final_download_button_selector = self.config.get('selectors', {}).get('final_pdf_download_button_selector')

        if not shopgun_pdf_trigger_selector and not final_download_button_selector:
            self.logger.error("SuperBrugsen: Neither 'shopgun_viewer_pdf_trigger_selector' nor 'final_pdf_download_button_selector' are configured.")
            return []

        if shopgun_pdf_trigger_selector:
            try:
                self.logger.info("SuperBrugsen: Taking screenshot before PDF trigger interaction")
                await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "superbrugsen_before_trigger.png"))
                
                self.logger.info(f"SuperBrugsen Viewer: Looking for PDF trigger element: '{shopgun_pdf_trigger_selector}'")
                pdf_trigger = self.page.locator(shopgun_pdf_trigger_selector).first
                
                trigger_count = await pdf_trigger.count()
                self.logger.info(f"SuperBrugsen: Found {trigger_count} elements matching trigger selector")
                
                if trigger_count == 0:
                    self.logger.warning(f"SuperBrugsen: No elements found for trigger selector '{shopgun_pdf_trigger_selector}'")
                
                self.logger.info("SuperBrugsen Viewer: Waiting for PDF trigger element to be visible")
                await pdf_trigger.wait_for(state="visible", timeout=20000)
                await pdf_trigger.click()
                
                # Wait for navigation after trigger click
                try:
                    await self.page.wait_for_load_state('domcontentloaded', timeout=10000)
                    self.logger.info(f"SuperBrugsen: Successfully navigated after PDF trigger click. New URL: {self.page.url}")
                except Exception:
                    self.logger.info("SuperBrugsen: No navigation detected after PDF trigger click, continuing on same page.")
                
                # Additional wait time after ShopGun PDF trigger click
                wait_after_trigger = self.config.get('behavior_flags', {}).get('wait_after_viewer_pdf_trigger_click_ms', 2000)
                self.logger.info(f"SuperBrugsen: Waiting additional {wait_after_trigger}ms after ShopGun trigger click")
                await asyncio.sleep(wait_after_trigger/1000)
                
                self.logger.info(f"SuperBrugsen: Clicked ShopGun PDF trigger. New URL for download: {self.page.url}")

                # NOW extract date from the actual catalog page (after cookies are gone and we're on catalog page)
                try:
                    self.logger.info("SuperBrugsen: Extracting date from catalog page...")
                    date_element = self.page.locator("text=/\\d{1,2}\\.\\d{1,2}\\.\\d{4}/").first
                    if await date_element.count() > 0:
                        extracted_date = await date_element.text_content()
                        if extracted_date and "Cookie" not in extracted_date:  # Make sure it's not cookie text
                            raw_date_info = extracted_date  # Update the outer scope variable
                            display_title = f"{self.store_name} Catalog ({extracted_date})"
                            self.logger.info(f"SuperBrugsen: Extracted date from catalog page: {raw_date_info}")
                        else:
                            self.logger.warning(f"SuperBrugsen: Found date element but it contains cookie text, ignoring: {extracted_date}")
                except Exception as e:
                    self.logger.warning(f"SuperBrugsen: Error extracting date from catalog page: {e}")

            except Exception as e:
                self.logger.error(f"SuperBrugsen Viewer: Error clicking ShopGun PDF trigger '{shopgun_pdf_trigger_selector}': {e}", exc_info=True)
                await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "superbrugsen_shopgun_trigger_fail.png"))
                # Continue, as the final download button might be on the current page if trigger failed or wasn't needed
        
        # 6. Locate and click the final download button (same as Brugsen)
        if not final_download_button_selector:
            self.logger.error("SuperBrugsen: 'final_pdf_download_button_selector' not configured.")
            return []

        download_url: Optional[str] = None

        try:
            # The PDF viewer is not in an iframe. Locate the download button directly on the page.
            self.logger.info(f"SuperBrugsen: Locating final PDF download button directly on page using selector: '{final_download_button_selector}'")
            download_button = self.page.locator(final_download_button_selector).first
            await download_button.wait_for(state="visible", timeout=20000)

            navigation_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            click_timeout = self.config.get('behavior_flags', {}).get('element_click_timeout_ms', 10000)

            self.logger.info("SuperBrugsen: Clicking download button and capturing resulting PDF URL...")

            # Handle download event (same as Brugsen and 365discount)
            self.logger.info("SuperBrugsen: Clicking download button and waiting for download...")
            async with self.page.expect_download(timeout=navigation_timeout) as download_info:
                await download_button.click(timeout=click_timeout)

            download = await download_info.value
            download_url = download.url
            self.logger.info(f"SuperBrugsen: Successfully captured PDF download URL: {download_url}")

            # Cancel the download since we only need the URL
            await download.cancel()

        except PlaywrightTimeoutError:
            self.logger.error("SuperBrugsen: Timeout error during PDF navigation or iframe interaction")
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "superbrugsen_final_download_fail.png"))
            return []
        except Exception as e:
            self.logger.error(f"SuperBrugsen: Failed to navigate to PDF or capture its URL. Error: {e}", exc_info=True)
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "superbrugsen_pdf_capture_fail.png"))
            return []

        if not download_url:
            self.logger.error("SuperBrugsen: Failed to capture PDF URL.")
            return []

        # Verify we got to a PDF URL (could be intermediate or final)
        if 'avis-pdf' in download_url:
            self.logger.info(f"SuperBrugsen: Got intermediate PDF URL: {download_url}")
        elif 'amazonaws.com' in download_url or 'sgn-prd-assets' in download_url:
            self.logger.info(f"SuperBrugsen: Got final AWS PDF URL: {download_url}")
        else:
            self.logger.warning(f"SuperBrugsen: Final URL doesn't look like a PDF URL: {download_url}")
            # But continue anyway - might still be valid

        # 7. Save PDF from URL (same as Brugsen)
        if not download_url:
            self.logger.error("SuperBrugsen: download_url was not captured, cannot save PDF.")
            return []

        file_title_part = self._sanitize_filename(raw_date_info.split('-')[0].strip() if raw_date_info and "Unknown_Date" not in raw_date_info else display_title)
        suggested_filename = f"{file_title_part}.pdf"

        final_pdf_filename = self._sanitize_filename(suggested_filename)
        if not final_pdf_filename.lower().endswith(".pdf"): final_pdf_filename += ".pdf"

        download_dir = self._ensure_download_dir()
        local_pdf_path = download_dir / final_pdf_filename

        try:
            pdf_content = await self._download_file_content(download_url)
            if not pdf_content:
                self.logger.error(f"SuperBrugsen: Failed to download PDF content from {download_url}.")
                return []

            with open(local_pdf_path, 'wb') as f:
                f.write(pdf_content)
            self.logger.info(f"SuperBrugsen: Successfully saved PDF to: {local_pdf_path}")

        except Exception as e:
            self.logger.error(f"SuperBrugsen: Error downloading or saving PDF: {e}", exc_info=True)
            return []

        # 8. Return catalog metadata
        catalog_data = {
            "store_name": self.store_name,
            "title": display_title,
            "raw_date_info": raw_date_info,
            "pdf_url": download_url,
            "local_path": str(local_pdf_path)
        }

        self.logger.info(f"SuperBrugsen: Successfully scraped catalog: {catalog_data}")
        return [catalog_data]
