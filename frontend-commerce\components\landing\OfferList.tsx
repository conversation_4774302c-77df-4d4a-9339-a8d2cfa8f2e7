import React, { useState } from 'react';
import { BackendProduct, BackendCatalog } from '@/lib/backend';
import { buildThumbnailUrl, getStoreLogoUrl } from '@/lib/ui-helpers';
import ImageModal from '@/components/ui/ImageModal';

interface OfferListProps {
  products?: BackendProduct[];
  offers?: BackendProduct[];
  catalogs?: Pick<BackendCatalog, 'id' | 'store_name'>[];
  loading?: boolean;
}

const OfferList: React.FC<OfferListProps> = ({ products, offers, catalogs = [], loading }) => {
  // Use either products or offers (for compatibility with both naming conventions)
  const items = products || offers || [];
  const [selectedImage, setSelectedImage] = useState<{
    src: string;
    alt: string;
    title: string;
    subtitle: string;
  } | null>(null);

  const getCatalogInfo = (catalogId: number) => {
    return catalogs.find(c => c.id === catalogId);
  };

  const handleImageClick = (item: BackendProduct, imageSrc: string) => {
    const catalogInfo = getCatalogInfo(item.catalog_id);
    setSelectedImage({
      src: imageSrc,
      alt: item.name,
      title: item.name,
      subtitle: catalogInfo?.store_name || 'Unknown Store'
    });
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {Array.from({ length: 10 }).map((_, index) => (
          <div key={index} className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/60 rounded-lg overflow-hidden shadow-lg animate-pulse">
            <div className="h-48 bg-gray-700/50"></div>
            <div className="p-4">
              <div className="h-6 bg-gray-700/50 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-700/50 rounded w-1/2 mb-4"></div>
              <div className="h-6 bg-gray-700/50 rounded w-1/3"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!items || items.length === 0) {
    return (
      <div className="text-center py-12 text-gray-400">
        <p>Ingen tilbud fundet for din forespørgsel.</p>
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {items.map((item) => {
        const catalogInfo = getCatalogInfo(item.catalog_id);
        const storeName = catalogInfo?.store_name;
        const storeLogo = getStoreLogoUrl(storeName);
        const imageSrc = buildThumbnailUrl(item.image_path) || storeLogo || '/placeholder.png';

        return (
          <div key={item.id} className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-200 dark:border-gray-700 group hover:scale-[1.02]">
            <div
              className="relative h-48 bg-gray-50 dark:bg-gray-900 flex items-center justify-center overflow-hidden cursor-pointer"
              onClick={() => handleImageClick(item, imageSrc)}
              title="Click to view full size"
            >
              <img
                src={imageSrc}
                alt={item.name}
                className="h-full w-auto max-w-full p-2 transition-transform duration-300 group-hover:scale-110 object-contain"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder.png';
                }}
              />
              {/* Overlay hint */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black/50 backdrop-blur-sm rounded-full p-2">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                </div>
              </div>
            </div>
            <div className="p-4 flex flex-col justify-between min-h-[10rem]">
              <h3 className="text-base font-semibold text-gray-100 truncate" title={item.name}>
                {item.name}
              </h3>
              <div>
                <div className="flex justify-between items-baseline mt-2">
                  <p className="text-xl font-bold text-sky-400">
                    {item.price.toFixed(2).replace('.', ',')} kr.
                  </p>
                  {item.original_price && (
                    <p className="text-sm text-gray-400 line-through">
                      {item.original_price.toFixed(2).replace('.', ',')} kr.
                    </p>
                  )}
                </div>
                <div className="flex items-center mt-3 pt-3 border-t border-gray-700/60">
                  {storeLogo && <img src={storeLogo} alt={storeName || ''} className="h-5 w-auto mr-2 object-contain" />}
                  <p className="text-xs text-gray-400">{storeName || 'Ukendt butik'}</p>
                </div>
              </div>
            </div>
          </div>
        );
        })}
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          isOpen={!!selectedImage}
          onClose={() => setSelectedImage(null)}
          imageSrc={selectedImage.src}
          imageAlt={selectedImage.alt}
          title={selectedImage.title}
          subtitle={selectedImage.subtitle}
        />
      )}
    </>
  );
};

export default OfferList;
