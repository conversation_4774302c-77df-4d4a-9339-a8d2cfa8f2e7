# File: scrapers/store_scrapers/spar_scraper.py

import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional

from ..scraper_core import BaseScraper, PlaywrightTimeoutError, PlaywrightError, Page


class SparScraper(BaseScraper):
    """
    Scraper for Spar catalogs (Dagrofa group).
    - Navigates directly to the 'ugensavis.spar.dk' catalog viewer page.
    - Extracts date info from the page title (e.g., "SPAR uge WWYY").
    - Clicks a direct PDF download button (often #modDownloadPdfBtn).
    """

    def __init__(self, config: dict):
        super().__init__(config)
        self.logger.info(f"SparScraper initialized for store: {self.store_name}")

    async def scrape_catalogs(self) -> List[Dict[str, Any]]:
        if not self.page or not self.context or not self.catalog_list_url:
            self.logger.error("Page, context, or catalog_list_url not available for SparScraper.")
            return []

        # Set timeouts
        try:
            default_nav_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            default_el_timeout = self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 30000)
            if self.page:
                self.page.set_default_navigation_timeout(default_nav_timeout)
                self.page.set_default_timeout(default_el_timeout)
                self.logger.info(f"SparScraper: Page timeouts set.")
        except Exception as e_timeout_set:
            self.logger.error(f"SparScraper: Error setting default timeouts: {e_timeout_set}")
            return []

        # 1. Navigate to Spar catalog viewer page
        self.logger.debug(f"Navigating to Spar viewer page: {self.catalog_list_url}.")
        nav_success = await self._navigate_to_url(self.page, self.catalog_list_url)
        if not nav_success:
            return []

        
        # 2. Handle Cookies (if any on this direct viewer page)
        cookie_selectors = self.config.get('selectors', {}).get('cookie_accept_selectors', [])
        if cookie_selectors: # Check if list is not empty
            await self._handle_cookies(self.page, cookie_selectors, 
                                       timeout_ms=self.config.get('behavior_flags', {}).get('cookie_consent_timeout_ms', 15000))
        else:
            self.logger.info("No cookie selectors configured for Spar viewer page.")


        # 3. Extract Date Info from page title
        raw_date_info = "Spar_Unknown_Date" # Default
        display_title = f"{self.store_name} Catalog" # Default title
        
        # Check behavior_flags if date should be extracted from title
        if self.config.get('behavior_flags', {}).get('extract_catalog_date_from_title', False):
            try:
                page_title_text = await self.page.title()
                if page_title_text:
                    raw_date_info = page_title_text.strip()
                    display_title = raw_date_info # Use the full title e.g., "SPAR uge 2225"
                    self.logger.info(f"Spar: Extracted raw_date_info from page title: '{raw_date_info}'")
                else:
                    self.logger.warning("Spar: Page title was empty, cannot extract date info from it.")
            except Exception as e:
                self.logger.error(f"Spar: Error extracting page title for date info: {e}")
        else:
            self.logger.warning("Spar: Config not set to extract date from title. Date info might be missing.")
        

        # 4. Locate and Click PDF Download Button
        pdf_download_selector = self.config.get('selectors', {}).get('pdf_download_button_selector')
        if not pdf_download_selector:
            self.logger.error("Spar: 'pdf_download_button_selector' not configured.")
            return []

        download: Optional[Any] = None
        try:
            self.logger.info(f"Spar: Attempting to click PDF download button: '{pdf_download_selector}'")
            download_button_locator = self.page.locator(pdf_download_selector).first
            
            await download_button_locator.wait_for(state="visible", timeout=self.config.get('behavior_flags',{}).get('element_wait_timeout_ms', 20000))
            
            async with self.page.expect_download(timeout=self.config.get('behavior_flags',{}).get('download_event_timeout_ms', 60000)) as download_info:
                await download_button_locator.click(timeout=5000) # Standard click timeout
            download = await download_info.value
            self.logger.info("Spar: PDF Download event triggered.")

        except PlaywrightTimeoutError:
            self.logger.error(f"Spar: Timeout finding or clicking PDF download button '{pdf_download_selector}'. URL: {self.page.url}", exc_info=True)
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "spar_download_timeout.png"))
            return []
        except Exception as e:
            self.logger.error(f"Spar: Error clicking PDF download button '{pdf_download_selector}' or during download: {e}. URL: {self.page.url}", exc_info=True)
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "spar_download_exception.png"))
            return []

        if not download:
            self.logger.error("Spar: PDF download object not obtained.")
            return []
            
        # 5. Save the downloaded PDF
        download_url = download.url
        file_title_part = self._sanitize_filename(display_title) # display_title is "SPAR uge WWYY"
        suggested_filename = download.suggested_filename or f"{file_title_part}.pdf"
        
        final_pdf_filename = self._sanitize_filename(suggested_filename)
        if not final_pdf_filename.lower().endswith(".pdf"):
            final_pdf_filename += ".pdf"

        download_dir = self._ensure_download_dir()
        local_pdf_path = download_dir / final_pdf_filename

        try:
            await download.save_as(local_pdf_path)
            self.logger.info(f"Spar: Successfully saved PDF to: {local_pdf_path}")
            
            self.collected_catalogs.append({
                "store_name": self.store_name,
                "title": display_title, 
                "raw_date_info": raw_date_info, # This will be "SPAR uge WWYY"
                "pdf_url": download_url, 
                "local_path": str(local_pdf_path)
            })
        except Exception as e:
            self.logger.error(f"Spar: Error saving downloaded PDF: {e}", exc_info=True)

        return self.collected_catalogs