'use client';

import { useState, useRef, useEffect } from 'react';

const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';

interface ParsedProduct {
  name: string;
  description: string | null;
  price: number | null;
  original_price: number | null;
  unit: string | null;
  category: string | null;
  brand: string | null;
}

interface TestResult {
  test_metadata: {
    timestamp: string;
    filename?: string;
    page_id?: number;
    catalog_name?: string;
    store_name?: string;
    page_number?: number;
    tested_by: string;
    processing_time_seconds: number;
    parsing_success: boolean;
    error_message: string | null;
  };
  current_settings: {
    model: string;
    temperature: number;
    max_tokens: number;
    max_retries: number;
  };
  results: {
    products_found?: number;
    new_products_found?: number;
    existing_products_count?: number;
    products?: ParsedProduct[];
    new_products?: ParsedProduct[];
    existing_products?: any[];
  };
}

interface CatalogPage {
  page_id: number;
  catalog_id: number;
  catalog_name: string;
  store_name: string;
  page_number: number;
  cloud_path: string;
  processed: boolean;
  product_count: number;
}

export default function ParserTestPage() {
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [catalogPages, setCatalogPages] = useState<CatalogPage[]>([]);
  const [selectedPageId, setSelectedPageId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<'upload' | 'existing'>('upload');
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const loadCatalogPages = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/admin/catalog-pages?limit=50`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCatalogPages(data.pages);
      }
    } catch (error) {
      console.error('Failed to load catalog pages:', error);
    }
  };

  // Load catalog pages on component mount
  useEffect(() => {
    loadCatalogPages();
  }, []);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    setError(null);
    setTestResult(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${API_BASE}/api/admin/test-parser`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        setTestResult(result);
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to test parser');
      }
    } catch (error) {
      setError('Network error occurred');
      console.error('Parser test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestExistingPage = async () => {
    if (!selectedPageId) return;

    setIsLoading(true);
    setError(null);
    setTestResult(null);

    try {
      const response = await fetch(`${API_BASE}/api/admin/test-parser-existing-page/${selectedPageId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        setTestResult(result);
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to test parser');
      }
    } catch (error) {
      setError('Network error occurred');
      console.error('Parser test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">🧪 Parser Testing Playground</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Test the parser with different images and current settings. Perfect for experimenting with models and prompts.
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('upload')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'upload'
                    ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400'
                }`}
              >
                📤 Upload Image
              </button>
              <button
                onClick={() => setActiveTab('existing')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'existing'
                    ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400'
                }`}
              >
                📄 Test Existing Page
              </button>
            </nav>
          </div>
        </div>

        {/* Upload Tab */}
        {activeTab === 'upload' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Upload Test Image</h2>
            <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
              />
              <div className="space-y-4">
                <div className="text-4xl">📸</div>
                <div>
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isLoading}
                    className="bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50"
                  >
                    {isLoading ? 'Testing...' : 'Choose Image to Test'}
                  </button>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Upload a catalog page image to test with current parser settings
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Existing Page Tab */}
        {activeTab === 'existing' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Test Existing Catalog Page</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Select Catalog Page
                </label>
                <select
                  value={selectedPageId || ''}
                  onChange={(e) => setSelectedPageId(Number(e.target.value) || null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="">Choose a catalog page...</option>
                  {catalogPages.map((page) => (
                    <option key={page.page_id} value={page.page_id}>
                      {page.store_name} - {page.catalog_name} - Page {page.page_number} ({page.product_count} products)
                    </option>
                  ))}
                </select>
              </div>
              <button
                onClick={handleTestExistingPage}
                disabled={!selectedPageId || isLoading}
                className="bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
              >
                {isLoading ? 'Testing...' : 'Test Selected Page'}
              </button>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <div className="flex">
              <div className="text-red-400">❌</div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <div className="ml-3 text-blue-800 dark:text-blue-200">
                Testing parser with current settings...
              </div>
            </div>
          </div>
        )}

        {/* Results Display */}
        {testResult && (
          <div className="space-y-6">
            {/* Test Metadata */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">📊 Test Results</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                  <div className="text-sm text-gray-500 dark:text-gray-400">Status</div>
                  <div className={`font-semibold ${testResult.test_metadata.parsing_success ? 'text-green-600' : 'text-red-600'}`}>
                    {testResult.test_metadata.parsing_success ? '✅ Success' : '❌ Failed'}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                  <div className="text-sm text-gray-500 dark:text-gray-400">Processing Time</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {testResult.test_metadata.processing_time_seconds}s
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                  <div className="text-sm text-gray-500 dark:text-gray-400">Products Found</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {testResult.results.products_found || testResult.results.new_products_found || 0}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                  <div className="text-sm text-gray-500 dark:text-gray-400">Model Used</div>
                  <div className="font-semibold text-gray-900 dark:text-white text-xs">
                    {testResult.current_settings.model.replace('models/', '')}
                  </div>
                </div>
              </div>

              {testResult.test_metadata.error_message && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded p-3 mb-4">
                  <div className="text-sm font-medium text-red-800 dark:text-red-200">Error Message:</div>
                  <div className="text-sm text-red-700 dark:text-red-300 mt-1 font-mono">
                    {testResult.test_metadata.error_message}
                  </div>
                </div>
              )}
            </div>

            {/* Current Settings */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">⚙️ Current Settings Used</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Model</div>
                  <div className="font-mono text-sm text-gray-900 dark:text-white">
                    {testResult.current_settings.model}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Temperature</div>
                  <div className="font-mono text-sm text-gray-900 dark:text-white">
                    {testResult.current_settings.temperature}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Max Tokens</div>
                  <div className="font-mono text-sm text-gray-900 dark:text-white">
                    {testResult.current_settings.max_tokens}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Max Retries</div>
                  <div className="font-mono text-sm text-gray-900 dark:text-white">
                    {testResult.current_settings.max_retries}
                  </div>
                </div>
              </div>
            </div>

            {/* Products Results */}
            {(testResult.results.products || testResult.results.new_products) && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                  🛍️ Parsed Products ({(testResult.results.products || testResult.results.new_products || []).length})
                </h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Price</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Unit</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Category</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Brand</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {(testResult.results.products || testResult.results.new_products || []).map((product, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            <div className="font-medium">{product.name}</div>
                            {product.description && (
                              <div className="text-gray-500 dark:text-gray-400 text-xs mt-1">{product.description}</div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {product.price ? `${product.price} kr` : '-'}
                            {product.original_price && (
                              <div className="text-gray-500 dark:text-gray-400 text-xs line-through">
                                {product.original_price} kr
                              </div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {product.unit || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {product.category || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {product.brand || '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Raw JSON Output */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">🔧 Raw JSON Output</h3>
              <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded text-xs overflow-x-auto text-gray-800 dark:text-gray-200">
                {JSON.stringify(testResult, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
