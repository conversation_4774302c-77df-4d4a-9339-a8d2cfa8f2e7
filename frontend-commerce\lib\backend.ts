// Lightweight fetch helpers that talk to the FastAPI backend
// Replace the Shopify data layer.

export interface BackendStore {
  id: number;
  name: string;
  logo_url?: string;
}

export interface BackendProduct {
  id: number;
  name: string;
  description?: string;
  price: number;
  original_price?: number;
  unit?: string;
  category?: string;
  catalog_id: number;
  image_path?: string;
  // Enhanced fields from database
  quantity?: number;
  unit_type?: string;
  price_per_base_unit?: number;
  base_unit_type?: string;
  brand?: string;
}

const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';

export async function getProducts(query?: string): Promise<BackendProduct[]> {
  const url = new URL(`${API_BASE}/products`);
  if (query) url.searchParams.set('q', query);
  const res = await fetch(url.toString(), { next: { revalidate: 0 } });
  if (!res.ok) {
    console.error('Failed to fetch products', await res.text());
    return [];
  }
  return res.json();
}

export interface BackendCatalog {
  id: number;
  title: string;
  store_id: number;
  store_name: string;
  store_logo_url?: string;  // Store logo URL from cloud storage
  valid_from?: string;
  valid_to?: string;
  pages?: number;
  products?: number;
  first_page_image_url?: string;  // Changed from first_page_image_path to match backend
  pages_image_paths?: string[];
  pdf_path?: string;  // PDF path for modal viewer
}

export async function getCatalogs(): Promise<BackendCatalog[]> {
  const headers: Record<string, string> = {};
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('token');
    if (token) headers['Authorization'] = `Bearer ${token}`;
  }
  const res = await fetch(`${API_BASE}/catalogs`, { headers, next: { revalidate: 0 } });
  if (!res.ok) return [];
  return res.json();
}

export async function getCatalogDetails(catalogId: number): Promise<BackendCatalog> {
  const headers: Record<string, string> = {};
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('token');
    if (token) headers['Authorization'] = `Bearer ${token}`;
  }
  const res = await fetch(`${API_BASE}/catalog/${catalogId}`, { headers, next: { revalidate: 0 } });
  if (!res.ok) {
    throw new Error('Failed to fetch catalog details');
  }
  return res.json();
}

export interface QueryResponse {
  response: string;
  products: BackendProduct[];
  query_history?: string[];
}

export async function queryOffers(question: string, catalogIds?: number[]): Promise<QueryResponse> {
  const token = localStorage.getItem('token');
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  const body = JSON.stringify({
    query: question,
    selected_catalog_ids: catalogIds,
  });

  console.log('Sending POST request to /ask with body:', body);
  const res = await fetch(`${API_BASE}/ask`, {
    method: 'POST',
    headers: headers,
    body: body,
  });

  if (!res.ok) {
    console.error('Query failed with status:', res.status);
    const errorText = await res.text();
    console.error('Error response:', errorText);
    throw new Error(`Query failed with status: ${res.status}`);
  }

  // Get the raw text first for debugging
  const rawText = await res.text();
  console.log('Raw API response:', rawText);

  // Early return for empty responses
  if (!rawText || rawText.trim() === '') {
    return {
      response: 'Jeg beklager, men jeg kunne ikke finde et svar på dit spørgsmål.',
      products: [],
      query_history: undefined
    };
  }

  try {
    // Try to parse as JSON
    const data = JSON.parse(rawText);
    console.log('Parsed JSON data:', data);
    return {
      response: data.response || data.answer || rawText || '',
      products: data.products || [],
      query_history: data.query_history
    };
  } catch (e) {
    console.log('Response is not JSON, using as plain text');
    // Not JSON, return as plain text response
    return {
      response: rawText,
      products: [], // No products for plain text response
      query_history: undefined
    };
  }
}

export async function getStores(): Promise<BackendStore[]> {
  const res = await fetch(`${API_BASE}/stores`, { next: { revalidate: 3600 } });
  if (!res.ok) return [];
  return res.json();
}

// Parsing status interface and functions
export interface ParsingStatus {
    status: 'queued' | 'processing' | 'completed' | 'error' | 'cancelled' | 'cancelling' | 'skipped' | 'unknown';
    message: string;
    products_found?: number;
    last_updated?: string; // 🔧 Added for enterprise dashboard
}

// Function to get the status of all processing catalogs
export async function getParsingStatus(token: string): Promise<Record<string, ParsingStatus>> {
  const res = await fetch(`${API_BASE}/parsing_status`, {
    headers: { 'Authorization': `Bearer ${token}` },
    cache: 'no-store' // Ensure we always get the latest status
  });
  if (!res.ok) throw new Error('Failed to fetch parsing status');
  return res.json();
}

// Function to re-parse a specific catalog
export async function reparseCatalog(catalogId: number, token: string): Promise<{ message: string }> {
  const res = await fetch(`${API_BASE}/api/parse_catalog/${catalogId}`, {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` }
  });
  if (!res.ok) {
      const err = await res.json();
      throw new Error(err.detail || 'Failed to trigger re-parse');
  }
  return res.json();
}

// Function to stop parsing a catalog
export async function stopParsing(catalogId: number, token: string): Promise<{ message: string }> {
  const res = await fetch(`${API_BASE}/stop_parsing_catalog/${catalogId}`, {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` }
  });
  if (!res.ok) {
    const err = await res.json();
    throw new Error(err.detail || 'Failed to send stop request');
  }
  return res.json();
}

// 🏗️ ENTERPRISE API FUNCTIONS
export async function getOperationLogs(token: string, limit: number = 50): Promise<any> {
  const res = await fetch(`${API_BASE}/api/admin/operation_logs?limit=${limit}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
  if (!res.ok) {
    throw new Error('Failed to fetch operation logs');
  }
  return res.json();
}

export async function triggerReconciliation(token: string): Promise<any> {
  const res = await fetch(`${API_BASE}/api/admin/reconcile_status`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) {
    throw new Error('Failed to trigger reconciliation');
  }
  return res.json();
}

export async function cleanMemory(token: string, maxAgeHours: number = 24): Promise<any> {
  const res = await fetch(`${API_BASE}/api/admin/cleanup_memory?max_age_hours=${maxAgeHours}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) {
    throw new Error('Failed to clean memory');
  }
  return res.json();
}

export async function triggerManualScrape(token: string, storeName: string): Promise<any> {
  const res = await fetch(`${API_BASE}/api/admin/manual_scrape/${storeName}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) {
    throw new Error('Failed to trigger manual scrape');
  }
  return res.json();
}

export async function getAvailableScrapers(token: string): Promise<string[]> {
  const res = await fetch(`${API_BASE}/api/admin/available_scrapers`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
  if (!res.ok) {
    // Return fallback list if API fails - matches actual config files
    return ['bilka', 'brugsen', 'discount365', 'fotex', 'lidl', 'meny', 'minkobmand', 'netto', 'rema1000', 'spar', 'superbrugsen'];
  }
  return res.json();
}
