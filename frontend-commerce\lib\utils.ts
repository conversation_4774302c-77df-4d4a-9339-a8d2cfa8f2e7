import { ReadonlyURLSearchParams } from 'next/navigation';

export const baseUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL
  ? `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`
  : 'http://localhost:3000';

export const createUrl = (
  pathname: string,
  params: URLSearchParams | ReadonlyURLSearchParams
) => {
  const paramsString = params.toString();
  const queryString = `${paramsString.length ? '?' : ''}${paramsString}`;

  return `${pathname}${queryString}`;
};

export const ensureStartsWith = (stringToCheck: string, startsWith: string) =>
  stringToCheck.startsWith(startsWith)
    ? stringToCheck
    : `${startsWith}${stringToCheck}`;

export function getStoreLogoUrl(storeName?: string): string | null {
  if (!storeName) return null;

  // This function is deprecated - logo URLs should come from the API via /stores endpoint
  // which fetches them from Google Cloud Storage
  console.warn(`getStoreLogoUrl is deprecated. Store logos should come from API /stores endpoint. Called for: ${storeName}`);

  // Return null to force using API-provided logo URLs
  return null;
}

export const validateEnvironmentVariables = () => {
  // Shopify validation removed. No environment variables are required now.
};
