# File: scrapers/store_scrapers/lidl_scraper.py

import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional
from playwright.async_api import ElementHandle

# Assuming scraper_core.py is in the parent directory (scrapers/)
from ..scraper_core import <PERSON><PERSON><PERSON>raper, Playwright<PERSON><PERSON>out<PERSON>rror, Play<PERSON><PERSON><PERSON>r, Page


class LidlScraper(BaseScraper):
    """
    Scraper specifically for Lidl catalogs.
    Navigates to the catalog list, identifies individual catalog flyers,
    opens the viewer for one, and downloads its PDF.
    """

    def __init__(self, config: dict):
        super().__init__(config)
        self.logger.info(f"LidlScraper initialized for store: {self.store_name}")

    async def scrape_catalogs(self) -> List[Dict[str, Any]]:
        if not self.page or not self.context or not self.catalog_list_url:
            self.logger.error("Page, context, or catalog_list_url not available for LidlScraper.")
            return []

        # Set default timeouts on the page
        try:
            default_nav_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            default_el_timeout = self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 30000)
            if self.page:
                self.page.set_default_navigation_timeout(default_nav_timeout)
                self.page.set_default_timeout(default_el_timeout)
                self.logger.info(f"LidlScraper: Page timeouts set - Nav: {default_nav_timeout}ms, Element: {default_el_timeout}ms.")
        except Exception as e_timeout_set:
            self.logger.error(f"LidlScraper: Error setting default timeouts: {e_timeout_set}")
            return [] # Critical if timeouts can't be set

        # 1. Navigate to the main Lidl page (or catalog list page if configured directly)
        # The config's catalog_list_url is "https://www.lidl.dk/c/tilbudsavis/s10013730"
        # This seems to be the catalog listing page directly.
        self.logger.debug(f"Navigating to Lidl catalog list: {self.catalog_list_url}. Page closed: {self.page.is_closed()}")
        nav_success = await self._navigate_to_url(self.page, self.catalog_list_url)
        if not nav_success:
            self.logger.error(f"Failed to navigate to Lidl catalog page: {self.catalog_list_url}")
            return []

        self.logger.debug(f"After navigation to catalog list. Page closed: {self.page.is_closed()}")


        # 2. Handle Cookies
        cookie_selectors = self.config.get('selectors', {}).get('cookie_accept_selectors', [])
        if cookie_selectors:
            self.logger.debug(f"Attempting to handle cookies. Page closed: {self.page.is_closed()}")
            await self._handle_cookies(self.page, cookie_selectors,
                                       timeout_ms=self.config.get('behavior_flags', {}).get('cookie_consent_timeout_ms', 15000))
            self.logger.debug(f"After cookie handling. Page closed: {self.page.is_closed()}")
        else:
            self.logger.info("No cookie selectors configured for Lidl.")
        



        # 3. Find Catalog Flyer Items
        # Your old config used: "catalog_item_selector": "div[id^='flyer-']"
        # And within that, title: "h4.flyer__title", date: "h2.flyer__name"
        flyer_item_selector = self.config.get('selectors', {}).get('catalog_item_selector')
        if not flyer_item_selector:
            self.logger.error("Lidl: 'catalog_item_selector' not configured.")
            return []

        self.logger.debug(f"Looking for catalog flyer items with selector: '{flyer_item_selector}'. Page closed: {self.page.is_closed()}")
        flyer_elements = await self.page.query_selector_all(flyer_item_selector)

        if not flyer_elements:
            self.logger.warning(f"Lidl: No catalog flyer items found with selector '{flyer_item_selector}'. Page URL: {self.page.url}")
            debug_dir = self._ensure_download_dir() / "debug"
            debug_dir.mkdir(exist_ok=True, parents=True)
            await self.page.screenshot(path=str(debug_dir / "lidl_no_flyers_found.png"))
            return []
        
        self.logger.info(f"Lidl: Found {len(flyer_elements)} raw catalog flyer item(s).")

        # --- Logic to select the correct flyer --- 
        selected_flyer_element: Optional[ElementHandle] = None
        selected_flyer_title_for_log: str = ""

        title_selector_within_tile = self.config.get('selectors', {}).get('catalog_title_selector_within_tile')
        preferred_substrings = [s.lower() for s in self.config.get('selectors', {}).get('preferred_title_substrings', [])]
        ignore_substrings = [s.lower() for s in self.config.get('selectors', {}).get('ignore_title_substrings', [])]

        if not title_selector_within_tile:
            self.logger.error("Lidl: 'catalog_title_selector_within_tile' is crucial for selection logic but not configured. Aborting.")
            return []

        candidate_flyers = [] # List of (element, title_text)
        for i, flyer_el in enumerate(flyer_elements):
            try:
                title_el = await flyer_el.query_selector(title_selector_within_tile)
                if not title_el:
                    self.logger.warning(f"Lidl: Tile {i+1} missing title element with selector '{title_selector_within_tile}'. Skipping.")
                    continue
                
                tile_title_text = (await title_el.text_content() or "").strip().lower()
                if not tile_title_text:
                    self.logger.warning(f"Lidl: Tile {i+1} has empty title. Skipping.")
                    continue
                
                self.logger.debug(f"Lidl: Tile {i+1}/{len(flyer_elements)} raw title: '{tile_title_text}'")

                # Check against ignore_substrings
                ignored = False
                for sub in ignore_substrings:
                    if sub in tile_title_text:
                        self.logger.info(f"Lidl: Tile '{tile_title_text}' ignored due to substring: '{sub}'.")
                        ignored = True
                        break
                if ignored:
                    continue
                
                candidate_flyers.append((flyer_el, tile_title_text))
                self.logger.debug(f"Lidl: Tile '{tile_title_text}' added as a candidate.")

            except Exception as e_title_extract:
                self.logger.error(f"Lidl: Error extracting title from flyer tile {i+1}: {e_title_extract}. Skipping this tile.")
                continue

        if not candidate_flyers:
            self.logger.warning(f"Lidl: No suitable catalog flyers found after filtering {len(flyer_elements)} items. Ignored all or all had errors.")
            return []

        # Try to find a preferred flyer
        for flyer_el, title_text in candidate_flyers:
            for sub in preferred_substrings:
                if sub in title_text:
                    selected_flyer_element = flyer_el
                    selected_flyer_title_for_log = title_text
                    self.logger.info(f"Lidl: Selected preferred catalog: '{title_text}' based on substring '{sub}'.")
                    break
            if selected_flyer_element:
                break
        
        # If no preferred found, take the first candidate
        if not selected_flyer_element and candidate_flyers:
            selected_flyer_element, selected_flyer_title_for_log = candidate_flyers[0]
            self.logger.info(f"Lidl: No preferred catalog found. Selecting first valid candidate: '{selected_flyer_title_for_log}'.")
        
        if not selected_flyer_element:
            self.logger.error("Lidl: Critical error - No flyer element could be selected after all checks. This shouldn't happen if candidates existed.")
            return []
        
        # --- End of selection logic --- 
        # Now, flyer_to_process is selected_flyer_element
        flyer_to_process = selected_flyer_element

        # 4. Extract Date and Title from the selected flyer item
        date_selector = self.config.get('selectors', {}).get('catalog_date_selector_within_tile')
        # title_selector (for display_title) is already title_selector_within_tile, title extracted during selection
        raw_date_info = "Lidl_Unknown_Date"
        display_title = selected_flyer_title_for_log.title() # Use the title we already have, properly cased

        if date_selector:
            try:
                date_element = await flyer_to_process.query_selector(date_selector)
                if date_element:
                    raw_date_info = (await date_element.text_content() or "").strip()
                    self.logger.info(f"Lidl: Extracted raw date info: '{raw_date_info}' from selected flyer tile ('{display_title}').")
                else:
                    self.logger.warning(f"Lidl: Date element not found within selected flyer ('{display_title}') using selector '{date_selector}'.")
            except Exception as e:
                self.logger.error(f"Lidl: Error extracting date from selected flyer ('{display_title}'): {e}")
        else:
            self.logger.warning("Lidl: 'catalog_date_selector_within_tile' not configured.")

        # Display title is already set from selected_flyer_title_for_log
        self.logger.info(f"Lidl: Proceeding with selected catalog: '{display_title}' (Raw date: '{raw_date_info}').")

        # 5. Click the flyer to open the catalog viewer (might open in new tab or navigate current)
        self.logger.info(f"Lidl: Attempting to click the selected flyer item ('{display_title}') to open viewer. Page closed: {self.page.is_closed()}")
        
        new_page_context = self.context
        viewer_page = self.page 
        original_page_url = self.page.url

        try:
            if self.context:
                async with self.context.expect_page(timeout=self.config.get('behavior_flags', {}).get('new_page_timeout_ms', 15000)) as new_page_event:
                    await flyer_to_process.click(timeout=10000) 
                    self.logger.info(f"Lidl: Clicked flyer item ('{display_title}'). Waiting for potential new page...")
                    viewer_page = await new_page_event.value
                    await viewer_page.wait_for_load_state('domcontentloaded', timeout=20000)
                    self.logger.info(f"Lidl: New page/tab opened for viewer. URL: {viewer_page.url}")
                    if self.page and not self.page.is_closed() and self.page != viewer_page:
                        self.logger.debug(f"Lidl: Closing original page: {original_page_url}")
                        await self.page.close()
                    self.page = viewer_page 
            else:
                await flyer_to_process.click(timeout=10000)
                await self.page.wait_for_load_state('domcontentloaded', timeout=20000)
                viewer_page = self.page
                self.logger.info(f"Lidl: Clicked flyer item ('{display_title}'), assuming navigation in current page. New URL: {viewer_page.url}")

        except PlaywrightTimeoutError:
            self.logger.warning(f"Lidl: Timeout waiting for new page after clicking flyer ('{display_title}'). Assuming navigation in current page: {self.page.url}")
            viewer_page = self.page 
            if self.page.url == original_page_url:
                self.logger.warning(f"Lidl: URL did not change after clicking flyer ('{display_title}') and timeout. Viewer might not have opened.")

        except Exception as e:
            self.logger.error(f"Lidl: Error clicking flyer ('{display_title}') or handling new page: {e}", exc_info=True)
            return []
        
        self.page = viewer_page 
        if self.page.is_closed():
            self.logger.error(f"Lidl: Viewer page {self.page.url} is closed unexpectedly before PDF download attempt for '{display_title}'.")
            return []


        self.logger.info(f"Lidl: Viewer page URL for '{display_title}': {self.page.url}. Page closed: {self.page.is_closed()}")

        # 5a. Handle any pre-download clicks if necessary (e.g., open a menu)
        pre_download_selectors = self.config.get('selectors', {}).get('pre_download_clicks_selectors', [])
        if pre_download_selectors:
            for i, pre_selector in enumerate(pre_download_selectors):
                try:
                    self.logger.info(f"Lidl: Attempting pre-download click {i+1} for '{display_title}' with selector: '{pre_selector}'. Page closed: {self.page.is_closed()}")
                    pre_click_element = self.page.locator(pre_selector).first
                    await pre_click_element.wait_for(state="visible", timeout=10000)
                    await pre_click_element.click(timeout=5000)
                    self.logger.info(f"Lidl: Successfully performed pre-download click on '{pre_selector}' for '{display_title}'.")
 
                except PlaywrightTimeoutError:
                    self.logger.warning(f"Lidl: Timeout waiting for pre-download element '{pre_selector}' for '{display_title}'.")
                except Exception as e:
                    self.logger.error(f"Lidl: Error during pre-download click on '{pre_selector}' for '{display_title}': {e}")

        # 5b. Locate and click the PDF download button on the viewer page
        pdf_download_selectors = self.config.get('selectors', {}).get('pdf_download_button_selectors_on_viewer', [])
        if not pdf_download_selectors:
            self.logger.error("Lidl: 'pdf_download_button_selectors_on_viewer' not configured for '{display_title}'.")
            return []

        download_button_found = False
        download = None
        for i, dl_selector in enumerate(pdf_download_selectors):
            try:
                self.logger.info(f"Lidl: Attempting to find PDF download button on viewer with selector: '{dl_selector}' (Attempt {i+1}). Page closed: {self.page.is_closed()}")
                download_button = self.page.locator(dl_selector).first
                await download_button.wait_for(state="visible", timeout=self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 15000) / len(pdf_download_selectors))
                
                if await download_button.is_visible():
                    self.logger.info(f"Lidl: PDF download button '{dl_selector}' found. Expecting download...")
                    async with self.page.expect_download(timeout=self.config.get('behavior_flags', {}).get('download_event_timeout_ms', 45000)) as download_info:
                        await download_button.click(timeout=5000)
                    download = await download_info.value
                    self.logger.info(f"Lidl: Download event triggered for selector '{dl_selector}'.")
                    download_button_found = True
                    break 
            except PlaywrightTimeoutError:
                self.logger.debug(f"Lidl: Timeout for PDF download button selector '{dl_selector}'.")
            except Exception as e:
                self.logger.warning(f"Lidl: Error with PDF download button selector '{dl_selector}': {e}")
        
        if not download_button_found or not download:
            self.logger.error(f"Lidl: Failed to initiate PDF download from viewer page. Page URL: {self.page.url}. Page closed: {self.page.is_closed()}")
            debug_dir = self._ensure_download_dir() / "debug"
            debug_dir.mkdir(exist_ok=True, parents=True)
            await self.page.screenshot(path=str(debug_dir / "lidl_viewer_page_no_download.png"))
            return []

        # 6. Save the downloaded PDF
        download_url = download.url
        suggested_filename_from_download = download.suggested_filename
        
        # Use a combination of store, date info, and title for a unique filename
        base_filename_parts = [self.store_name]
        if raw_date_info and raw_date_info != "Lidl_Unknown_Date":
            base_filename_parts.append(self._sanitize_filename(raw_date_info))
        if display_title and display_title != f"{self.store_name} Catalog":
             # Avoid duplicating store name if already in title
            sanitized_display_title = self._sanitize_filename(display_title)
            if self.store_name.lower() not in sanitized_display_title.lower():
                base_filename_parts.append(sanitized_display_title)
            elif len(base_filename_parts) == 1: # Only store name so far
                base_filename_parts.append(sanitized_display_title)


        if not base_filename_parts or len(base_filename_parts) == 1 and base_filename_parts[0] == self.store_name: # only store name
            base_filename_parts.append(self._sanitize_filename(suggested_filename_from_download or "catalog"))
            
        final_pdf_filename = "_".join(base_filename_parts) + ".pdf"
        
        download_dir = self._ensure_download_dir()
        local_pdf_path = download_dir / final_pdf_filename

        try:
            await download.save_as(local_pdf_path)
            self.logger.info(f"Lidl: Successfully saved PDF to: {local_pdf_path}")
            
            catalog_data = {
                "store_name": self.store_name,
                "title": display_title,
                "raw_date_info": raw_date_info,
                "pdf_url": download_url,
                "local_path": str(local_pdf_path)
            }
            self.collected_catalogs.append(catalog_data)
            
        except Exception as e:
            self.logger.error(f"Lidl: Error saving downloaded PDF: {e}", exc_info=True)

        return self.collected_catalogs