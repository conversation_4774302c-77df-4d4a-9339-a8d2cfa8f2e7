#!/usr/bin/env python3
"""
Safety test for the enhanced utils.py using real database data patterns.
This ensures the enhanced version works with actual production data.
"""

import sys
from utils import parse_and_calculate_unit_price

# Real unit strings from your parser logs that were failing
REAL_FAILING_CASES = [
    "144 g / 6 stk.",
    "750 ml.",
    "125 g. 10 stk.",
    "175 g.",
    "900 ml.",
    "330-720 ml.",
    "stk.",
    "g",
    "42 stk.",
    "200-300 ml",
    "150-350 stk.",
    "500-750 ml",
    "40-70 stk.",
]

# Real unit strings that should continue working
REAL_WORKING_CASES = [
    "500g",
    "1 kg", 
    "1.5 l",
    "2x100g",
    "3 x 75 cl",
    "pakke",
    "pose",
    "rulle",
    "bakke",
]

def test_enhanced_utils_safety():
    """Test the enhanced utils with real data patterns."""
    print("🔒 ENHANCED UTILS SAFETY TEST")
    print("=" * 50)
    print("Testing enhanced utils.py with real database patterns...")
    
    total_tests = 0
    successful_tests = 0
    errors = []
    
    print("\n📋 Testing previously failing cases:")
    for unit_string in REAL_FAILING_CASES:
        total_tests += 1
        try:
            result = parse_and_calculate_unit_price(unit_string, 20.00)
            
            # Check if we got a reasonable result
            if result.get("unit_type") is not None:
                print(f"✅ '{unit_string}' → {result}")
                successful_tests += 1
            else:
                print(f"⚠️  '{unit_string}' → Still no unit_type: {result}")
                
        except Exception as e:
            print(f"❌ '{unit_string}' → ERROR: {e}")
            errors.append(f"'{unit_string}': {e}")
    
    print("\n📋 Testing previously working cases:")
    for unit_string in REAL_WORKING_CASES:
        total_tests += 1
        try:
            result = parse_and_calculate_unit_price(unit_string, 20.00)
            
            # These should definitely work
            if result.get("quantity") is not None or result.get("unit_type") is not None:
                print(f"✅ '{unit_string}' → {result}")
                successful_tests += 1
            else:
                print(f"❌ '{unit_string}' → REGRESSION: {result}")
                errors.append(f"REGRESSION: '{unit_string}' stopped working")
                
        except Exception as e:
            print(f"❌ '{unit_string}' → ERROR: {e}")
            errors.append(f"'{unit_string}': {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SAFETY TEST RESULTS:")
    print(f"Total tests: {total_tests}")
    print(f"Successful: {successful_tests}")
    print(f"Success rate: {(successful_tests/total_tests)*100:.1f}%")
    
    if errors:
        print(f"\n⚠️  {len(errors)} ISSUES FOUND:")
        for error in errors:
            print(f"   - {error}")
    else:
        print("\n✅ NO ISSUES FOUND!")
    
    # Final recommendation
    if successful_tests >= total_tests * 0.9:  # 90% success rate
        print("\n🚀 SAFETY CHECK PASSED!")
        print("   Enhanced utils.py is ready for production!")
        return True
    else:
        print("\n⚠️  SAFETY CHECK FAILED!")
        print("   Review issues before deploying.")
        return False

def test_db_compatibility():
    """Test that the return format is exactly what the database expects."""
    print("\n🗄️  DATABASE COMPATIBILITY TEST")
    print("=" * 50)
    
    test_case = "500g"
    result = parse_and_calculate_unit_price(test_case, 20.00)
    
    # Check exact structure
    expected_keys = {"quantity", "unit_type", "price_per_base_unit", "base_unit_type"}
    actual_keys = set(result.keys())
    
    if expected_keys == actual_keys:
        print("✅ Return structure matches database schema exactly!")
        print(f"   Keys: {sorted(actual_keys)}")
        return True
    else:
        print("❌ Return structure mismatch!")
        print(f"   Expected: {sorted(expected_keys)}")
        print(f"   Actual: {sorted(actual_keys)}")
        print(f"   Missing: {expected_keys - actual_keys}")
        print(f"   Extra: {actual_keys - expected_keys}")
        return False

if __name__ == "__main__":
    try:
        print("🧪 Running comprehensive safety tests for enhanced utils.py...")
        
        # Test 1: Safety with real data
        safety_passed = test_enhanced_utils_safety()
        
        # Test 2: Database compatibility
        db_compatible = test_db_compatibility()
        
        # Final verdict
        print("\n" + "=" * 50)
        print("🎯 FINAL SAFETY VERDICT:")
        
        if safety_passed and db_compatible:
            print("✅ ALL SAFETY CHECKS PASSED!")
            print("🚀 Enhanced utils.py is SAFE TO DEPLOY!")
            print("   - Dramatically improves parsing")
            print("   - Maintains database compatibility")
            print("   - No regressions detected")
        else:
            print("⚠️  SAFETY ISSUES DETECTED!")
            print("   Review and fix issues before deployment.")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Safety test failed: {e}")
        sys.exit(1)
