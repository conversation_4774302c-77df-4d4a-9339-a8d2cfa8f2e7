Typography & Spacing Guide 

Modern but neutral. Usability over inconvinience. Clarity over beauty. Light themed. Modern but neutral. Thoughtful spacing. 

Typography
Font stack: Roboto, Helvetica Neue, sans-serif

Font sizes:

text-sm (14px) – metadata, timestamps

text-base (16px) – body content

text-lg (18px) – section headers

text-xl (20px–24px) – page titles

Line height: leading-relaxed for body, leading-tight for headers

Font weight:

font-normal for body

font-semibold for headings

font-medium for buttons and labels

Text color: text-[#333] for body, text-[#6482AD] for highlights

Spacing
Base unit: 8px (Tailwind: p-2, m-2)

Content padding: px-4 py-6 on cards and sections

Card/grid gap: gap-4 or gap-6

Header/body spacing: mb-2, mb-4, etc. — keep vertical rhythm

Mobile breakpoint: sm: tweaks only where needed — layout should already breathe

Component styling with palette
Primary button: bg-[#6482AD] text-white hover:bg-[#7FA1C3]

Secondary: bg-[#E2DAD6] text-[#333] hover:bg-[#F5EDED]

Card bg: bg-[#F5EDED] border border-[#E2DAD6] rounded-md shadow-sm

Accent text: text-[#6482AD] font-medium