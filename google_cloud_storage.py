"""
Google Cloud Storage Implementation
Implements the StorageInterface for Google Cloud Storage operations
"""

import os
import logging
from typing import List, Optional, Union
from pathlib import Path
from google.cloud import storage
from google.cloud.exceptions import NotFound, GoogleCloudError
from storage_interface import StorageInterface, StorageError, FileNotFoundError, UploadError, DownloadError

logger = logging.getLogger(__name__)


class GoogleCloudStorage(StorageInterface):
    """Google Cloud Storage implementation of StorageInterface"""
    
    def __init__(self, bucket_name: str = None):
        """
        Initialize Google Cloud Storage client with proper authentication

        Args:
            bucket_name: Name of the GCS bucket (defaults to config value)
        """
        try:
            # Try to get credentials from environment
            credentials_json = os.getenv('GOOGLE_CLOUD_CREDENTIALS_JSON')
            if credentials_json:
                # Parse JSON credentials from environment variable
                import json
                from google.oauth2 import service_account
                credentials_info = json.loads(credentials_json)
                credentials = service_account.Credentials.from_service_account_info(credentials_info)
                self.client = storage.Client(credentials=credentials)
                logger.info("✅ Using JSON credentials from GOOGLE_CLOUD_CREDENTIALS_JSON")
            else:
                # Fall back to default credentials (for local development with gcloud auth)
                self.client = storage.Client()
                logger.info("✅ Using default Google Cloud credentials")

            self.bucket_name = bucket_name or os.getenv('GOOGLE_CLOUD_BUCKET', 'tilbudsjaegeren')
            self.bucket = self.client.bucket(self.bucket_name)
            logger.info(f"✅ Initialized Google Cloud Storage with bucket: {self.bucket_name}")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Google Cloud Storage: {e}")
            raise StorageError(f"GCS initialization failed: {e}")
    
    def upload_file(self, local_path: Union[str, Path], cloud_path: str) -> bool:
        """Upload a file to Google Cloud Storage"""
        try:
            local_path = Path(local_path)
            if not local_path.exists():
                logger.error(f"Local file does not exist: {local_path}")
                return False
            
            blob = self.bucket.blob(cloud_path)
            blob.upload_from_filename(str(local_path))
            
            logger.info(f"Successfully uploaded {local_path} to gs://{self.bucket_name}/{cloud_path}")
            return True
            
        except GoogleCloudError as e:
            logger.error(f"GCS upload failed for {local_path} -> {cloud_path}: {e}")
            raise UploadError(f"Failed to upload {local_path}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during upload {local_path} -> {cloud_path}: {e}")
            return False
    
    def download_file(self, cloud_path: str, local_path: Union[str, Path]) -> bool:
        """Download a file from Google Cloud Storage"""
        try:
            local_path = Path(local_path)
            local_path.parent.mkdir(parents=True, exist_ok=True)
            
            blob = self.bucket.blob(cloud_path)
            if not blob.exists():
                logger.error(f"File does not exist in GCS: {cloud_path}")
                return False
            
            blob.download_to_filename(str(local_path))
            
            logger.info(f"Successfully downloaded gs://{self.bucket_name}/{cloud_path} to {local_path}")
            return True
            
        except NotFound:
            logger.error(f"File not found in GCS: {cloud_path}")
            raise FileNotFoundError(f"File not found: {cloud_path}")
        except GoogleCloudError as e:
            logger.error(f"GCS download failed for {cloud_path} -> {local_path}: {e}")
            raise DownloadError(f"Failed to download {cloud_path}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during download {cloud_path} -> {local_path}: {e}")
            return False
    
    def file_exists(self, cloud_path: str) -> bool:
        """Check if a file exists in Google Cloud Storage"""
        try:
            blob = self.bucket.blob(cloud_path)
            exists = blob.exists()
            logger.debug(f"File exists check for {cloud_path}: {exists}")
            return exists
        except Exception as e:
            logger.error(f"Error checking file existence for {cloud_path}: {e}")
            return False
    
    def delete_file(self, cloud_path: str) -> bool:
        """Delete a file from Google Cloud Storage"""
        try:
            blob = self.bucket.blob(cloud_path)
            if not blob.exists():
                logger.warning(f"File does not exist, cannot delete: {cloud_path}")
                return False
            
            blob.delete()
            logger.info(f"Successfully deleted gs://{self.bucket_name}/{cloud_path}")
            return True
            
        except NotFound:
            logger.warning(f"File not found for deletion: {cloud_path}")
            return False
        except GoogleCloudError as e:
            logger.error(f"GCS delete failed for {cloud_path}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during delete {cloud_path}: {e}")
            return False
    
    def list_files(self, prefix: str = "") -> List[str]:
        """List files in Google Cloud Storage with optional prefix"""
        try:
            blobs = self.client.list_blobs(self.bucket_name, prefix=prefix)
            file_list = [blob.name for blob in blobs]
            logger.debug(f"Listed {len(file_list)} files with prefix '{prefix}'")
            return file_list
        except Exception as e:
            logger.error(f"Error listing files with prefix '{prefix}': {e}")
            return []
    
    def get_file_url(self, cloud_path: str, expires_in: int = 3600) -> Optional[str]:
        """Get a signed URL for a file in Google Cloud Storage"""
        try:
            blob = self.bucket.blob(cloud_path)
            if not blob.exists():
                logger.error(f"Cannot generate URL for non-existent file: {cloud_path}")
                return None
            
            url = blob.generate_signed_url(expiration=expires_in)
            logger.debug(f"Generated signed URL for {cloud_path} (expires in {expires_in}s)")
            return url
            
        except Exception as e:
            logger.error(f"Error generating signed URL for {cloud_path}: {e}")
            return None
    
    def get_file_size(self, cloud_path: str) -> Optional[int]:
        """Get the size of a file in Google Cloud Storage"""
        try:
            blob = self.bucket.blob(cloud_path)
            if not blob.exists():
                return None
            
            blob.reload()  # Refresh metadata
            size = blob.size
            logger.debug(f"File size for {cloud_path}: {size} bytes")
            return size
            
        except Exception as e:
            logger.error(f"Error getting file size for {cloud_path}: {e}")
            return None
    
    def copy_file(self, source_path: str, dest_path: str) -> bool:
        """Copy a file within Google Cloud Storage"""
        try:
            source_blob = self.bucket.blob(source_path)
            if not source_blob.exists():
                logger.error(f"Source file does not exist: {source_path}")
                return False
            
            dest_blob = self.bucket.copy_blob(source_blob, self.bucket, dest_path)
            logger.info(f"Successfully copied {source_path} to {dest_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error copying file {source_path} -> {dest_path}: {e}")
            return False
