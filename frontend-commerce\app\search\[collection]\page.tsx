import { getProducts, getCatalogs, getStores, BackendProduct } from 'lib/backend';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import Grid from 'components/grid';
import ProductGridItems from 'components/layout/product-grid-items';
import EnhancedProductGrid from 'components/layout/enhanced-product-grid';
import { defaultSort, sorting } from 'lib/constants';

// TODO: dynamic metadata from backend collections when available
export async function generateMetadata(props: {
  params: Promise<{ collection: string }>;
}): Promise<Metadata> {
  const params = await props.params;
  // const collection = await getCollection(params.collection);

  // collection support not yet implemented

  return {
    title: `Collection ${params.collection}` ,
    description: `Products in ${params.collection}`
  };
}

export default async function CategoryPage(props: {
  params: Promise<{ collection: string }>;
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const searchParams = await props.searchParams;
  const params = await props.params;
  const { sort } = searchParams as { [key: string]: string };

  // Get all data needed for filtering
  const [allProducts, catalogs, stores] = await Promise.all([
    getProducts(),
    getCatalogs(),
    getStores()
  ]);

  // Find the store by name
  const targetStore = stores.find(store =>
    store.name.toLowerCase() === params.collection.toLowerCase()
  );

  // Filter products by store (via catalog)
  const products: BackendProduct[] = targetStore
    ? allProducts.filter(product => {
        const catalog = catalogs.find(c => c.id === product.catalog_id);
        return catalog?.store_id === targetStore.id;
      })
    : [];

  return (
    <section>
      {products.length === 0 ? (
        <p className="py-3 text-lg">{`No products found in this collection`}</p>
      ) : (
        <EnhancedProductGrid
          products={products}
          title={targetStore ? `${targetStore.name} Products` : "Store Products"}
          showCount={true}
        />
      )}
    </section>
  );
}
