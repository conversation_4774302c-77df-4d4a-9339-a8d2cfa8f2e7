# File: scrapers/store_scrapers/fotex_scraper.py

import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional

from ..scraper_core import BaseScraper, PlaywrightTimeoutError, PlaywrightError, Page


class FotexScraper(BaseScraper):
    """
    Scraper specifically for Føtex catalogs.
    Handles multi-step navigation including a new tab for the catalog viewer.
    """

    def __init__(self, config: dict):
        super().__init__(config)
        self.logger.info(f"FotexScraper initialized for store: {self.store_name}")

    async def scrape_catalogs(self) -> List[Dict[str, Any]]:
        if not self.page or not self.context or not self.catalog_list_url:
            self.logger.error("Page, context, or catalog_list_url not available for FotexScraper.")
            return []

        # Set default timeouts on the page
        try:
            default_nav_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            default_el_timeout = self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 30000)
            if self.page:
                self.page.set_default_navigation_timeout(default_nav_timeout)
                self.page.set_default_timeout(default_el_timeout)
                self.logger.info(f"FotexScraper: Page timeouts set - Nav: {default_nav_timeout}ms, Element: {default_el_timeout}ms.")
        except Exception as e_timeout_set:
            self.logger.error(f"FotexScraper: Error setting default timeouts: {e_timeout_set}")
            return []

        # 1. Navigate to the initial Føtex catalog page
        self.logger.debug(f"Navigating to Føtex start page: {self.catalog_list_url}. Page closed: {self.page.is_closed()}")
        nav_success = await self._navigate_to_url(self.page, self.catalog_list_url)
        if not nav_success:
            self.logger.error(f"Failed to navigate to Føtex start page: {self.catalog_list_url}")
            return []

        self.logger.debug(f"After navigation to start page. Page closed: {self.page.is_closed()}")

        # 2. Handle Cookies
        cookie_selectors = self.config.get('selectors', {}).get('cookie_accept_selectors', [])
        if cookie_selectors:
            await self._handle_cookies(self.page, cookie_selectors,
                                       timeout_ms=self.config.get('behavior_flags', {}).get('cookie_consent_timeout_ms', 15000))
        else:
            self.logger.info("No cookie selectors configured for Føtex.")


        # 3. Grab Date from the main page
        raw_date_info = f"Føtex_Unknown_Date_{Path(self.page.url).name}" # Default
        date_element_selector = self.config.get('selectors', {}).get('date_element_on_main_page')
        if date_element_selector:
            try:
                date_el = self.page.locator(date_element_selector).first
                await date_el.wait_for(state="visible", timeout=5000)
                text_content = await date_el.text_content()
                if text_content:
                    raw_date_info = text_content.strip()
                    self.logger.info(f"Føtex: Extracted raw_date_info from main page: '{raw_date_info}'")
                else:
                    self.logger.warning(f"Føtex: Date element '{date_element_selector}' found but had no text.")
            except PlaywrightTimeoutError:
                self.logger.warning(f"Føtex: Timeout finding date element '{date_element_selector}' on main page.")
            except Exception as e:
                self.logger.warning(f"Føtex: Error extracting date from main page: {e}")
        else:
            self.logger.warning("Føtex: 'date_element_on_main_page' selector not configured.")
        
        display_title = f"{self.store_name} Catalog ({raw_date_info.split('-')[0].strip()})" if "Unknown_Date" not in raw_date_info else f"{self.store_name} Catalog"


        # 4. Click "Læs avisen her" button, which opens a new tab
        read_catalog_button_selector = self.config.get('selectors', {}).get('read_catalog_button_selector')
        if not read_catalog_button_selector:
            self.logger.error("Føtex: 'read_catalog_button_selector' not configured.")
            return []

        viewer_page: Optional[Page] = None
        try:
            self.logger.info(f"Føtex: Attempting to click 'Læs avisen her' button: '{read_catalog_button_selector}'")
            read_button = self.page.locator(read_catalog_button_selector).first
            await read_button.wait_for(state="visible", timeout=self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 20000))

            async with self.context.expect_page(timeout=self.config.get('behavior_flags',{}).get('new_page_timeout_ms', 30000)) as new_page_info:
                await read_button.click(timeout=5000)
            
            viewer_page = await new_page_info.value
            await viewer_page.wait_for_load_state('domcontentloaded', timeout=self.config.get('behavior_flags',{}).get('navigation_timeout_ms', 60000))
            self.logger.info(f"Føtex: New viewer page opened. URL: {viewer_page.url}")


        except PlaywrightTimeoutError:
            self.logger.error(f"Føtex: Timeout waiting for 'Læs avisen her' button or for new page to open.", exc_info=True)
            return []
        except Exception as e:
            self.logger.error(f"Føtex: Error clicking 'Læs avisen her' or handling new page: {e}", exc_info=True)
            return []

        if not viewer_page or viewer_page.is_closed():
            self.logger.error("Føtex: Viewer page was not opened or is closed.")
            return []

        # 5. On the new viewer page, click the download icon
        # Correctly get the selector from the config for the download trigger
        download_trigger_selector_from_config = self.config.get('selectors', {}).get('pdf_download_button_on_viewer_page')
        if not download_trigger_selector_from_config:
            self.logger.error("Føtex Viewer: 'pdf_download_button_on_viewer_page' selector not found in config.")
            await viewer_page.close()
            return []

        download: Optional[Any] = None
        try:
            self.logger.info(f"Fotex Viewer: Looking for download trigger using selector from config: '{download_trigger_selector_from_config}'")
            download_trigger = viewer_page.locator(download_trigger_selector_from_config).first
            
            # Increased timeout for visibility and added a small delay before click
            await download_trigger.wait_for(state="visible", timeout=self.config.get('behavior_flags',{}).get('element_wait_timeout_ms', 25000))


            async with viewer_page.expect_download(timeout=self.config.get('behavior_flags',{}).get('download_event_timeout_ms', 60000)) as download_info:
                await download_trigger.click(timeout=10000) # Increased click timeout
            download = await download_info.value
            self.logger.info(f"Føtex Viewer: PDF Download event triggered using selector '{download_trigger_selector_from_config}'.")

        except PlaywrightTimeoutError:
            self.logger.error(f"Føtex Viewer: Timeout finding or clicking PDF download trigger ('{download_trigger_selector_from_config}'), or waiting for download. URL: {viewer_page.url}", exc_info=True)
            debug_dir = self._ensure_download_dir() / "debug"
            debug_dir.mkdir(exist_ok=True, parents=True)
            await viewer_page.screenshot(path=str(debug_dir / "fotex_viewer_download_timeout.png"))
            await viewer_page.close()
            return []
        except Exception as e:
            self.logger.error(f"Føtex Viewer: Error interacting with PDF download trigger ('{download_trigger_selector_from_config}') or during download: {e}. URL: {viewer_page.url}", exc_info=True)
            debug_dir = self._ensure_download_dir() / "debug"
            debug_dir.mkdir(exist_ok=True, parents=True)
            await viewer_page.screenshot(path=str(debug_dir / "fotex_viewer_download_exception.png"))
            await viewer_page.close()
            return []

        if not download:
            self.logger.error("Føtex: PDF download object not obtained.")
            await viewer_page.close()
            return []
            
        # 6. Save the downloaded PDF
        download_url = download.url
        suggested_filename = download.suggested_filename or f"{self._sanitize_filename(display_title)}.pdf"
        
        final_pdf_filename = self._sanitize_filename(suggested_filename)
        if not final_pdf_filename.lower().endswith(".pdf"):
            final_pdf_filename += ".pdf"

        download_dir = self._ensure_download_dir()
        local_pdf_path = download_dir / final_pdf_filename

        try:
            await download.save_as(local_pdf_path)
            self.logger.info(f"Føtex: Successfully saved PDF to: {local_pdf_path}")
            
            catalog_data = {
                "store_name": self.store_name,
                "title": display_title, 
                "raw_date_info": raw_date_info,
                "pdf_url": download_url, 
                "local_path": str(local_pdf_path)
            }
            self.collected_catalogs.append(catalog_data)
            
        except Exception as e:
            self.logger.error(f"Føtex: Error saving downloaded PDF: {e}", exc_info=True)
        finally:
            if viewer_page and not viewer_page.is_closed():
                await viewer_page.close()
                self.logger.debug("Føtex: Viewer page closed.")

        return self.collected_catalogs