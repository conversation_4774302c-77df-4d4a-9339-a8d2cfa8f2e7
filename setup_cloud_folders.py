#!/usr/bin/env python3
"""
<PERSON><PERSON>t to set up the initial folder structure in Google Cloud Storage
and upload existing files from local directories.
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from cloud_storage import cloud_storage
except ImportError as e:
    print(f"❌ Missing cloud_storage module: {e}")
    sys.exit(1)

def create_folder_structure():
    """Create the initial folder structure in Google Cloud Storage"""
    
    print("🏗️ Setting up Google Cloud Storage folder structure...")
    print("=" * 60)
    
    if not cloud_storage.is_available():
        print("❌ Cloud storage not available")
        return False
    
    # Create folder structure by uploading placeholder files
    folders = [
        "catalogs/.gitkeep",
        "images/.gitkeep", 
        "logos/supermarkets/.gitkeep",
        "temp/processing/.gitkeep",
        "temp/uploads/.gitkeep",
        "static/css/.gitkeep",
        "static/js/.gitkeep",
        "static/fonts/.gitkeep"
    ]
    
    print("\n📁 Creating folder structure...")
    for folder_path in folders:
        try:
            # Upload a small placeholder file to create the folder
            cloud_storage.client.bucket("tilbudsjaegeren").blob(folder_path).upload_from_string("")
            print(f"✅ Created: {folder_path}")
        except Exception as e:
            print(f"❌ Failed to create {folder_path}: {e}")
    
    return True

def upload_existing_files():
    """Upload existing local files to cloud storage"""
    
    print("\n📤 Uploading existing files...")
    
    # Upload existing PDFs
    catalogs_dir = Path("./catalogs")
    if catalogs_dir.exists():
        print(f"\n📄 Uploading PDFs from {catalogs_dir}...")
        pdf_count = 0
        for pdf_file in catalogs_dir.glob("*.pdf"):
            cloud_path = f"catalogs/{pdf_file.name}"
            if cloud_storage.upload_file(str(pdf_file), cloud_path):
                print(f"✅ Uploaded: {pdf_file.name}")
                pdf_count += 1
            else:
                print(f"❌ Failed: {pdf_file.name}")
        print(f"📊 Uploaded {pdf_count} PDF files")
    
    # Upload existing logos
    logos_dir = Path("./logos")
    if logos_dir.exists():
        print(f"\n🎨 Uploading logos from {logos_dir}...")
        logo_count = 0
        
        # Upload supermarket logos
        supermarkets_dir = logos_dir / "supermarkets"
        if supermarkets_dir.exists():
            for logo_file in supermarkets_dir.glob("*"):
                if logo_file.is_file():
                    cloud_path = f"logos/supermarkets/{logo_file.name}"
                    if cloud_storage.upload_file(str(logo_file), cloud_path):
                        print(f"✅ Uploaded: supermarkets/{logo_file.name}")
                        logo_count += 1
        
        # Upload main logos
        for logo_file in logos_dir.glob("*"):
            if logo_file.is_file() and logo_file.suffix.lower() in ['.png', '.jpg', '.jpeg', '.svg', '.ico']:
                cloud_path = f"logos/{logo_file.name}"
                if cloud_storage.upload_file(str(logo_file), cloud_path):
                    print(f"✅ Uploaded: {logo_file.name}")
                    logo_count += 1
        
        print(f"📊 Uploaded {logo_count} logo files")
    
    # Upload static assets
    static_dir = Path("./static")
    if static_dir.exists():
        print(f"\n🌐 Uploading static assets from {static_dir}...")
        static_count = 0
        for static_file in static_dir.rglob("*"):
            if static_file.is_file():
                # Preserve directory structure
                relative_path = static_file.relative_to(static_dir)
                cloud_path = f"static/{relative_path.as_posix()}"
                if cloud_storage.upload_file(str(static_file), cloud_path):
                    print(f"✅ Uploaded: static/{relative_path}")
                    static_count += 1
        print(f"📊 Uploaded {static_count} static files")
    
    return True

def list_cloud_structure():
    """List the current cloud storage structure"""
    
    print("\n📋 Current cloud storage structure:")
    print("=" * 40)
    
    try:
        files = cloud_storage.list_files()
        
        # Group files by folder
        folders = {}
        for file_path in files:
            if '/' in file_path:
                folder = file_path.split('/')[0]
                if folder not in folders:
                    folders[folder] = []
                folders[folder].append(file_path)
            else:
                if 'root' not in folders:
                    folders['root'] = []
                folders['root'].append(file_path)
        
        for folder, files_in_folder in sorted(folders.items()):
            print(f"\n📁 {folder}/")
            for file_path in sorted(files_in_folder)[:10]:  # Show first 10 files
                print(f"   📄 {file_path}")
            if len(files_in_folder) > 10:
                print(f"   ... and {len(files_in_folder) - 10} more files")
        
        print(f"\n📊 Total files: {len(files)}")
        
    except Exception as e:
        print(f"❌ Error listing files: {e}")

def main():
    """Main setup function"""
    
    print("🚀 Google Cloud Storage Setup")
    print("=" * 60)
    
    # Step 1: Create folder structure
    if not create_folder_structure():
        print("❌ Failed to create folder structure")
        return False
    
    # Step 2: Upload existing files
    if not upload_existing_files():
        print("❌ Failed to upload existing files")
        return False
    
    # Step 3: Show final structure
    list_cloud_structure()
    
    print("\n🎉 Cloud storage setup complete!")
    print("\n📋 Next steps:")
    print("   1. Update configuration to use cloud storage")
    print("   2. Update scrapers to upload to cloud")
    print("   3. Update processors to download from cloud")
    print("   4. Remove local file dependencies")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
