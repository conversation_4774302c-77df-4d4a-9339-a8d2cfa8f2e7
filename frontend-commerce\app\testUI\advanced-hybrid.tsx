"use client";

import { useState, useEffect } from 'react';
import Image from "next/image";
import { getCatalogs, BackendCatalog } from '@/lib/backend';

export default function AdvancedHybridPage() {
  const [catalogs, setCatalogs] = useState<BackendCatalog[]>([]);
  const [question, setQuestion] = useState('');
  const [selectedCatalogs, setSelectedCatalogs] = useState<number[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [helpMode, setHelpMode] = useState(false);

  useEffect(() => {
    const fetchCatalogs = async () => {
      try {
        const data = await getCatalogs();
        setCatalogs(data.slice(0, 6));
      } catch (err) {
        console.error('Failed to load catalogs:', err);
      }
    };
    fetchCatalogs();
  }, []);

  const toggleCatalog = (id: number) => {
    setSelectedCatalogs(prev => 
      prev.includes(id) ? prev.filter(c => c !== id) : [...prev, id]
    );
  };

  const handleSearch = () => {
    setShowResults(true);
  };

  // Mock search results
  const mockResults = [
    {
      id: 1,
      name: "Arla Økologisk Mælk",
      price: "12,95 kr",
      originalPrice: "15,95 kr",
      store: "Netto",
      image: "/placeholder.png",
      savings: "3,00 kr",
      unit: "per liter",
      description: "Frisk økologisk mælk fra danske køer"
    },
    {
      id: 2,
      name: "Thise Økologisk Mælk",
      price: "13,50 kr",
      originalPrice: "16,50 kr",
      store: "SuperBrugsen",
      image: "/placeholder.png",
      savings: "3,00 kr",
      unit: "per liter",
      description: "Premium økologisk mælk fra Thy"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto space-y-16">
        
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">🎯 Advanced Hybrid Design</h1>
          <p className="text-lg text-gray-600">The perfect blend of simplicity and guidance</p>
        </div>

        {/* ===== ULTIMATE HYBRID: ADAPTIVE INTERFACE ===== */}
        <div className="border-t-8 border-indigo-500 pt-8">
          <h2 className="text-2xl font-bold text-indigo-600 mb-6">🚀 Adaptive Interface</h2>
          <p className="text-gray-600 mb-8"><strong>Concept:</strong> Starts simple like Google, reveals guidance when needed</p>
          
          <div className="bg-white min-h-96 p-8 rounded-lg shadow-lg">
            <div className="max-w-4xl mx-auto">
              
              {/* Adaptive Header */}
              <div className="text-center mb-8">
                <div className="w-20 h-20 relative mx-auto mb-4">
                  <Image 
                    src="/logos/logo-samlet.png" 
                    alt="Tilbudsjægeren"
                    fill
                    className="object-contain"
                  />
                </div>
                <h3 className="text-2xl font-light text-gray-800 mb-2">Tilbudsjægeren</h3>
                {helpMode && (
                  <p className="text-indigo-600 animate-fade-in">Vi hjælper dig med at finde de bedste tilbud! 👋</p>
                )}
              </div>

              {/* Smart Search Interface */}
              <div className="space-y-6">
                
                {/* Main Search */}
                <div className="relative">
                  <input
                    type="text"
                    placeholder={helpMode ? "Skriv hvad du søger efter (f.eks. 'billig kaffe')" : "Søg efter tilbud..."}
                    className={`w-full px-6 py-4 text-lg border rounded-full shadow-sm focus:outline-none focus:ring-2 transition-all ${
                      helpMode 
                        ? 'border-indigo-300 focus:ring-indigo-500 focus:border-indigo-500' 
                        : 'border-gray-300 focus:ring-blue-500 focus:border-transparent'
                    }`}
                    value={question}
                    onChange={(e) => setQuestion(e.target.value)}
                    onFocus={() => setHelpMode(true)}
                  />
                  <button 
                    onClick={handleSearch}
                    className={`absolute right-2 top-2 px-6 py-2 text-white rounded-full transition-all ${
                      helpMode 
                        ? 'bg-indigo-500 hover:bg-indigo-600' 
                        : 'bg-blue-500 hover:bg-blue-600'
                    }`}
                  >
                    {helpMode ? 'Find Tilbud' : 'Søg'}
                  </button>
                </div>

                {/* Progressive Disclosure */}
                {helpMode && (
                  <div className="animate-slide-down space-y-4">
                    
                    {/* Quick Examples */}
                    <div className="text-center">
                      <p className="text-sm text-indigo-600 mb-3">💡 Populære søgninger:</p>
                      <div className="flex flex-wrap justify-center gap-2">
                        {['økologisk mælk', 'billig kaffe', 'friske æg', 'dansk kød'].map((example) => (
                          <button
                            key={example}
                            onClick={() => setQuestion(example)}
                            className="px-4 py-2 bg-indigo-100 text-indigo-700 rounded-full text-sm hover:bg-indigo-200 transition-colors"
                          >
                            {example}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Store Selection with Guidance */}
                    <div className="bg-indigo-50 rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium text-indigo-800">Vælg dine foretrukne butikker</h4>
                        <span className="text-sm text-indigo-600">(valgfrit - vi søger i alle hvis du ikke vælger)</span>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-4">
                        {catalogs.map((catalog) => (
                          <div
                            key={catalog.id}
                            onClick={() => toggleCatalog(catalog.id)}
                            className={`p-4 rounded-lg border-2 cursor-pointer transition-all text-center ${
                              selectedCatalogs.includes(catalog.id) 
                                ? 'border-indigo-500 bg-indigo-100 shadow-md' 
                                : 'border-gray-300 hover:border-indigo-300 bg-white'
                            }`}
                          >
                            <img
                              src={catalog.store_logo_url || '/placeholder.png'}
                              alt={catalog.title}
                              className="w-12 h-12 object-contain mx-auto mb-2"
                            />
                            <div className="text-sm font-medium text-gray-700">{catalog.title.split(' ')[0]}</div>
                            {selectedCatalogs.includes(catalog.id) && (
                              <div className="text-indigo-600 text-sm mt-1 font-medium">✓ Valgt</div>
                            )}
                          </div>
                        ))}
                      </div>
                      
                      {selectedCatalogs.length > 0 && (
                        <div className="mt-4 text-center">
                          <p className="text-sm text-indigo-700">
                            👍 Perfekt! Vi søger i {selectedCatalogs.length} butik{selectedCatalogs.length > 1 ? 'ker' : ''}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Help Toggle */}
                <div className="text-center">
                  <button
                    onClick={() => setHelpMode(!helpMode)}
                    className="text-sm text-gray-500 hover:text-indigo-600 transition-colors"
                  >
                    {helpMode ? '🔼 Skjul hjælp' : '🔽 Vis hjælp og tips'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* ===== ADAPTIVE SEARCH RESULTS ===== */}
        {showResults && (
          <div className="border-t-8 border-green-500 pt-8">
            <h2 className="text-2xl font-bold text-green-600 mb-6">📊 Adaptive Search Results</h2>
            <p className="text-gray-600 mb-8">Results that adapt to user preference - clean or guided</p>
            
            <div className="space-y-8">
              
              {/* Results Header */}
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Fandt 2 produkter for "økologisk mælk"</h3>
                    {helpMode && (
                      <p className="text-green-600 text-sm mt-1">🎉 Her er de bedste tilbud vi fandt til dig!</p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">Vis:</span>
                    <button
                      onClick={() => setHelpMode(!helpMode)}
                      className={`px-3 py-1 text-xs rounded-full transition-colors ${
                        helpMode 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {helpMode ? 'Guidet' : 'Simpel'}
                    </button>
                  </div>
                </div>
              </div>

              {/* Adaptive Results */}
              <div className="space-y-4">
                {mockResults.map((result, index) => (
                  <div key={result.id} className={`bg-white rounded-lg shadow-sm transition-all ${
                    helpMode ? 'p-6 border-l-4 border-green-500' : 'p-4 border border-gray-200'
                  }`}>
                    <div className="flex items-center space-x-4">
                      {helpMode && (
                        <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-bold text-sm">
                          {index + 1}
                        </div>
                      )}
                      <img src={result.image} alt={result.name} className="w-16 h-16 object-contain bg-gray-50 rounded" />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{result.name}</h4>
                        {helpMode ? (
                          <div className="space-y-1">
                            <p className="text-sm text-gray-600">📍 {result.store}</p>
                            <p className="text-xs text-gray-500">{result.description}</p>
                            <p className="text-sm text-green-600">💰 Du sparer {result.savings} ({result.unit})</p>
                          </div>
                        ) : (
                          <p className="text-sm text-gray-600">{result.store}</p>
                        )}
                      </div>
                      <div className="text-right">
                        <div className={`font-bold text-green-600 ${helpMode ? 'text-xl' : 'text-lg'}`}>
                          {result.price}
                        </div>
                        <div className="text-sm text-gray-500 line-through">{result.originalPrice}</div>
                        {helpMode && (
                          <button className="mt-2 px-4 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 transition-colors">
                            Se tilbud
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Adaptive Tips */}
              {helpMode && (
                <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-6">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-2xl">💡</span>
                    <h4 className="font-medium text-blue-800">Smart tip!</h4>
                  </div>
                  <p className="text-blue-700 mb-4">
                    Arla Økologisk Mælk har den bedste pris lige nu. Den er 3 kr billigere end normalt!
                  </p>
                  <div className="flex space-x-3">
                    <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                      Gem til favoritter
                    </button>
                    <button className="px-4 py-2 bg-white text-blue-600 border border-blue-300 rounded hover:bg-blue-50 transition-colors">
                      Få besked ved næste tilbud
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
