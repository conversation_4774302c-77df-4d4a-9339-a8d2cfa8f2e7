"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth';
import { getCatalogs, BackendCatalog } from '@/lib/backend';

import Link from 'next/link';

export default function AdminCatalogsPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [catalogs, setCatalogs] = useState<BackendCatalog[]>([]);
  const [catalogsLoading, setCatalogsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redirect non-admin users
  useEffect(() => {
    if (!isLoading && user && !user.is_admin) {
      router.push('/');
    }
  }, [user, isLoading, router]);

  // Load catalogs
  useEffect(() => {
    if (user?.is_admin) {
      const loadCatalogs = async () => {
        try {
          setCatalogsLoading(true);
          const data = await getCatalogs();
          setCatalogs(data);
        } catch (err) {
          console.error('Failed to load catalogs:', err);
          setError('Kunne ikke indlæse kataloger');
        } finally {
          setCatalogsLoading(false);
        }
      };
      
      loadCatalogs();
    }
  }, [user]);

  // Handle delete catalog
  const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';

  const buildThumbnailUrl = (path?: string) => {
    if (!path) return undefined;
    return path.startsWith('http') ? path : `${API_BASE}/${path.replace(/^\\?/, '')}`;
  };

  const handleDelete = async (catalogId: number) => {
    if (!confirm('Er du sikker på, at du vil slette dette katalog?')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE}/catalog/${catalogId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Kunne ikke slette katalog');
      }

      // Remove the deleted catalog from state
      setCatalogs(catalogs.filter(catalog => catalog.id !== catalogId));
    } catch (err) {
      console.error('Error deleting catalog:', err);
      setError('Kunne ikke slette katalog');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[70vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Don't render admin content until we confirm the user is an admin
  if (!user || !user.is_admin) {
    return null;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Katalog Styring</h1>
        <Link 
          href="/admin/catalogs/upload" 
          className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          Upload Nyt Katalog
        </Link>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {catalogsLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
        </div>
      ) : catalogs.length === 0 ? (
        <div className="bg-white shadow overflow-hidden sm:rounded-md p-6 text-center">
          <p className="text-gray-500">Ingen kataloger fundet. Upload et nyt katalog for at komme i gang.</p>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Katalog</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Butik</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Udløbsdato</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sider</th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Handlinger</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {catalogs.map((catalog) => (
                <tr key={catalog.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {catalog.first_page_image_url && (
                        <div className="flex-shrink-0 h-10 w-10 mr-3">
                          <img className="h-10 w-10 object-cover rounded" src={catalog.first_page_image_url} alt="" />
                        </div>
                      )}
                      <div>
                        <div className="text-sm font-medium text-gray-900">{catalog.title}</div>
                        <div className="text-sm text-gray-500">ID: {catalog.id}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{catalog.store_name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{catalog.valid_to || 'N/A'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{catalog.pages ?? '-'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link href={`/admin/catalogs/${catalog.id}`} className="text-indigo-600 hover:text-indigo-900 mr-4">
                      Vis
                    </Link>
                    <button 
                      onClick={() => handleDelete(catalog.id)} 
                      className="text-red-600 hover:text-red-900"
                    >
                      Slet
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="mt-6">
        <Link href="/admin" className="text-indigo-600 hover:text-indigo-900 flex items-center gap-1">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          Tilbage til Admin Dashboard
        </Link>
      </div>
    </div>
  );
}
