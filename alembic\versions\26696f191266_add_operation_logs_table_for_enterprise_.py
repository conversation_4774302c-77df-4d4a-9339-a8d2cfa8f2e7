"""add_operation_logs_table_for_enterprise_pipeline_monitoring

Revision ID: 26696f191266
Revises: a1b2c3d4e5f6
Create Date: 2025-07-14 14:02:14.118342

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '26696f191266'
down_revision: Union[str, None] = 'a1b2c3d4e5f6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create operation_logs table for enterprise pipeline monitoring
    op.create_table(
        'operation_logs',
        sa.Column('id', sa.Integer(), nullable=False, primary_key=True, autoincrement=True),
        sa.Column('catalog_id', sa.Integer(), nullable=True),  # Nullable for system-wide operations
        sa.Column('store_name', sa.String(length=100), nullable=True),
        sa.Column('operation_type', sa.String(length=50), nullable=False),  # SCRAPE, PARSE, STATUS_UPDATE, RECONCILE
        sa.Column('operation_status', sa.String(length=50), nullable=False),  # STARTED, SUCCESS, FAILED, CANCELLED
        sa.Column('message', sa.Text(), nullable=True),
        sa.Column('error_details', sa.Text(), nullable=True),
        sa.Column('operation_metadata', sa.JSON(), nullable=True),  # Store additional context (product counts, timing, etc.)
        sa.Column('started_at', sa.DateTime(), nullable=False, server_default=sa.func.now()),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('duration_seconds', sa.Float(), nullable=True),
        sa.Column('triggered_by', sa.String(length=100), nullable=True),  # USER, CRON, AUTO_RECONCILE
        sa.ForeignKeyConstraint(['catalog_id'], ['catalogs.id'], ondelete='SET NULL'),
    )

    # Add indexes for efficient querying
    op.create_index('idx_operation_logs_catalog_id', 'operation_logs', ['catalog_id'])
    op.create_index('idx_operation_logs_operation_type', 'operation_logs', ['operation_type'])
    op.create_index('idx_operation_logs_started_at', 'operation_logs', ['started_at'])
    op.create_index('idx_operation_logs_status', 'operation_logs', ['operation_status'])


def downgrade() -> None:
    """Downgrade schema."""
    # Drop indexes first
    op.drop_index('idx_operation_logs_status', 'operation_logs')
    op.drop_index('idx_operation_logs_started_at', 'operation_logs')
    op.drop_index('idx_operation_logs_operation_type', 'operation_logs')
    op.drop_index('idx_operation_logs_catalog_id', 'operation_logs')

    # Drop the table
    op.drop_table('operation_logs')
