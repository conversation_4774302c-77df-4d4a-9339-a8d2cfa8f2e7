<!DOCTYPE html>
<html lang="da">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tilbud fra Danske Supermarkeder</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        body {
            padding-top: 2rem;
            padding-bottom: 2rem;
            background: linear-gradient(135deg, #f0f4ff 0%, #e6feff 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            line-height: 1.6;
            color: #334155;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            padding-bottom: 2.5rem;
            margin-bottom: 3.5rem;
            text-align: center;
            position: relative;
        }
        .header:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #4f46e5 0%, #38bdf8 100%);
            border-radius: 2px;
        }
        .header h1 {
            font-weight: 800;
            color: #1e293b;
            letter-spacing: -0.025em;
            margin-bottom: 1rem;
            font-size: 2.5rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        .header .lead {
            color: #64748b;
            font-size: 1.25rem;
            font-weight: 500;
            max-width: 600px;
            margin: 0 auto;
        }
        .settings-link {
            position: absolute;
            top: 0;
            right: 1rem;
            font-size: 0.9rem;
            font-weight: 500;
            color: #4f46e5;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }
        .settings-link:hover {
            color: #3730a3;
            transform: translateY(-2px);
        }
        .settings-link svg {
            width: 1.2rem;
            height: 1.2rem;
        }
        .section-container {
            padding: 2rem;
            margin-bottom: 3rem;
            border-radius: 1rem;
            box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.05);
            background: linear-gradient(15deg, #ffffff 0%, #f8faff 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
            backdrop-filter: blur(8px);
            transition: all 0.3s ease;
        }
        .section-container:hover {
            box-shadow: 0 20px 40px -15px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }
        .section-container h2 {
            color: #1e293b;
            font-weight: 700;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            position: relative;
            display: inline-block;
        }
        .section-container h2:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50%;
            height: 3px;
            background: linear-gradient(90deg, #4f46e5 0%, #38bdf8 100%);
            border-radius: 2px;
        }
        .upload-container {
            background: linear-gradient(15deg, #ffffff 0%, #f8faff 100%);
        }
        .query-container {
            background: linear-gradient(15deg, #ffffff 0%, #fafbff 100%);
        }
        .catalogs-container {
            background: linear-gradient(15deg, #ffffff 0%, #f8fdff 100%);
        }
        .catalog-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(226, 232, 240, 0.7);
            background: rgba(255, 255, 255, 0.85);
            border-radius: 0.85rem;
            overflow: hidden;
            margin-bottom: 1.5rem;
        }
        .catalog-card .card {
            border: none;
            background: transparent;
            height: 100%;
        }
        .catalog-card .card-body {
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .catalog-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.08);
            border-color: rgba(99, 102, 241, 0.2);
        }
        .btn-primary {
            background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #4338ca 0%, #2563eb 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px -1px rgba(59, 130, 246, 0.3);
        }
        .btn-primary:active {
            transform: translateY(0);
        }
        .btn-outline-primary {
            border-color: #3b82f6;
            color: #3b82f6;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-outline-primary:hover {
            background-color: #3b82f6;
            border-color: #3b82f6;
        }
        .btn-outline-secondary {
            border-color: #64748b;
            color: #64748b;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-outline-secondary:hover {
            background-color: #64748b;
            border-color: #64748b;
        }
        .btn-outline-danger {
            border-color: #ef4444;
            color: #ef4444;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-outline-danger:hover {
            background-color: #ef4444;
            border-color: #ef4444;
        }
        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 1px solid rgba(226, 232, 240, 0.8);
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.15);
        }
        /* Custom styling for the query input placeholder */
        #queryInput::placeholder {
            color: #adb5bd; /* A lighter grey color */
            /* Alternatively, for opacity: opacity: 0.6; */
        }
        .form-label {
            font-weight: 500;
            color: #475569;
            margin-bottom: 0.5rem;
        }
        #queryResult {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(8px);
            border-radius: 0.85rem;
            padding: 1.5rem;
            box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.05);
            margin-top: 1.5rem;
            border: 1px solid rgba(226, 232, 240, 0.8);
            transition: all 0.3s ease;
            font-size: 0.95rem;
            line-height: 1.7;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-10px); }
            to { opacity: 1; transform: translateX(0); }
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .loading {
            animation: fadeIn 0.3s ease;
            display: none;
            align-items: center;
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
        }
        .spinner-border {
            margin-right: 0.5rem;
            width: 1.2rem;
            height: 1.2rem;
        }
        .card-title {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .card-subtitle {
            color: #64748b;
            font-weight: 500;
            margin-bottom: 1rem;
        }
        .card-text {
            color: #475569;
            font-size: 0.9rem;
            margin-bottom: 1.25rem;
        }
        .card-img-top {
            border-radius: 0.5rem;
            border: 1px solid rgba(226, 232, 240, 0.5);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .catalog-card:hover .card-img-top {
            transform: scale(1.03);
        }
        /* Add accessibility improvements */
        .form-control:focus, .btn:focus, .form-select:focus {
            outline: none;
        }
        /* Custom range styling */
        .form-range::-webkit-slider-thumb {
            background: #4f46e5;
        }
        .form-range::-moz-range-thumb {
            background: #4f46e5;
        }
        /* Improve response visibility */
        .alert {
            border-radius: 0.75rem;
            border: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
        }
        .alert-light {
            background-color: rgba(255, 255, 255, 0.95);
            border-left: 4px solid #4f46e5;
        }
        .alert-danger {
            background-color: rgba(254, 242, 242, 0.95);
            border-left: 4px solid #ef4444;
        }
        /* Style for selected catalog cards */
        .catalog-card.selected {
            border-color: #4f46e5; /* Indigo border */
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.25); /* Subtle glow */
            background: linear-gradient(15deg, #f0f4ff 0%, #eef8ff 100%); /* Slightly different background */
        }

        /* --- Prompt Suggestion Gallery Styles --- */
        .prompt-gallery {
            margin-top: 1.5rem; /* Space below the query form */
            margin-bottom: 1.5rem; /* Space above query result */
            overflow-x: auto; /* Enable horizontal scrolling */
            white-space: nowrap; /* Keep items in a single line */
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
            padding-bottom: 1rem; /* Space for scrollbar if needed */
            scrollbar-width: thin; /* Firefox */
            scrollbar-color: #cbd5e1 #f1f5f9; /* Firefox scrollbar colors */
            position: relative; /* For potential absolute positioned arrows later */
        }
        /* Hide scrollbar */
        .prompt-gallery::-webkit-scrollbar {
            height: 6px;
        }
        .prompt-gallery::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .prompt-gallery::-webkit-scrollbar-thumb {
            background-color: #cbd5e1;
            border-radius: 3px;
            border: 1px solid #f1f5f9;
        }
        .prompt-card {
            display: inline-block; /* Arrange cards horizontally */
            background: linear-gradient(160deg, #ffffff 0%, #f9fafb 100%);
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1rem 1.25rem;
            margin-right: 1rem; /* Space between cards */
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1); /* Smooth transition for hover/click */
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.03), 0 2px 4px -2px rgba(0, 0, 0, 0.03);
            min-width: 200px; /* Minimum width */
            max-width: 300px; /* Maximum width */
            white-space: normal; /* Allow text wrapping */
            vertical-align: top; /* Align cards nicely */
        }
        .prompt-card:last-child {
            margin-right: 0; /* No margin for the last card */
        }
        .prompt-card:hover {
            transform: translateY(-4px) scale(1.02); /* Lift and slightly enlarge */
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -4px rgba(0, 0, 0, 0.07);
            border-color: #a5b4fc; /* Highlight border */
            background: linear-gradient(160deg, #f9fafb 0%, #f0f4ff 100%);
        }
        .prompt-card:active {
            transform: translateY(-1px) scale(1.01); /* Slight press down effect */
             box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.05);
        }
        .prompt-card p {
            font-size: 0.9rem;
            color: #4b5563; /* Slightly darker text */
            margin: 0;
            line-height: 1.5;
            /* Consider adding ellipsis for overflow if needed */
        }
        /* Optional: Add space for icons if used later */
        .prompt-card .icon {
            margin-bottom: 0.5rem;
            /* Style icons here */
        }
        /* Animation for gallery fade-in */
        @keyframes galleryFadeIn {
            from { opacity: 0; transform: translateY(15px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .prompt-gallery.loaded {
             animation: galleryFadeIn 0.6s ease-out forwards;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Find de Bedste Tilbud med AI</h1>
            <p class="lead">Udforsk tilbud fra danske supermarkeder. Spørg AI'en og find præcis hvad du søger.</p>
            {# Conditionally show Settings link only for admins #}
            {% if is_admin %}
            <a href="/settings" class="settings-link">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 1.255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-1.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Indstillinger
            </a>
            {% endif %}
            <!-- Auth Buttons / User Info -->
            <div id="auth-section" class="position-absolute top-0 start-0 mt-2 ms-2">
                <!-- This will be populated by JavaScript -->
            </div>
        </div>

        <!-- User Welcome Message -->
        <div id="welcome-message" class="alert alert-info mb-4" style="display: none;"></div>

        <!-- Modals for Login and Sign Up -->
        <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="loginModalLabel">Log Ind</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Luk"></button>
                    </div>
                    <div class="modal-body">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="loginUsername" class="form-label">Brugernavn</label>
                                <input type="text" class="form-control" id="loginUsername" required>
                            </div>
                            <div class="mb-3">
                                <label for="loginPassword" class="form-label">Adgangskode</label>
                                <input type="password" class="form-control" id="loginPassword" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Log Ind</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="signupModal" tabindex="-1" aria-labelledby="signupModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="signupModalLabel">Opret Bruger</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Luk"></button>
                    </div>
                    <div class="modal-body">
                        <form id="signupForm">
                            <div class="mb-3">
                                <label for="signupUsername" class="form-label">Brugernavn</label>
                                <input type="text" class="form-control" id="signupUsername" required>
                            </div>
                            <div class="mb-3">
                                <label for="signupPassword" class="form-label">Adgangskode</label>
                                <input type="password" class="form-control" id="signupPassword" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Opret Bruger</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Query section (always visible) -->
        <div class="query-container section-container">
            <h2>Spørg AI om tilbud</h2>
            <form id="queryForm">
                <div class="mb-3">
                    <label for="queryInput" class="form-label">Dit spørgsmål:</label>
                    <textarea class="form-control" id="queryInput" rows="3" placeholder="Stil et spørgsmål eller prøv et eksempel fra vores galleri nedenfor"></textarea>
                </div>
                {% if current_user and current_user.is_admin %}
                <div class="mb-3">
                    <label for="queryModelSelect" class="form-label">AI-model</label>
                    <select class="form-select" id="queryModelSelect">
                        {% for model_name in available_models %}
                        <option value="{{ model_name }}" {% if model_name == (current_user.preferred_model or default_query_model) %}selected{% endif %}>{{ model_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                {% endif %}
                <div class="mb-3">
                    <label class="form-label">Søg i katalog(er):</label>
                    <div id="catalogCheckboxes" style="border: 1px solid #dee2e6; border-radius: 0.5rem; padding: 10px;">
                        <!-- Buttons will be loaded here -->
                        <small class="text-muted">Indlæser kataloger...</small>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Spørg AI</button>
                <div class="loading" id="queryLoading">
                    <div class="spinner-border spinner-border-sm" role="status"></div>
                    <span>Behandler din forespørgsel...</span>
                </div>
            </form>

            <!-- Prompt Suggestion Gallery -->
            <div id="promptSuggestionGallery" class="prompt-gallery">
                <!-- Prompt cards will be injected here by JavaScript -->
            </div>
            <!-- End Prompt Suggestion Gallery -->

            <div id="queryResult" style="display: none;"></div>
            <!-- Query History Section -->
            <div id="queryHistorySection" class="mt-4" style="display: none;">
                <h5>Seneste forespørgsler</h5>
                <ul id="queryHistoryList" class="list-group list-group-flush">
                    <!-- History loaded here -->
                </ul>
            </div>
        </div>

        <!-- Ensure Upload section is conditional -->
        {% if is_admin %}
        <div class="upload-container section-container">
            <h2>Upload nyt tilbudskatalog</h2>
            <form id="uploadForm">
                <div class="mb-3">
                    <label for="file" class="form-label">Katalog-PDF</label>
                    <input type="file" class="form-control" id="file" name="file" accept=".pdf" required>
                </div>
                <div class="mb-3">
                    <label for="store_id" class="form-label">Butik</label>
                    <select class="form-select" id="store_id" name="store_id" required>
                        <option value="">Vælg butik</option>
                        {% for store in stores %}
                        <option value="{{ store.id }}">{{ store.name }}</option>
                        {% endfor %}
                        <option value="__add_new__">-- Tilføj ny butik --</option>
                    </select>
                </div>
                <div class="mb-3" id="new_store_name_container" style="display: none;">
                    <label for="new_store_name" class="form-label">Navn på ny butik</label>
                    <input type="text" class="form-control" id="new_store_name" name="new_store_name">
                </div>
                <div class="mb-3">
                    <label for="title" class="form-label">Titel (valgfri)</label>
                    <input type="text" class="form-control" id="title" name="title" placeholder="F.eks. Ugenstilbud">
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="valid_from" class="form-label">Gældende fra</label>
                        <input type="date" class="form-control" id="valid_from" name="valid_from">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="valid_to" class="form-label">Gældende til</label>
                        <input type="date" class="form-control" id="valid_to" name="valid_to">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="parsing_model" class="form-label">Parsing-model</label>
                    <select class="form-select" id="parsing_model" name="parsing_model">
                        <option value="">Brug standardindstilling</option>
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="skip_parsing" name="skip_parsing">
                    <label class="form-check-label" for="skip_parsing">Spring automatisk parsing over (parse senere)</label>
                </div>
                <button type="submit" class="btn btn-primary" id="uploadButton">
                   <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true" style="display:none;"></span>
                   Upload Katalog
                </button>
            </form>
            <div class="loading" id="uploadLoading">
                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                <span>Uploader katalog...</span>
            </div>
            <div id="uploadStatus"></div>
        </div>
        {% endif %}

        <!-- Catalog List section (Visible to all, controls are admin-only via JS) -->
        <div class="section-container catalogs-container" id="catalog-list-section">
            <h2>Uploadede tilbudskataloger</h2>
            <div id="catalogList" class="row">
                <!-- Catalog cards will be injected here by JavaScript -->
            </div>
        </div>

    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/script.js" defer></script>

    <!-- Add this near the end of the body, after the main container -->
    <div class="modal fade" id="imagePreviewModal" tabindex="-1" aria-labelledby="imagePreviewModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="imagePreviewModalLabel">Forhåndsvisning af katalogside</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Luk"></button>
          </div>
          <div class="modal-body text-center">
            <img src="" id="modalImageElement" class="img-fluid" alt="Billede af katalogside">
          </div>
        </div>
      </div>
    </div>
</body>
</html> 