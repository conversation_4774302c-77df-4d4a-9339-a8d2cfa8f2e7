#!/usr/bin/env python3
"""
End-to-end test of the cloud storage pipeline.
Tests: scraper → cloud upload → parser download → processing → storage
"""

import os
import sys
import logging
import tempfile
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from database import SessionLocal, Store, Catalog, CatalogPage, Product
    from cloud_storage import cloud_storage, upload_catalog_pdf
    from pdf_extractor import process_pdf_catalog_from_cloud
    from gemini_parser import process_catalog_page
    import config
except ImportError as e:
    print(f"❌ Missing required modules: {e}")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_cloud_storage_availability():
    """Test 1: Verify cloud storage is available and accessible"""
    
    print("🧪 Test 1: Cloud Storage Availability")
    print("-" * 40)
    
    if not cloud_storage.is_available():
        print("❌ FAIL: Cloud storage not available")
        return False
    
    try:
        # Test basic operations
        files = cloud_storage.list_files()
        print(f"✅ PASS: Cloud storage available with {len(files)} files")
        
        # Test bucket access
        test_file = "test/pipeline_test.txt"
        test_content = f"Pipeline test at {datetime.now().isoformat()}"
        
        # Upload test file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write(test_content)
            temp_path = f.name
        
        if cloud_storage.upload_file(temp_path, test_file):
            print("✅ PASS: Test file upload successful")
            
            # Download test file
            download_path = temp_path + "_download"
            if cloud_storage.download_file(test_file, download_path):
                print("✅ PASS: Test file download successful")
                
                # Verify content
                with open(download_path, 'r') as f:
                    downloaded_content = f.read()
                
                if downloaded_content == test_content:
                    print("✅ PASS: File content integrity verified")
                else:
                    print("❌ FAIL: File content mismatch")
                    return False
                
                # Cleanup
                os.remove(temp_path)
                os.remove(download_path)
                cloud_storage.delete_file(test_file)
                print("✅ PASS: Test cleanup successful")
                
            else:
                print("❌ FAIL: Test file download failed")
                return False
        else:
            print("❌ FAIL: Test file upload failed")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Cloud storage test error: {e}")
        return False

def test_pdf_upload_and_processing():
    """Test 2: Upload a PDF and process it through the cloud pipeline"""
    
    print("\n🧪 Test 2: PDF Upload and Processing")
    print("-" * 40)
    
    # Find an existing PDF to test with
    catalogs_dir = Path("./catalogs")
    if not catalogs_dir.exists():
        print("❌ FAIL: No local catalogs directory found for testing")
        return False
    
    pdf_files = list(catalogs_dir.glob("*.pdf"))
    if not pdf_files:
        print("❌ FAIL: No PDF files found for testing")
        return False
    
    test_pdf = pdf_files[0]
    print(f"📄 Using test PDF: {test_pdf.name}")
    
    try:
        # Upload PDF to cloud storage
        cloud_path = upload_catalog_pdf(
            str(test_pdf),
            "TestStore",
            "20250630",
            "20250706"
        )
        
        if not cloud_path:
            print("❌ FAIL: PDF upload to cloud storage failed")
            return False
        
        print(f"✅ PASS: PDF uploaded to cloud: {cloud_path}")
        
        # Verify file exists in cloud
        if not cloud_storage.file_exists(cloud_path):
            print("❌ FAIL: Uploaded PDF not found in cloud storage")
            return False
        
        print("✅ PASS: PDF verified in cloud storage")
        
        # Test download from cloud
        temp_download = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_download.close()
        
        if cloud_storage.download_file(cloud_path, temp_download.name):
            print("✅ PASS: PDF downloaded from cloud storage")
            
            # Verify file size
            original_size = test_pdf.stat().st_size
            downloaded_size = Path(temp_download.name).stat().st_size
            
            if abs(original_size - downloaded_size) < 100:  # Allow small differences
                print("✅ PASS: PDF file size integrity verified")
            else:
                print(f"❌ FAIL: File size mismatch (original: {original_size}, downloaded: {downloaded_size})")
                return False
            
            # Cleanup
            os.remove(temp_download.name)
            cloud_storage.delete_file(cloud_path)
            print("✅ PASS: Test PDF cleanup successful")
            
        else:
            print("❌ FAIL: PDF download from cloud storage failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: PDF processing test error: {e}")
        return False

def test_database_integration():
    """Test 3: Verify database stores cloud paths correctly"""
    
    print("\n🧪 Test 3: Database Integration")
    print("-" * 40)
    
    db = SessionLocal()
    try:
        # Check if we have any catalogs with cloud paths
        catalogs = db.query(Catalog).limit(5).all()
        
        if not catalogs:
            print("⚠️ SKIP: No catalogs found in database")
            return True
        
        cloud_path_count = 0
        local_path_count = 0
        
        for catalog in catalogs:
            if catalog.pdf_path:
                if catalog.pdf_path.startswith('catalogs/'):
                    cloud_path_count += 1
                    print(f"✅ Cloud path: {catalog.pdf_path}")
                else:
                    local_path_count += 1
                    print(f"⚠️ Local path: {catalog.pdf_path}")
        
        print(f"📊 Results: {cloud_path_count} cloud paths, {local_path_count} local paths")
        
        # Check catalog pages
        pages = db.query(CatalogPage).limit(5).all()
        page_cloud_count = 0
        page_local_count = 0
        
        for page in pages:
            if page.image_path:
                if page.image_path.startswith('images/'):
                    page_cloud_count += 1
                else:
                    page_local_count += 1
        
        print(f"📊 Page Results: {page_cloud_count} cloud paths, {page_local_count} local paths")
        
        if cloud_path_count > 0 or page_cloud_count > 0:
            print("✅ PASS: Database contains cloud storage paths")
            return True
        else:
            print("⚠️ WARNING: Database contains only local paths - migration may be needed")
            return True
            
    except Exception as e:
        print(f"❌ FAIL: Database integration test error: {e}")
        return False
    finally:
        db.close()

def test_api_endpoints():
    """Test 4: Verify API returns cloud storage URLs"""
    
    print("\n🧪 Test 4: API Endpoint URLs")
    print("-" * 40)
    
    try:
        # Test cloud storage URL generation
        from api import get_cloud_storage_url, get_logo_url
        
        # Test URL generation
        test_path = "logos/supermarkets/bilka.png"
        url = get_cloud_storage_url(test_path)
        expected_url = f"https://storage.googleapis.com/tilbudsjaegeren/{test_path}"
        
        if url == expected_url:
            print("✅ PASS: Cloud storage URL generation correct")
        else:
            print(f"❌ FAIL: URL mismatch. Expected: {expected_url}, Got: {url}")
            return False
        
        # Test logo URL generation
        logo_url = get_logo_url("Bilka")
        if logo_url.startswith("https://storage.googleapis.com/tilbudsjaegeren/"):
            print("✅ PASS: Logo URL generation correct")
        else:
            print(f"❌ FAIL: Invalid logo URL: {logo_url}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAIL: API endpoint test error: {e}")
        return False

def run_all_tests():
    """Run all pipeline tests"""
    
    print("🚀 Cloud Storage Pipeline End-to-End Test")
    print("=" * 60)
    
    tests = [
        ("Cloud Storage Availability", test_cloud_storage_availability),
        ("PDF Upload and Processing", test_pdf_upload_and_processing),
        ("Database Integration", test_database_integration),
        ("API Endpoint URLs", test_api_endpoints),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Cloud storage pipeline is working correctly.")
        print("\n✅ Ready for deployment!")
        return True
    else:
        print(f"⚠️ {total - passed} tests failed. Please review and fix issues.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    if not success:
        sys.exit(1)
