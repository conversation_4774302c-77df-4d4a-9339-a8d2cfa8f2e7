# File: scrapers/store_scrapers/netto_scraper.py

import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional

from ..scraper_core import BaseScraper, PlaywrightTimeoutError, PlaywrightError, Page


class NettoScraper(BaseScraper):
    """
    Scraper for Netto catalogs.
    - Navigates to netto-avisen page.
    - Extracts date from a main catalog display area.
    - Clicks this area to open an overlay.
    - Clicks a download button within the overlay.
    """

    def __init__(self, config: dict):
        super().__init__(config)
        self.logger.info(f"NettoScraper initialized for store: {self.store_name}")



    async def scrape_catalogs(self) -> List[Dict[str, Any]]:
        if not self.page or not self.context or not self.catalog_list_url:
            self.logger.error("Page, context, or catalog_list_url not available for NettoScraper.")
            return []

        # Set timeouts
        try:
            default_nav_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            default_el_timeout = self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 30000)
            if self.page:
                self.page.set_default_navigation_timeout(default_nav_timeout)
                self.page.set_default_timeout(default_el_timeout)
                self.logger.info(f"NettoScraper: Page timeouts set.")
        except Exception as e_timeout_set:
            self.logger.error(f"NettoScraper: Error setting default timeouts: {e_timeout_set}")
            return []

        # 1. Navigate to Netto /netto-avisen/ page
        self.logger.debug(f"Navigating to Netto /netto-avisen/ page: {self.catalog_list_url}.")
        nav_success = await self._navigate_to_url(self.page, self.catalog_list_url)
        if not nav_success:
            return []

        
        # 2. Handle Cookies
        cookie_selectors = self.config.get('selectors', {}).get('cookie_accept_selectors', [])
        if cookie_selectors:
            await self._handle_cookies(self.page, cookie_selectors)


        # 3. Find the main catalog tile/date container & extract date
        tile_date_container_selector = self.config.get('selectors', {}).get('catalog_tile_and_date_container_selector')
        if not tile_date_container_selector:
            self.logger.error("Netto: 'catalog_tile_and_date_container_selector' not configured.")
            return []

        self.logger.debug(f"Looking for catalog tile/date container with selector: '{tile_date_container_selector}'.")
        
        tile_date_container_locator = self.page.locator(tile_date_container_selector).first # This is a Locator
        
        raw_date_info = "Netto_Unknown_Date"
        display_title = f"{self.store_name} Catalog"

        try:
            await tile_date_container_locator.wait_for(state="visible", timeout=15000)
            
            date_in_tile_selector = self.config.get('selectors', {}).get('date_selector_within_tile')
            if date_in_tile_selector:
                # CORRECTED WAY TO FIND CHILD WITH LOCATOR API:
                date_locator = tile_date_container_locator.locator(date_in_tile_selector).first
                try:
                    await date_locator.wait_for(state="visible", timeout=5000) # Wait for child to be visible
                    text_content = await date_locator.text_content()
                    if text_content: raw_date_info = text_content.strip()
                    self.logger.info(f"Netto: Extracted raw_date_info from tile: '{raw_date_info}'")
                except PlaywrightTimeoutError:
                    self.logger.warning(f"Netto: Date element for selector '{date_in_tile_selector}' not visible/found within container.")
                except Exception as e_date_child:
                    self.logger.warning(f"Netto: Error getting text from date child locator '{date_in_tile_selector}': {e_date_child}")
            else:
                self.logger.info("Netto: 'date_selector_within_tile' not configured, attempting fallback.")
                container_text = await tile_date_container_locator.text_content(timeout=5000)
                if container_text: raw_date_info = container_text.strip().split('\n')[0] 
                self.logger.info(f"Netto: Extracted raw_date_info from container (fallback): '{raw_date_info}'")

            if "Unknown_Date" not in raw_date_info:
                 display_title = f"{self.store_name} ({raw_date_info.split('-')[0].strip()})"

        except PlaywrightTimeoutError:
            self.logger.warning(f"Netto: Timeout finding catalog tile/date container '{tile_date_container_selector}'. URL: {self.page.url}")
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "netto_no_tile_date_container.png"))
            return []
        except Exception as e:
            self.logger.error(f"Netto: Error finding or processing catalog tile/date container: {e}", exc_info=True)
            return []
        
        # 4. Click the tile/date container to open the overlay
        try:
            self.logger.info(f"Netto: Clicking catalog tile/date container to open overlay.")
            await tile_date_container_locator.click(timeout=10000)
            self.logger.info(f"Netto: Clicked. Waiting for overlay and download button...")

        except Exception as e:
            self.logger.error(f"Netto: Error clicking tile/date container: {e}", exc_info=True)
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "netto_tile_click_fail.png"))
            return []

        # 5. Click the download button within the overlay
        overlay_download_button_selector = self.config.get('selectors', {}).get('overlay_download_button_selector')
        if not overlay_download_button_selector:
            self.logger.error("Netto: 'overlay_download_button_selector' not configured.")
            return []

        download_url = None
        try:
            self.logger.info(f"Netto: Locating download button in overlay: '{overlay_download_button_selector}'")
            download_button_locator = self.page.locator(overlay_download_button_selector).first

            await download_button_locator.wait_for(state="visible", timeout=self.config.get('behavior_flags',{}).get('element_wait_timeout_ms', 20000))

            # First try the download event approach
            try:
                async with self.page.expect_download(timeout=10000) as download_info:  # Shorter timeout
                    await download_button_locator.click(timeout=5000)
                download = await download_info.value
                download_url = download.url
                self.logger.info(f"Netto: PDF Download URL captured via download event: {download_url}")

                # Cancel the download to prevent browser issues
                try:
                    await download.cancel()
                    self.logger.info("Netto: Download cancelled to prevent browser blocking")
                except Exception as cancel_error:
                    self.logger.warning(f"Netto: Could not cancel download: {cancel_error}")

            except PlaywrightTimeoutError:
                # Fallback: Check if clicking the button opens a popup/new tab
                self.logger.info("Netto: Download event timeout, trying popup approach...")

                try:
                    # Wait for a new page (popup/tab) to open
                    async with self.page.context.expect_page(timeout=10000) as popup_info:
                        await download_button_locator.click(timeout=5000)

                    popup_page = await popup_info.value
                    await popup_page.wait_for_load_state('load', timeout=15000)
                    popup_url = popup_page.url
                    self.logger.info(f"Netto: PDF URL captured via popup: {popup_url}")

                    # Validate it's a PDF URL
                    if 'amazonaws.com' in popup_url or popup_url.lower().endswith('.pdf'):
                        download_url = popup_url
                        # Close the popup
                        await popup_page.close()
                    else:
                        self.logger.warning(f"Netto: Popup URL doesn't look like a PDF: {popup_url}")
                        await popup_page.close()
                        return []

                except PlaywrightTimeoutError:
                    # Final fallback: Look for PDF links on the current page
                    self.logger.info("Netto: Popup timeout, checking for PDF links on page...")
                    pdf_links = await self.page.locator("a[href*='.pdf']").all()
                    if pdf_links:
                        for link in pdf_links:
                            href = await link.get_attribute('href')
                            if href and ('netto' in href.lower() or 'amazonaws.com' in href):
                                download_url = href
                                self.logger.info(f"Netto: Found PDF link on page: {download_url}")
                                break

                    if not download_url:
                        self.logger.error("Netto: Could not find PDF URL via any method")
                        return []

        except PlaywrightTimeoutError:
             self.logger.error(f"Netto: Timeout finding or clicking overlay download button '{overlay_download_button_selector}'.", exc_info=True)
             return []
        except Exception as e:
            self.logger.error(f"Netto: Error clicking overlay download button '{overlay_download_button_selector}' or during download: {e}", exc_info=True)
            return []

        if not download_url:
            self.logger.error("Netto: PDF download URL not obtained.")
            return []

        # 6. Download PDF manually using the URL
        # Use a simple filename - the cloud upload will handle proper date formatting
        file_title_part = self._sanitize_filename(raw_date_info.split('-')[0].strip() if "Unknown_Date" not in raw_date_info else display_title)
        suggested_filename = f"{file_title_part}.pdf"

        final_pdf_filename = self._sanitize_filename(suggested_filename)
        if not final_pdf_filename.lower().endswith(".pdf"): final_pdf_filename += ".pdf"

        download_dir = self._ensure_download_dir()
        local_pdf_path = download_dir / final_pdf_filename

        try:
            # Download PDF content manually using the captured URL
            self.logger.info(f"Netto: Downloading PDF content from: {download_url}")
            pdf_content = await self._download_file_content(download_url)
            if not pdf_content:
                self.logger.error(f"Netto: Failed to download PDF content from {download_url}")
                return []

            # Save PDF content to local file
            with open(local_pdf_path, 'wb') as f:
                f.write(pdf_content)
            self.logger.info(f"Netto: Successfully saved PDF to: {local_pdf_path}")

            # Create catalog data
            catalog_data = {
                "store_name": self.store_name, "title": display_title,
                "raw_date_info": raw_date_info, "pdf_url": download_url,
                "local_path": str(local_pdf_path)
            }

            # Upload to cloud storage
            cloud_path = self.upload_pdf_to_cloud(str(local_pdf_path), catalog_data)
            if cloud_path:
                catalog_data["cloud_pdf_path"] = cloud_path
                self.logger.info(f"Netto: PDF uploaded to cloud storage: {cloud_path}")
            else:
                self.logger.warning(f"Netto: Failed to upload PDF to cloud storage")
                return []

            self.collected_catalogs.append(catalog_data)

            # Clean up local file after cloud upload
            try:
                local_pdf_path.unlink()
                self.logger.debug(f"Netto: Cleaned up local PDF: {local_pdf_path}")
            except Exception as cleanup_error:
                self.logger.warning(f"Netto: Failed to clean up local PDF: {cleanup_error}")

        except Exception as e:
            self.logger.error(f"Netto: Error downloading/saving PDF from URL {download_url}: {e}", exc_info=True)
            return []

        return self.collected_catalogs