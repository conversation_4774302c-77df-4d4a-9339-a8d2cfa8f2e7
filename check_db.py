#!/usr/bin/env python
"""Checks the PostgreSQL database connection using the DATABASE_URL environment variable."""

import os
import sys
import psycopg2
from dotenv import load_dotenv

def check_connection():
    """Loads DATABASE_URL and attempts to connect to the PostgreSQL database."""
    print("Loading environment variables from .env file...")
    load_dotenv() # Load .env file if it exists

    db_url = os.getenv("DATABASE_URL")

    if not db_url:
        print("Error: DATABASE_URL environment variable not set.")
        print("Please ensure DATABASE_URL is defined in your .env file or system environment.")
        sys.exit(1) # Exit with error code

    if not db_url.startswith("postgresql://"):
        print(f"Error: DATABASE_URL does not appear to be a valid PostgreSQL URL.")
        print(f"Current value: {db_url}")
        print("Expected format: postgresql://user:password@host:port/dbname")
        sys.exit(1) # Exit with error code

    print(f"Attempting to connect using DATABASE_URL (credentials hidden)...")
    try:
        conn = psycopg2.connect(db_url)
        print("\n*** Successfully connected to PostgreSQL database! ***")
        conn.close()
        sys.exit(0) # Exit successfully
    except psycopg2.OperationalError as e:
        print(f"\n--- Failed to connect to PostgreSQL database. ---")
        # Get first line of error, avoiding detailed credential exposure
        error_msg = str(e).strip().split('\n')[0]
        print(f"Error Details: {error_msg}")
        print("\nPlease check:")
        print("  1. Is the PostgreSQL server running?")
        print("  2. Are the hostname, port, username, and password in DATABASE_URL correct?")
        print("  3. Is the database name correct?")
        print("  4. Are there any firewall restrictions?")
        sys.exit(1) # Exit with error code
    except Exception as e:
        print(f"\n--- An unexpected error occurred during connection attempt. ---")
        print(f"Error: {e}")
        sys.exit(1) # Exit with error code

if __name__ == "__main__":
    check_connection() 