# Use the official Playwright image which has browsers and dependencies pre-installed.
# This is the most reliable way to build a Playwright environment.
FROM mcr.microsoft.com/playwright/python:v1.52.0-jammy

# Set environment variables for best practices in Docker
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# Set the working directory inside the container
WORKDIR /app

# Copy only the requirements file to leverage Docker's layer caching
COPY requirements.txt .

# Install Python dependencies and Playwright browsers
# The base image already includes playwright, but we need to install browsers and our other dependencies.
RUN pip install --no-cache-dir -r requirements.txt \
    && playwright install --with-deps \
    && apt-get update -y \
    && apt-get install -y --no-install-recommends curl \
    && rm -rf /var/lib/apt/lists/*

# Copy the rest of the application's code into the image
COPY . .

# Copy and prepare the cron trigger script
COPY trigger.sh /app/trigger.sh
RUN sed -i 's/\r$//g' /app/trigger.sh && chmod +x /app/trigger.sh

# Copy the entrypoint script
COPY entrypoint.sh /usr/local/bin/

# Fix CRLF line endings that may be introduced by Git on Windows and make it executable.
# This is critical for the script to run correctly in a Linux environment.
RUN sed -i 's/\r$//g' /usr/local/bin/entrypoint.sh && chmod +x /usr/local/bin/entrypoint.sh

# Set the entrypoint script to be executed when the container starts
ENTRYPOINT ["entrypoint.sh"]

# Set the default command to 'web' for the web service
CMD ["web"]
