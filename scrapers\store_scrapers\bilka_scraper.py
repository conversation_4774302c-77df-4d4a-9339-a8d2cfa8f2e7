# File: scrapers/store_scrapers/bilka_scraper.py

import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional

from ..scraper_core import BaseScraper, PlaywrightTimeoutError, PlaywrightError, Page


class BilkaScraper(BaseScraper):
    """
    Scraper specifically for Bilka catalogs.
    Similar flow to Føtex: gets date and link from main page,
    then downloads PDF from a new tab viewer page.
    """

    def __init__(self, config: dict):
        super().__init__(config)
        self.logger.info(f"BilkaScraper initialized for store: {self.store_name}")

    async def scrape_catalogs(self) -> List[Dict[str, Any]]:
        if not self.page or not self.context or not self.catalog_list_url:
            self.logger.error("Page, context, or catalog_list_url not available for BilkaScraper.")
            return []

        # Set default timeouts on the page
        try:
            default_nav_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            default_el_timeout = self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 30000)
            if self.page:
                self.page.set_default_navigation_timeout(default_nav_timeout)
                self.page.set_default_timeout(default_el_timeout)
                self.logger.info(f"BilkaScraper: Page timeouts set - Nav: {default_nav_timeout}ms, Element: {default_el_timeout}ms.")
        except Exception as e_timeout_set:
            self.logger.error(f"BilkaScraper: Error setting default timeouts: {e_timeout_set}")
            return []

        # 1. Navigate to the initial Bilka catalog page
        self.logger.debug(f"Navigating to Bilka start page: {self.catalog_list_url}. Page closed: {self.page.is_closed()}")
        nav_success = await self._navigate_to_url(self.page, self.catalog_list_url)
        if not nav_success:
            self.logger.error(f"Failed to navigate to Bilka start page: {self.catalog_list_url}")
            return []

        self.logger.debug(f"After navigation to start page. Page closed: {self.page.is_closed()}")

        # 2. Handle Cookies
        cookie_selectors = self.config.get('selectors', {}).get('cookie_accept_selectors', [])
        if cookie_selectors:
            await self._handle_cookies(self.page, cookie_selectors,
                                       timeout_ms=self.config.get('behavior_flags', {}).get('cookie_consent_timeout_ms', 15000))
        else:
            self.logger.info("No cookie selectors configured for Bilka.")


        # 3. Grab Date from the main page AND identify the link element
        raw_date_info = f"Bilka_Unknown_Date_{Path(self.page.url).name}"
        catalog_link_element_selector = self.config.get('selectors', {}).get('date_and_catalog_link_element')
        
        if not catalog_link_element_selector:
            self.logger.error("Bilka: 'date_and_catalog_link_element' selector not configured.")
            return []

        catalog_link_el = None
        try:
            # Bilka might have multiple catalog tiles. We'll assume the first one is the main one.
            potential_elements = await self.page.query_selector_all(catalog_link_element_selector)
            if not potential_elements:
                self.logger.warning(f"Bilka: No elements found with selector '{catalog_link_element_selector}'.")
                await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "bilka_no_date_link_element.png"))
                return []

            catalog_link_el = self.page.locator(catalog_link_element_selector).first
            await catalog_link_el.wait_for(state="visible", timeout=10000)

            # Use .evaluate to get the text content, as it seems to handle encoding better than text_content()
            text_content = await catalog_link_el.evaluate('(element) => element.textContent')

            if text_content:
                raw_date_info = text_content.strip()
                self.logger.info(f"Bilka: Extracted raw_date_info from link element: '{raw_date_info}'")
            else:
                self.logger.warning(f"Bilka: Link element '{catalog_link_element_selector}' found but had no text for date.")

        except PlaywrightTimeoutError:
            self.logger.warning(f"Bilka: Timeout finding link/date element '{catalog_link_element_selector}'.")
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "bilka_timeout_date_link_element.png"))
            return []
        except Exception as e:
            self.logger.warning(f"Bilka: Error extracting date/link from element: {e}", exc_info=True)
            return []
        
        display_title = f"{self.store_name} Catalog ({raw_date_info.split('-')[0].strip()})" if "Unknown_Date" not in raw_date_info else f"{self.store_name} Catalog"


        # 4. Click the date/catalog link element, which opens a new tab
        viewer_page: Optional[Page] = None
        if not catalog_link_el:
            self.logger.error("Bilka: Catalog link element not identified. Cannot proceed.")
            return []
            
        try:
            self.logger.info(f"Bilka: Attempting to click catalog link element: '{catalog_link_element_selector}'")
            
            async with self.context.expect_page(timeout=self.config.get('behavior_flags',{}).get('new_page_timeout_ms', 30000)) as new_page_info:
                await catalog_link_el.click(timeout=10000) # Increased click timeout slightly
            
            viewer_page = await new_page_info.value
            await viewer_page.wait_for_load_state('domcontentloaded', timeout=self.config.get('behavior_flags',{}).get('navigation_timeout_ms', 60000))
            self.logger.info(f"Bilka: New viewer page opened. URL: {viewer_page.url}")


        except PlaywrightTimeoutError:
            self.logger.error(f"Bilka: Timeout waiting for catalog link click or for new page to open.", exc_info=True)
            await self.page.screenshot(path=str(self._ensure_download_dir() / "debug" / "bilka_click_new_page_timeout.png"))
            return []
        except Exception as e:
            self.logger.error(f"Bilka: Error clicking catalog link or handling new page: {e}", exc_info=True)
            return []

        if not viewer_page or viewer_page.is_closed():
            self.logger.error("Bilka: Viewer page was not opened or is closed.")
            return []

        # 5. On the new viewer page, click the download icon (same logic as Føtex)
        pdf_download_button_selector = self.config.get('selectors', {}).get('pdf_download_button_on_viewer_page')
        if not pdf_download_button_selector: # Should be "#modDownloadPdfBtn"
            self.logger.error("Bilka Viewer: 'pdf_download_button_on_viewer_page' selector not configured.")
            await viewer_page.close()
            return []

        download: Optional[Any] = None
        try:
            self.logger.info(f"Bilka Viewer: Looking for download trigger using selector: '{pdf_download_button_selector}'")
            download_trigger = viewer_page.locator(pdf_download_button_selector).first
            
            await download_trigger.wait_for(state="visible", timeout=self.config.get('behavior_flags',{}).get('element_wait_timeout_ms', 25000))
 

            async with viewer_page.expect_download(timeout=self.config.get('behavior_flags',{}).get('download_event_timeout_ms', 60000)) as download_info:
                await download_trigger.click(timeout=10000)
            download = await download_info.value
            download_url = download.url
            self.logger.info(f"Bilka Viewer: PDF Download event triggered using selector '{pdf_download_button_selector}'. URL: {download_url}")

            # Cancel the download to prevent browser issues
            try:
                await download.cancel()
                self.logger.info("Bilka: Download cancelled to prevent browser blocking")
            except Exception as cancel_error:
                self.logger.warning(f"Bilka: Could not cancel download: {cancel_error}")

        except PlaywrightTimeoutError:
            self.logger.error(f"Bilka Viewer: Timeout finding/clicking PDF download trigger ('{pdf_download_button_selector}'), or for download. URL: {viewer_page.url}", exc_info=True)
            debug_dir = self._ensure_download_dir() / "debug"
            debug_dir.mkdir(exist_ok=True, parents=True)
            await viewer_page.screenshot(path=str(debug_dir / "bilka_viewer_download_timeout.png"))
            await viewer_page.close()
            return []
        except Exception as e:
            self.logger.error(f"Bilka Viewer: Error interacting with PDF download trigger ('{pdf_download_button_selector}') or during download: {e}. URL: {viewer_page.url}", exc_info=True)
            debug_dir = self._ensure_download_dir() / "debug"
            debug_dir.mkdir(exist_ok=True, parents=True)
            await viewer_page.screenshot(path=str(debug_dir / "bilka_viewer_download_exception.png"))
            await viewer_page.close()
            return []

        if not download:
            self.logger.error("Bilka: PDF download object not obtained.")
            await viewer_page.close()
            return []
            
        # 6. Save the downloaded PDF
        download_url = download.url
        suggested_filename = download.suggested_filename or f"{self._sanitize_filename(display_title)}.pdf"
        
        final_pdf_filename = self._sanitize_filename(suggested_filename)
        if not final_pdf_filename.lower().endswith(".pdf"):
            final_pdf_filename += ".pdf"

        download_dir = self._ensure_download_dir()
        local_pdf_path = download_dir / final_pdf_filename

        try:
            # Download PDF content manually using the captured URL
            self.logger.info(f"Bilka: Downloading PDF content from: {download_url}")
            pdf_content = await self._download_file_content(download_url)
            if not pdf_content:
                self.logger.error(f"Bilka: Failed to download PDF content from {download_url}")
                await viewer_page.close()
                return []

            # Save PDF content to local file
            with open(local_pdf_path, 'wb') as f:
                f.write(pdf_content)
            self.logger.info(f"Bilka: Successfully saved PDF to: {local_pdf_path}")

            # Create initial catalog data
            catalog_data = {
                "store_name": self.store_name,
                "title": display_title,
                "raw_date_info": raw_date_info,
                "pdf_url": download_url,
                "local_path": str(local_pdf_path)
            }

            # Upload to cloud storage
            cloud_path = self.upload_pdf_to_cloud(str(local_pdf_path), catalog_data)
            if cloud_path:
                catalog_data["cloud_pdf_path"] = cloud_path
                self.logger.info(f"Bilka: PDF uploaded to cloud storage: {cloud_path}")
            else:
                self.logger.warning(f"Bilka: Failed to upload PDF to cloud storage")

            self.collected_catalogs.append(catalog_data)

            # Clean up local file after cloud upload
            try:
                local_pdf_path.unlink()
                self.logger.debug(f"Bilka: Cleaned up local PDF: {local_pdf_path}")
            except Exception as cleanup_error:
                self.logger.warning(f"Bilka: Failed to clean up local PDF: {cleanup_error}")
            
        except Exception as e:
            self.logger.error(f"Bilka: Error saving downloaded PDF: {e}", exc_info=True)
        finally:
            if viewer_page and not viewer_page.is_closed():
                await viewer_page.close()
                self.logger.debug("Bilka: Viewer page closed.")

        return self.collected_catalogs