"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth';
import Link from 'next/link';
import { getCatalogDetails, BackendCatalog } from '@/lib/backend';

export default function CatalogDetailPage({ params }: { params: Promise<{ catalogId: string }> }) {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [catalog, setCatalog] = useState<BackendCatalog | null>(null);
  const [catalogLoading, setCatalogLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [catalogId, setCatalogId] = useState<number | null>(null);

  // Handle async params
  useEffect(() => {
    params.then(resolvedParams => {
      setCatalogId(parseInt(resolvedParams.catalogId, 10));
    });
  }, [params]);

  // Redirect non-admin users
  useEffect(() => {
    if (!isLoading && user && !user.is_admin) {
      router.push('/');
    }
  }, [user, isLoading, router]);

  // Load catalog details
  useEffect(() => {
    if (user?.is_admin && catalogId !== null) {
      const loadCatalogDetails = async () => {
        try {
          setCatalogLoading(true);
          const data = await getCatalogDetails(catalogId);
          setCatalog(data);
        } catch (err) {
          console.error('Failed to load catalog details:', err);
          setError('Kunne ikke indlæse katalog detaljer');
        } finally {
          setCatalogLoading(false);
        }
      };
      
      loadCatalogDetails();
    }
  }, [user, catalogId]);

  const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';

  const buildImageUrl = (path?: string) => {
    if (!path) return '/placeholder.png';
    return path.startsWith('http') ? path : `${API_BASE}/${path.replace(/^\\?/, '')}`;
  };

  if (isLoading || catalogLoading) {
    return (
      <div className="flex justify-center items-center min-h-[70vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!user || !user.is_admin) {
    return null; // or a redirect
  }

  if (error) {
    return <p className="text-center py-10 text-red-500">{error}</p>;
  }

  if (!catalog) {
    return <p className="text-center py-10">Katalog ikke fundet.</p>;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
      <div className="mb-8">
        <Link href="/admin/catalogs" className="text-indigo-600 hover:text-indigo-900 flex items-center gap-1 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          Tilbage til Katalog Styring
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">{catalog.title}</h1>
        <p className="mt-2 text-gray-600">Butik: {catalog.store_name} | Gyldig til: {catalog.valid_to || 'N/A'}</p>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {catalog.pages_image_paths?.map((path, index) => (
          <div key={index} className="bg-white shadow rounded-lg overflow-hidden">
            <img src={buildImageUrl(path)} alt={`Side ${index + 1}`} className="w-full h-auto object-cover" />
            <div className="p-2 text-center text-sm text-gray-600">
              Side {index + 1}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
