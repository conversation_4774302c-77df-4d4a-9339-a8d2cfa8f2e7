from google import genai
from google.genai import types
import os
import logging
import json
import base64
from PIL import Image, UnidentifiedImageError
from io import BytesIO
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from database import Product, CatalogPage, SessionLocal
import time
from google.api_core import exceptions as google_exceptions
import re
import config
# APP_CONFIG import removed - using database-first config
from utils import parse_and_calculate_unit_price
from cloud_storage import cloud_storage


# --- Custom Exception ---


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)



# Removed is_gemini_available() check - rely on external configuration

# --- Custom Exceptions ---
class ModelRequiresBillingError(Exception):
    """Custom exception for models requiring billing/paid tier."""
    pass

class DailyLimitExceededError(Exception):
    """Custom exception for hitting the daily API quota."""
    pass

# Define the default prompt as a constant
DEFAULT_PRODUCT_EXTRACTION_PROMPT = """
You are a professional product data extractor specialized in Danish supermarket catalogs.
Extract all product information from this catalog page.

For each product visible, extract the following information in Danish:
- Name: The name of the product
- Description: Any additional description of the product
- Price: The current price (use decimal point for separator, e.g. 24.95)
- Original price: The original price before discount (if shown)
- Unit: The unit of measurement (e.g., kg, stk, pakke)
- Category: General product category (e.g., kød, mejeri, frugt, grønt)
- Brand: The brand or manufacturer name (if visible)

Return the data as a valid JSON array, where each object represents one product.
Example format:
[
  {
    "name": "Hakket Oksekød",
    "description": "8-12% fedt",
    "price": 39.95,
    "original_price": 59.95,
    "unit": "500g",
    "category": "kød",
    "brand": "SuperBrugsen Egne Varer"
  },
  ...
]

Make the best attempt to extract each field. If a field is not visible or applicable, use null.
The JSON must be valid and properly formatted.
"""

def encode_file(file_path):
    """Encode a file to base64 for API transmission."""
    with open(file_path, "rb") as file:
        return base64.b64encode(file.read()).decode('utf-8')

def parse_file_with_gemini(file_path: str, model_name: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Send an image file (using its FULL path) to Gemini API and extract product information
    
    Args:
        file_path: FULL path to the image file
        model_name: Specific Gemini model name to use (optional)
                    If not provided, uses the value from database settings or default.
        
    Returns:
        List of product dictionaries
    """
    # Ensure file path is not empty and exists before proceeding
    if not file_path or not os.path.exists(file_path):
        logger.error(f"File path is invalid or file does not exist for Gemini parsing: {file_path}")
        return []
        
    logger.info(f"Analyzing file with Gemini: {os.path.basename(file_path)}") # Log basename for brevity
    
    # --- Get Settings from Database ---
    # Use passed model_name, then database setting, then fallback
    effective_model_name = model_name or config.get_setting_from_db('GEMINI_PARSING_MODEL', 'models/gemini-1.5-flash-latest')
    # Use database for prompt, then fallback constant
    parser_prompt = config.get_setting_from_db('GEMINI_PARSER_SYSTEM_PROMPT', DEFAULT_PRODUCT_EXTRACTION_PROMPT)
    # Use database for max retries, with a fallback, ensuring it's an integer
    max_retries = config.get_int_setting_from_db('PARSER_MAX_RETRIES', 3)
    # --- End Get Settings ---

    attempt = 0
    while attempt < max_retries:
        attempt += 1
        try:
            # Simple proactive rate limiting: wait before every single API call.
            time.sleep(config.get_int_setting_from_db('GEMINI_API_DELAY_SECONDS', 2))

            # Initialize client with new unified SDK
            client = genai.Client(api_key=os.getenv("GOOGLE_API_KEY"))

            if attempt == 1:
                logger.info(f"Using parsing model: {effective_model_name} for {os.path.basename(file_path)}")

            img = Image.open(file_path)

            # Get generation parameters from database
            temperature = config.get_float_setting_from_db("GEMINI_TEMPERATURE", 0.7)
            top_p = config.get_float_setting_from_db("GEMINI_TOP_P", 0.95)
            top_k = config.get_int_setting_from_db("GEMINI_TOP_K", 20)
            max_output_tokens = config.get_int_setting_from_db("GEMINI_MAX_OUTPUT_TOKENS", 2048)
            candidate_count = config.get_int_setting_from_db("GEMINI_CANDIDATE_COUNT", 1)
            seed = config.get_setting_from_db("GEMINI_SEED", "")
            stop_sequences_str = config.get_setting_from_db("GEMINI_STOP_SEQUENCES", "[]")
            presence_penalty = config.get_float_setting_from_db("GEMINI_PRESENCE_PENALTY", 0.0)
            frequency_penalty = config.get_float_setting_from_db("GEMINI_FREQUENCY_PENALTY", 0.0)

            # Parse stop sequences from JSON string
            try:
                import json
                stop_sequences = json.loads(stop_sequences_str) if stop_sequences_str else []
            except:
                stop_sequences = []

            # Get safety settings from database
            safety_settings = [
                types.SafetySetting(
                    category="HARM_CATEGORY_HATE_SPEECH",
                    threshold=config.get_setting_from_db("GEMINI_SAFETY_HATE_SPEECH", "BLOCK_ONLY_HIGH")
                ),
                types.SafetySetting(
                    category="HARM_CATEGORY_DANGEROUS_CONTENT",
                    threshold=config.get_setting_from_db("GEMINI_SAFETY_DANGEROUS_CONTENT", "BLOCK_ONLY_HIGH")
                ),
                types.SafetySetting(
                    category="HARM_CATEGORY_HARASSMENT",
                    threshold=config.get_setting_from_db("GEMINI_SAFETY_HARASSMENT", "BLOCK_ONLY_HIGH")
                ),
                types.SafetySetting(
                    category="HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    threshold=config.get_setting_from_db("GEMINI_SAFETY_SEXUALLY_EXPLICIT", "BLOCK_ONLY_HIGH")
                )
            ]

            # Prepare content with new API structure
            # Convert PIL Image to bytes for the new GenAI SDK
            import io
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='PNG')
            img_bytes = img_bytes.getvalue()

            contents = [
                types.Content(
                    role='user',
                    parts=[
                        types.Part.from_text(text=parser_prompt),
                        types.Part.from_bytes(data=img_bytes, mime_type='image/png')
                    ]
                )
            ]

            # Build generation config
            generation_config = types.GenerateContentConfig(
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                max_output_tokens=max_output_tokens,
                candidate_count=candidate_count,
                presence_penalty=presence_penalty,
                frequency_penalty=frequency_penalty,
                safety_settings=safety_settings
            )

            # Add seed if provided
            if seed and seed.strip():
                generation_config.seed = int(seed)

            # Add stop sequences if provided
            if stop_sequences:
                generation_config.stop_sequences = stop_sequences

            # Generate content with new API
            response = client.models.generate_content(
                model=effective_model_name,
                contents=contents,
                config=generation_config
            )

            response_text = response.text # Assuming response.text contains the JSON string or error
            
            # Extract JSON
            try:
                start_idx = response_text.find('[')
                end_idx = response_text.rfind(']') + 1
                if start_idx != -1 and end_idx != -1:
                    json_text = response_text[start_idx:end_idx]
                    products = json.loads(json_text)
                    logger.info(f"Successfully extracted {len(products)} products from {os.path.basename(file_path)} on attempt {attempt}")
                    return products
                else:
                    logger.error(f"No JSON array found in response from {os.path.basename(file_path)} (Attempt {attempt}): {response_text[:500]}...")
                    # Don't retry on bad response format, return empty
                    return [] 
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON from {os.path.basename(file_path)} (Attempt {attempt}): {e}. Response: {response_text[:500]}...")
                # Don't retry on bad response format, return empty
                return []
            
        except google_exceptions.ResourceExhausted as e: 
            error_message = str(e)
            # Check for specific error messages to raise targeted exceptions
            if "doesn't have a free quota tier" in error_message:
                log_msg = f"Billing required for model '{effective_model_name}'."
                logger.error(log_msg)
                raise ModelRequiresBillingError(log_msg)
            
            if "quota" in error_message.lower():
                log_msg = f"Daily quota likely exceeded. Stopping worker. Error: {error_message}"
                logger.error(log_msg)
                raise DailyLimitExceededError(log_msg)

            # General rate limit handling (e.g., requests per minute)
            logger.warning(f"Rate limit hit for {os.path.basename(file_path)} (Attempt {attempt}/{max_retries}). Error: {error_message}")
            if attempt < max_retries:
                # Simple exponential backoff
                wait_time = 5 * (2 ** attempt) 
                logger.info(f"Retrying {os.path.basename(file_path)} in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                logger.error(f"Max retries ({max_retries}) reached for {os.path.basename(file_path)} due to rate limits. Giving up.")
                return []
        
        except UnidentifiedImageError:
             logger.error(f"Cannot identify image file (PIL Error): {file_path}")
             return [] # Cannot process, don't retry
        except FileNotFoundError:
             logger.error(f"Image file not found during parsing attempt {attempt}: {file_path}")
             return [] # Cannot process, don't retry
        except Exception as e:
            logger.error(f"Unexpected error analyzing file {os.path.basename(file_path)} with Gemini (Attempt {attempt}): {e}", exc_info=True)
            # Don't retry on unexpected errors
            return [] 

    return [] # Should only be reached if max_retries is 0 or loop fails

def process_catalog_page(page_id: int, model_name: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Processes a single catalog page: retrieves path from DB, gets full path, parses with Gemini, stores products.
    Returns the raw list of product dictionaries extracted by Gemini (or empty list on failure).

    This function manages its own database session lifecycle to prevent connection pool exhaustion.
    """
    # Create a new database session for this page processing
    db = SessionLocal()
    products_data = [] # Initialize as empty list
    try:
        page = db.query(CatalogPage).filter(CatalogPage.id == page_id).first()
        if not page:
            logger.error(f"Catalog page with ID {page_id} not found")
            return []
        
        # --- Download image from cloud storage ---
        cloud_image_path = page.image_path  # This is now a cloud storage path
        if not cloud_image_path:
             logger.error(f"Image path is missing for CatalogPage ID {page_id}")
             return []

        # Download image from cloud storage to temporary location
        temp_image_path = cloud_storage.get_temp_download_path(cloud_image_path)
        if not cloud_storage.download_file(cloud_image_path, temp_image_path):
             logger.error(f"Failed to download image from cloud storage: {cloud_image_path} (Page ID: {page_id})")
             return []

        logger.info(f"Processing Page ID {page_id}: Downloaded from cloud '{cloud_image_path}' -> Temp '{temp_image_path}'")
        # --- End path conversion ---

        # File existence check is now inside parse_file_with_gemini
        # if not os.path.exists(full_image_path): ... (removed)

        # --- Determine model name ---
        # Use provided, then database setting, then fallback
        effective_model_name = model_name or config.get_setting_from_db('GEMINI_PARSING_MODEL', 'models/gemini-1.5-flash-latest')
        # --- End determine model name ---

        # Parse the file using the temporary downloaded path and effective model name
        products_data = parse_file_with_gemini(temp_image_path, model_name=effective_model_name)

        if products_data: # Only proceed if parsing returned products
            products_to_add = []
            for product_info in products_data:
                if not isinstance(product_info, dict) or not product_info.get('name') or product_info.get('price') is None: # Price is required
                     logger.warning(f"Skipping product data with missing name/price from page {page_id}: {product_info}")
                     continue
                 
                # Validate/Convert price 
                price_val = None
                try:
                    price_str = str(product_info['price']).replace(',', '.') # Handle comma decimal separator
                    price_val = float(price_str)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid price format '{product_info.get('price')}' for product '{product_info.get('name')}' on page {page_id}. Setting price to None.")
                    # Skip product if price is invalid?
                    continue
                
                # Validate/Convert original_price
                original_price_val = None
                if product_info.get('original_price') is not None:
                    try:
                        orig_price_str = str(product_info['original_price']).replace(',', '.')
                        original_price_val = float(orig_price_str)
                    except (ValueError, TypeError):
                         logger.warning(f"Invalid original_price format '{product_info.get('original_price')}' for product '{product_info.get('name')}' on page {page_id}. Setting original_price to None.")

                # --- Parse unit and calculate unit price --- 
                unit_details = parse_and_calculate_unit_price(
                    unit_string=product_info.get('unit'), 
                    price=price_val
                )
                # --- End unit parsing ---

                product = Product(
                    catalog_id=page.catalog_id,
                    page_number=page.page_number,
                    image_path=page.image_path, # Make sure we save the page image path here too
                    name=product_info.get('name'),
                    description=product_info.get('description'),
                    price=price_val, # Use validated float
                    original_price=original_price_val, # Use validated float or None
                    unit=product_info.get('unit'), # Store raw unit string
                    category=product_info.get('category'),
                    brand=product_info.get('brand'), # <-- Add brand
                    
                    # Add parsed unit details
                    quantity=unit_details['quantity'],
                    unit_type=unit_details['unit_type'],
                    price_per_base_unit=unit_details['price_per_base_unit'],
                    base_unit_type=unit_details['base_unit_type']
                )
                products_to_add.append(product)

            if products_to_add:
                try:
                    db.add_all(products_to_add)
                    db.commit()
                    logger.info(f"Added {len(products_to_add)} products from page ID {page_id} to database.")
                except Exception as db_err:
                     logger.error(f"Database error saving products for page ID {page_id}: {db_err}", exc_info=True)
                     db.rollback()
                     # Should we clear products_data if save fails?
                     # For now, we still return what Gemini extracted.
            else:
                 logger.warning(f"No valid products extracted or added from page ID {page_id}.")
        else:
             logger.info(f"No products extracted by Gemini from page ID {page_id} (Cloud Path: {cloud_image_path}).")
             
        # Clean up temporary file
        try:
            os.remove(temp_image_path)
            logger.debug(f"Cleaned up temporary image file: {temp_image_path}")
        except Exception as cleanup_error:
            logger.warning(f"Failed to clean up temporary image file: {cleanup_error}")

        # Return the raw extracted data (or empty list)
        return products_data
    
    except ModelRequiresBillingError as billing_error:
        logger.error(f"Stopping parsing for catalog due to billing error on page {page_id}: {billing_error}")
        raise # Re-raise the specific error to stop process_catalog_pages
    except Exception as e:
        logger.error(f"Error processing catalog page {page_id}: {e}", exc_info=True)
        if db: db.rollback() # Ensure rollback on unexpected error
        return [] # Return empty list on error
    finally:
        # Always close the database session to prevent connection pool exhaustion
        db.close()

def process_catalog_pages(catalog_id: int, model_name: Optional[str] = None) -> int:
    """
    Orchestrates the processing of all pages in a catalog. Each page is processed with its own database session.
    Returns the final count of products associated with the catalog in the DB.

    This function no longer holds a long-lived database session to prevent connection pool exhaustion.
    """
    # Get initial count and page information with a short-lived session
    db = SessionLocal()
    try:
        initial_product_count = db.query(Product).filter(Product.catalog_id == catalog_id).count()
        pages = db.query(CatalogPage).filter(CatalogPage.catalog_id == catalog_id).order_by(CatalogPage.page_number).all()
        page_ids = [page.id for page in pages]
        page_count = len(page_ids)
    finally:
        db.close()

    if not page_ids:
        logger.warning(f"No pages found for catalog ID {catalog_id} to process.")
        return initial_product_count # Return initial count (likely 0)

    # --- Determine model name ---
    # Use provided, then database setting, then fallback
    effective_model_name = model_name or config.get_setting_from_db('GEMINI_PARSING_MODEL', 'models/gemini-1.5-flash-latest')
    # --- End determine model name ---

    logger.info(f"Starting to process {page_count} pages for catalog ID {catalog_id} (initial products: {initial_product_count}) using model '{effective_model_name}'...")

    # Process each page with its own database session
    for i, page_id in enumerate(page_ids, 1):
        try:
            # Each page gets its own database session via process_catalog_page
            process_catalog_page(page_id, model_name=effective_model_name)
            # Logging is handled within process_catalog_page
            logger.info(f"Completed processing page {i}/{page_count} (ID: {page_id}) for catalog {catalog_id}.")
        except ModelRequiresBillingError:
            logger.error(f"Stopping processing for catalog {catalog_id} due to model billing requirement on page {page_id}.")
            break # Exit the loop for this catalog
        except Exception as page_error: # Catch unexpected errors from process_catalog_page
            logger.error(f"Unexpected error processing page ID {page_id} for catalog {catalog_id}: {page_error}", exc_info=True)
            # Continue to the next page
            continue

    # Get final count with a new short-lived session
    db = SessionLocal()
    try:
        final_product_count = db.query(Product).filter(Product.catalog_id == catalog_id).count()
    finally:
        db.close()

    logger.info(f"Finished processing pages for catalog {catalog_id}. Final product count in DB: {final_product_count}")
    return final_product_count # Return the actual count from DB

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: python gemini_parser.py <catalog_id>")
        sys.exit(1)
    
    catalog_id = int(sys.argv[1])
    total_products = process_catalog_pages(catalog_id)
    
    print(f"Processed catalog {catalog_id}, final product count in DB: {total_products}")