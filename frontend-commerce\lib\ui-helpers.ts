const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';
const CLOUD_STORAGE_BASE = 'https://storage.googleapis.com/tilbudsjaegeren';

// Function to build proper thumbnail URL from a catalog's image path
// Note: This handles legacy path formats since StoragePaths is backend-only
export const buildThumbnailUrl = (path?: string) => {
  if (!path) return undefined;

  // If it's already a full URL, return as-is
  if (path.startsWith('http')) {
    return path;
  }

  // Normalize path separators
  let normalized = path.replace(/\\/g, '/');

  // Handle different cloud storage path formats:
  // 1. New format: "dk/store/2025-w29/images/page_001.jpg" (use as-is)
  // 2. Legacy format: "images/catalog_123/page_1.png" (use as-is)
  // 3. Very old format: "catalog_123/page_1.png" (add images/ prefix)

  if (normalized.startsWith('dk/') || normalized.startsWith('catalogs/') || normalized.toLowerCase().startsWith('images/')) {
    // New format or legacy format - use as-is
    return `${CLOUD_STORAGE_BASE}/${normalized}`;
  } else {
    // Very old format - add images/ prefix
    return `${CLOUD_STORAGE_BASE}/images/${normalized}`;
  }
};

// Function to get a store's logo URL from its name
// NOTE: This function is deprecated - logo URLs should come from the API via /stores endpoint
// This is kept as a fallback only
export const getStoreLogoUrl = (storeName?: string) => {
  if (!storeName) return undefined;

  // This function should not be used anymore - logos should come from the API
  // which fetches them from Google Cloud Storage
  console.warn(`getStoreLogoUrl is deprecated. Store logos should come from API /stores endpoint. Called for: ${storeName}`);

  // Return undefined to force using API-provided logo URLs
  return undefined;
};
