"use client";

import React, { createContext, useState, useContext, ReactNode } from 'react';

interface SelectedCatalogsContextType {
  selectedCatalogs: number[];
  toggleCatalog: (catalogId: number) => void;
  clearSelectedCatalogs: () => void;
}

const SelectedCatalogsContext = createContext<SelectedCatalogsContextType | undefined>(undefined);

export function SelectedCatalogsProvider({ children }: { children: ReactNode }) {
  const [selectedCatalogs, setSelectedCatalogs] = useState<number[]>([]);

  const toggleCatalog = (catalogId: number) => {
    setSelectedCatalogs(prev => 
      prev.includes(catalogId) ? prev.filter(id => id !== catalogId) : [...prev, catalogId]
    );
  };

  const clearSelectedCatalogs = () => {
    setSelectedCatalogs([]);
  };

  return (
    <SelectedCatalogsContext.Provider value={{ selectedCatalogs, toggleCatalog, clearSelectedCatalogs }}>
      {children}
    </SelectedCatalogsContext.Provider>
  );
}

export function useSelectedCatalogsContext() {
  const context = useContext(SelectedCatalogsContext);
  if (context === undefined) {
    throw new Error('useSelectedCatalogsContext must be used within a SelectedCatalogsProvider');
  }
  return context;
}
