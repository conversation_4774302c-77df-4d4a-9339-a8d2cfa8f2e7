{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "prettier": "prettier --write --ignore-unknown .", "prettier:check": "prettier --check --ignore-unknown .", "test": "pnpm prettier:check"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "clsx": "^2.1.1", "geist": "^1.3.1", "gsap": "^3.13.0", "keyboardjs": "^2.7.0", "lucide-react": "^0.525.0", "next": "15.0.3", "novel": "^1.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "react-share": "^5.2.2", "react-zoom-pan-pinch": "^3.7.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "screenfull": "^6.0.2", "sonner": "^2.0.6"}, "devDependencies": {"@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/postcss": "^4.0.14", "@tailwindcss/typography": "^0.5.16", "@types/node": "22.13.10", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "postcss": "^8.5.3", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.14", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "5.8.2"}}