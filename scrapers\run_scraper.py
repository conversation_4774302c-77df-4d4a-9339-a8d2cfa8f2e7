# File: scrapers/run_scraper.py

# Load environment variables first
from dotenv import load_dotenv
load_dotenv()

import asyncio
import argparse
import json
import logging
import os
import sys
import importlib
from pathlib import Path
import io

# --- START SCRIPT SETUP ---

# 1. Configure logging with optimized format.
# Logs are sent to stderr to keep stdout clean for JSON data.
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(module)s:%(lineno)d - %(message)s',
    datefmt='%d-%m %H:%M:%S',  # Optimized: dd-mm hh:mm:ss format saves tokens
    stream=sys.stderr,
    force=True
)
main_logger = logging.getLogger(__name__)

# 2. Force stdout to use UTF-8 encoding.
# This is critical to prevent mojibake on Windows when piping JSON output.
if sys.stdout.encoding != 'utf-8':
    try:
        # Preferred method for Python 3.7+
        sys.stdout.reconfigure(encoding='utf-8')
        main_logger.debug("Reconfigured stdout to UTF-8.")
    except (TypeError, AttributeError):
        # Fallback for older versions or specific environments
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        main_logger.debug("Wrapped stdout with TextIOWrapper for UTF-8.")

# 3. Determine project root and add it to Python Path.
PROJECT_ROOT = Path(__file__).resolve().parent.parent
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

main_logger.debug("Execution started. Logging, UTF-8 stdout, and path configured.")
main_logger.debug(f"sys.stdout.encoding: {sys.stdout.encoding}")

# --- END SCRIPT SETUP ---


async def main():
    """
    Dynamically loads and runs a store scraper based on a JSON config file.
    Outputs a single JSON list to stdout. On error, the list is empty.
    """
    parser = argparse.ArgumentParser(description='Run a Playwright scraper for a specific store.')
    parser.add_argument('config_name', type=str, help='Name of the JSON config file in scrapers/configs/ (e.g., meny.json)')
    args = parser.parse_args()

    main_logger.info(f"--- Starting Scraper Run for Config: {args.config_name} ---")

    scraped_data = []  # Default to an empty list

    try:
        # --- Load Configuration ---
        config_file_path = PROJECT_ROOT / 'scrapers' / 'configs' / args.config_name
        if not config_file_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_file_path}")

        with open(config_file_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        main_logger.info(f"Successfully loaded configuration from: {config_file_path}")

        # --- Dynamic Scraper Class Loading ---
        scraper_class_name = config_data.get('scraper_class_name')
        if not scraper_class_name:
            raise ValueError(f"'scraper_class_name' not specified in config: {args.config_name}")

        module_name_snake_case = scraper_class_name.replace('Scraper', '').lower() + "_scraper"
        scraper_module_path = f"scrapers.store_scrapers.{module_name_snake_case}"

        main_logger.info(f"Attempting to load module: '{scraper_module_path}' for class: '{scraper_class_name}'")
        module = importlib.import_module(scraper_module_path)
        ScraperClass = getattr(module, scraper_class_name)
        main_logger.info(f"Successfully loaded ScraperClass: {scraper_class_name}")

        # --- Instantiate and Run the Scraper ---
        scraper_instance = ScraperClass(config_data)
        main_logger.info(f"Starting {scraper_class_name}.run()...")
        result = await scraper_instance.run()
        main_logger.info(f"{scraper_class_name}.run() finished.")

        if result and isinstance(result, list):
            scraped_data = result
            main_logger.info(f"Successfully scraped {len(scraped_data)} catalog(s).")
        else:
            main_logger.warning(f"Scraper returned no data or data in an incorrect format. Result: {result}")

    except Exception as e:
        # Log any and all errors to stderr. The calling process will capture this.
        main_logger.error(f"A critical error occurred during the scraper run for {args.config_name}: {e}", exc_info=True)
        # `scraped_data` remains an empty list, which will be printed in the `finally` block.

    finally:
        # --- Output Generation ---
        # Always print the resulting list to stdout. It will be empty if an error occurred.
        # This provides a consistent output format for the parent process.
        # `ensure_ascii=False` is critical for outputting raw UTF-8 characters.
        main_logger.info(f"--- Scraper Run for Config: {args.config_name} Ended. Writing {len(scraped_data)} items to stdout. ---")
        print(json.dumps(scraped_data, indent=2, ensure_ascii=False))


if __name__ == '__main__':
    try:
        asyncio.run(main())
    except Exception as e:
        # A final catch-all for errors that might occur outside the main async loop.
        main_logger.critical(f"A fatal, top-level error occurred in run_scraper.py: {e}", exc_info=True)
        # Still attempt to print an empty list to not break the parent process's JSON parser
        print(json.dumps([], indent=2, ensure_ascii=False))
        sys.exit(1)