#!/usr/bin/env python3
"""
Full PDF processing test - tests the actual PDF processing with a real or generated PDF.
Use this to verify the refactor works end-to-end.
"""

import os
import sys
import logging
import tempfile
import time
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)-20s - %(levelname)-8s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger('test_pdf_full')

def create_test_pdf():
    """Create a test PDF with Danish text for testing"""
    logger.info("📄 Creating test PDF with Danish catalog content...")
    
    try:
        import fitz  # PyMuPDF
        
        # Create a PDF that looks like a Danish supermarket catalog
        doc = fitz.open()
        
        # Page 1 - Cover with dates
        page1 = doc.new_page()
        page1.insert_text((50, 50), "NETTO TILBUD", fontsize=20, color=(1, 0, 0))
        page1.insert_text((50, 100), "Gælder fra: 2024-01-15", fontsize=14)
        page1.insert_text((50, 120), "Til: 2024-01-21", fontsize=14)
        page1.insert_text((50, 160), "Ugens bedste tilbud!", fontsize=16)
        
        # Page 2 - Products
        page2 = doc.new_page()
        page2.insert_text((50, 50), "MÆLK", fontsize=16, color=(0, 0, 1))
        page2.insert_text((50, 80), "Arla Økologisk Mælk 1L", fontsize=12)
        page2.insert_text((50, 100), "Pris: 15,95 kr", fontsize=12)
        page2.insert_text((50, 140), "BRØD", fontsize=16, color=(0, 0, 1))
        page2.insert_text((50, 170), "Kohberg Franskbrød", fontsize=12)
        page2.insert_text((50, 190), "Pris: 12,00 kr", fontsize=12)
        
        # Page 3 - More products
        page3 = doc.new_page()
        page3.insert_text((50, 50), "KØDVARER", fontsize=16, color=(0, 0, 1))
        page3.insert_text((50, 80), "Hakket oksekød 500g", fontsize=12)
        page3.insert_text((50, 100), "Pris: 45,00 kr", fontsize=12)
        
        # Save to temp file
        temp_pdf = tempfile.NamedTemporaryFile(suffix='_test_catalog.pdf', delete=False)
        doc.save(temp_pdf.name)
        doc.close()
        
        file_size = os.path.getsize(temp_pdf.name)
        logger.info(f"✅ Created test PDF: {temp_pdf.name} ({file_size} bytes)")
        return temp_pdf.name
    except Exception as e:
        logger.error(f"❌ Failed to create test PDF: {e}")
        return None

def test_local_pdf_processing(pdf_path: str):
    """Test processing a local PDF file"""
    logger.info("🧪 Testing local PDF processing...")
    
    try:
        from pdf_extractor import process_pdf_catalog
        from database import SessionLocal
        
        start_time = time.time()
        
        db = SessionLocal()
        try:
            catalog, image_paths = process_pdf_catalog(
                temp_pdf_path=pdf_path,
                store_name="Test Store",
                original_filename="test_catalog.pdf",
                title="Test Catalog - Local Processing",
                valid_from=datetime(2024, 1, 15),
                valid_to=datetime(2024, 1, 21),
                attempt_date_extraction=False,  # Skip AI to avoid API costs during testing
                dpi=150,  # Lower DPI for faster testing
                db=db
            )
            
            processing_time = time.time() - start_time
            
            if catalog and image_paths:
                logger.info(f"✅ Local processing successful!")
                logger.info(f"   Catalog ID: {catalog.id}")
                logger.info(f"   Title: {catalog.title}")
                logger.info(f"   Store: {catalog.store.name}")
                logger.info(f"   Pages: {len(image_paths)}")
                logger.info(f"   Processing time: {processing_time:.2f} seconds")
                logger.info(f"   Image paths: {image_paths[:2]}...")  # Show first 2 paths
                return True, catalog.id
            else:
                logger.error("❌ Local processing failed - no catalog returned")
                return False, None
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ Local PDF processing test failed: {e}")
        return False, None

def test_cloud_pdf_processing(catalog_id: int):
    """Test processing a PDF from cloud storage"""
    logger.info("🧪 Testing cloud PDF processing...")
    
    try:
        from pdf_extractor import process_pdf_catalog_from_cloud
        from database import SessionLocal, Catalog
        
        # Get the cloud path from the previously created catalog
        db = SessionLocal()
        try:
            catalog = db.query(Catalog).filter(Catalog.id == catalog_id).first()
            if not catalog:
                logger.error(f"❌ Could not find catalog {catalog_id}")
                return False
            
            cloud_pdf_path = catalog.pdf_path
            logger.info(f"📥 Testing with cloud PDF: {cloud_pdf_path}")
            
            start_time = time.time()
            
            # Process the same PDF from cloud storage
            new_catalog, image_paths = process_pdf_catalog_from_cloud(
                cloud_pdf_path=cloud_pdf_path,
                store_name="Test Store Cloud",
                title="Test Catalog - Cloud Processing",
                valid_from=datetime(2024, 1, 15),
                valid_to=datetime(2024, 1, 21),
                attempt_date_extraction=False,  # Skip AI to avoid API costs
                dpi=150,  # Lower DPI for faster testing
                db=db
            )
            
            processing_time = time.time() - start_time
            
            if new_catalog and image_paths:
                logger.info(f"✅ Cloud processing successful!")
                logger.info(f"   Catalog ID: {new_catalog.id}")
                logger.info(f"   Title: {new_catalog.title}")
                logger.info(f"   Store: {new_catalog.store.name}")
                logger.info(f"   Pages: {len(image_paths)}")
                logger.info(f"   Processing time: {processing_time:.2f} seconds")
                return True
            else:
                logger.error("❌ Cloud processing failed - no catalog returned")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ Cloud PDF processing test failed: {e}")
        return False

def test_performance_comparison():
    """Test performance with different DPI settings"""
    logger.info("🧪 Testing performance with different DPI settings...")
    
    pdf_path = create_test_pdf()
    if not pdf_path:
        return False
    
    try:
        from pdf_extractor import _process_pdf_unified
        from database import SessionLocal
        
        dpi_settings = [75, 150, 300]
        results = {}
        
        for dpi in dpi_settings:
            logger.info(f"   Testing DPI: {dpi}")
            
            db = SessionLocal()
            try:
                start_time = time.time()
                
                catalog, image_paths = _process_pdf_unified(
                    pdf_source=pdf_path,
                    store_name=f"Test Store DPI{dpi}",
                    title=f"Test Catalog DPI {dpi}",
                    attempt_date_extraction=False,
                    dpi=dpi,
                    db=db
                )
                
                processing_time = time.time() - start_time
                results[dpi] = {
                    'success': catalog is not None,
                    'time': processing_time,
                    'pages': len(image_paths) if image_paths else 0
                }
                
                logger.info(f"     DPI {dpi}: {processing_time:.2f}s, {len(image_paths) if image_paths else 0} pages")
                
            finally:
                db.close()
        
        # Report results
        logger.info("📊 Performance comparison:")
        for dpi, result in results.items():
            status = "✅" if result['success'] else "❌"
            logger.info(f"   {status} DPI {dpi}: {result['time']:.2f}s for {result['pages']} pages")
        
        return all(r['success'] for r in results.values())
        
    except Exception as e:
        logger.error(f"❌ Performance test failed: {e}")
        return False
    finally:
        # Clean up
        if pdf_path and os.path.exists(pdf_path):
            os.remove(pdf_path)

def run_full_tests():
    """Run comprehensive PDF processing tests"""
    logger.info("🚀 Starting comprehensive PDF processing tests...")
    logger.info("=" * 60)
    
    # Create test PDF
    pdf_path = create_test_pdf()
    if not pdf_path:
        logger.error("❌ Cannot create test PDF - aborting tests")
        return False
    
    try:
        tests_passed = 0
        total_tests = 3
        
        # Test 1: Local PDF processing
        logger.info("\n📋 Test 1: Local PDF Processing")
        success, catalog_id = test_local_pdf_processing(pdf_path)
        if success:
            tests_passed += 1
        
        # Test 2: Cloud PDF processing (only if local test passed)
        if success and catalog_id:
            logger.info("\n📋 Test 2: Cloud PDF Processing")
            if test_cloud_pdf_processing(catalog_id):
                tests_passed += 1
        else:
            logger.warning("⚠️ Skipping cloud test due to local test failure")
        
        # Test 3: Performance comparison
        logger.info("\n📋 Test 3: Performance Comparison")
        if test_performance_comparison():
            tests_passed += 1
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("📊 COMPREHENSIVE TEST RESULTS")
        logger.info("=" * 60)
        logger.info(f"🎯 Tests passed: {tests_passed}/{total_tests}")
        
        if tests_passed == total_tests:
            logger.info("🎉 ALL TESTS PASSED! The refactor is working correctly.")
            return True
        else:
            logger.warning(f"⚠️ {total_tests - tests_passed} tests failed. Review before deploying.")
            return False
            
    finally:
        # Clean up test PDF
        if os.path.exists(pdf_path):
            os.remove(pdf_path)
            logger.info(f"🧹 Cleaned up test PDF: {pdf_path}")

if __name__ == "__main__":
    success = run_full_tests()
    sys.exit(0 if success else 1)
