from sqlalchemy import create_engine, Column, Integer, String, Float, <PERSON><PERSON>ey, DateTime, Text, JSON, <PERSON>olean, Date, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Session
import os
from datetime import datetime
from typing import Optional

# The .env file is now loaded by config.py. Ensure config is imported in the main script before database.


# Get DATABASE_URL from environment, with fallback for common typos
raw_db_url = os.getenv("DATABASE_URL") or os.getenv("DATABSE_URL")  # Handle common typo
if not raw_db_url:
    raw_db_url = "postgresql://user:password@host:port/dbname"

# Replace the literal placeholder ':port/' with default 5432 if still present
if ":port/" in raw_db_url:
    raw_db_url = raw_db_url.replace(":port/", ":5432/")
# If still using placeholder host, fall back to local SQLite database for development
if "host" in raw_db_url:
    raw_db_url = "sqlite:///./local.db"
DATABASE_URL = raw_db_url

# Allow creating engine with a specific URL (for testing or alternative configs)
def create_db_engine(db_url: str = DATABASE_URL, connect_args: dict = {}):
    # Enhanced connection pool settings for better reliability
    if "postgresql" in db_url:
        # PostgreSQL-specific connection pool settings
        return create_engine(
            db_url,
            connect_args=connect_args,
            pool_size=10,           # Number of connections to maintain
            max_overflow=20,        # Additional connections beyond pool_size
            pool_timeout=30,        # Seconds to wait for connection
            pool_recycle=3600,      # Recycle connections after 1 hour
            pool_pre_ping=True,     # Validate connections before use
            echo=False              # Set to True for SQL debugging
        )
    else:
        # SQLite or other databases
        return create_engine(db_url, connect_args=connect_args)

engine = create_db_engine() # Create default engine
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class Store(Base):
    __tablename__ = "stores"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), index=True)
    logo_url = Column(String(255), nullable=True)
    
    catalogs = relationship("Catalog", back_populates="store")

class Catalog(Base):
    __tablename__ = "catalogs"
    
    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id"))
    title = Column(String(255))
    valid_from = Column(DateTime, default=datetime.now)
    valid_to = Column(DateTime, nullable=True)
    pdf_path = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)
    is_latest_for_today = Column(Boolean, default=False, nullable=False) # New column
    
    store = relationship("Store", back_populates="catalogs")
    pages = relationship("CatalogPage", back_populates="catalog")
    products = relationship("Product", back_populates="catalog")

class CatalogPage(Base):
    __tablename__ = "catalog_pages"
    
    id = Column(Integer, primary_key=True, index=True)
    catalog_id = Column(Integer, ForeignKey("catalogs.id"))
    page_number = Column(Integer)
    image_path = Column(String(255))
    raw_text = Column(Text, nullable=True)
    
    catalog = relationship("Catalog", back_populates="pages")

class Product(Base):
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, index=True)
    catalog_id = Column(Integer, ForeignKey("catalogs.id"))
    name = Column(String(255), index=True)
    description = Column(Text, nullable=True)
    price = Column(Float)
    original_price = Column(Float, nullable=True)
    unit = Column(String(50), nullable=True)  # kg, pcs, etc.
    category = Column(String(100), nullable=True)
    page_number = Column(Integer, nullable=True)
    image_path = Column(String(255), nullable=True)

    # --- New Fields for Feature 2 ---
    quantity = Column(Float, nullable=True) # Extracted numeric quantity (e.g., 500.0, 1.5, 3.0)
    unit_type = Column(String(20), nullable=True) # Extracted unit type (e.g., "g", "kg", "l", "ml", "stk", "pakke")
    price_per_base_unit = Column(Float, nullable=True) # Calculated price (e.g., kr/kg or kr/l)
    base_unit_type = Column(String(10), nullable=True) # Base unit for calculation ("kg" or "l")
    # --- End New Fields ---

    brand = Column(String(100), nullable=True, index=True) # Extracted brand/manufacturer

    catalog = relationship("Catalog", back_populates="products")

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_admin = Column(Boolean, default=False, nullable=False)
    preferred_model = Column(String(100), nullable=True)
    query_history = Column(JSON, nullable=True) # Store last 3 queries as a list

# --- NEW: Model for Storing Application Settings --- 
class AppSettings(Base):
    __tablename__ = "app_settings"

    key = Column(String(100), primary_key=True, index=True)
    value = Column(Text, nullable=True) # Store values as text
# --- End New Model ---

class ProcessedCatalogHashes(Base):
    __tablename__ = "processed_catalog_hashes"

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    catalog_title_from_scraper = Column(String(255), nullable=True) # Title as seen by scraper
    valid_from_date = Column(Date, nullable=False)
    valid_to_date = Column(Date, nullable=False)
    pdf_content_hash = Column(String(64), nullable=False, index=True) # SHA256 hash
    status = Column(String(50), nullable=False, default='PENDING') # e.g., PENDING, SUCCESS, FAILED
    processed_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    catalog_db_id = Column(Integer, ForeignKey("catalogs.id"), nullable=True)

    store = relationship("Store")
    catalog = relationship("Catalog")

    __table_args__ = (UniqueConstraint('store_id', 'valid_from_date', 'valid_to_date', 'pdf_content_hash', name='uq_store_date_hash'),
                     )

class CatalogProcessingQueue(Base):
    __tablename__ = "catalog_processing_queue"

    id = Column(Integer, primary_key=True, index=True)
    catalog_db_id = Column(Integer, ForeignKey("catalogs.id"), nullable=False)
    pdf_path_to_process = Column(String(512), nullable=False)
    pdf_content_hash = Column(String(64), nullable=False) # To cross-reference with ProcessedCatalogHashes
    status = Column(String(50), nullable=False, default='PENDING') # e.g., PENDING, PROCESSING, SUCCESS, FAILED, FAILED_RETRY, FAILED_MAX_RETRIES
    retry_count = Column(Integer, default=0)
    added_at = Column(DateTime, default=datetime.now)
    last_attempt_at = Column(DateTime, nullable=True)

    catalog = relationship("Catalog")

class OperationLog(Base):
    """
    🏗️ ENTERPRISE OPERATION LOGGING
    Comprehensive logging for all pipeline operations with automatic status reconciliation.
    Provides full audit trail and enables hands-off monitoring.
    """
    __tablename__ = "operation_logs"

    id = Column(Integer, primary_key=True, index=True)
    catalog_id = Column(Integer, ForeignKey("catalogs.id", ondelete="SET NULL"), nullable=True)  # Nullable for system-wide operations
    store_name = Column(String(100), nullable=True)
    operation_type = Column(String(50), nullable=False, index=True)  # SCRAPE, PARSE, STATUS_UPDATE, RECONCILE
    operation_status = Column(String(50), nullable=False, index=True)  # STARTED, SUCCESS, FAILED, CANCELLED
    message = Column(Text, nullable=True)
    error_details = Column(Text, nullable=True)
    operation_metadata = Column(JSON, nullable=True)  # Store additional context (product counts, timing, etc.)
    started_at = Column(DateTime, default=datetime.now, nullable=False, index=True)
    completed_at = Column(DateTime, nullable=True)
    duration_seconds = Column(Float, nullable=True)
    triggered_by = Column(String(100), nullable=True)  # USER, CRON, AUTO_RECONCILE

    catalog = relationship("Catalog")


def init_db(bind_engine=engine): # Allow specifying engine for init
    Base.metadata.create_all(bind=bind_engine)

# Dependency to get DB session
# Note: This default 'get_db' uses the global SessionLocal bound to the default engine.
# For testing, we will override this dependency.
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close() 

# --- Settings Helper Functions ---

def get_setting(db: Session, key: str, default: Optional[str] = None) -> Optional[str]:
    """Retrieve a setting value from the AppSettings table."""
    setting = db.query(AppSettings).filter(AppSettings.key == key).first()
    return setting.value if setting else default

def set_setting(db: Session, key: str, value: str):
    """Create or update a setting value in the AppSettings table."""
    setting = db.query(AppSettings).filter(AppSettings.key == key).first()
    if setting:
        setting.value = value
    else:
        setting = AppSettings(key=key, value=value)
        db.add(setting)
    # No commit here - assume the calling function handles the commit
    # db.commit() 
# --- End Settings Helper Functions --- 