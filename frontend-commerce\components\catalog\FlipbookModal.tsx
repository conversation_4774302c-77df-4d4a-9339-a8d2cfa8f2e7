"use client";

import React, { useEffect, useState } from 'react';
import { X, Download, ExternalLink } from 'lucide-react';

interface FlipbookModalProps {
  isOpen: boolean;
  onClose: () => void;
  pdfUrl: string;
  title: string;
  storeName: string;
}

/**
 * Render-safe PDF viewer using native browser iframe
 *
 * This component provides a reliable PDF viewing experience that works
 * consistently across all deployment environments including Render.
 *
 * Features:
 * - Zero dependencies (no PDF.js workers to break)
 * - Native browser PDF controls (zoom, navigation, search)
 * - Mobile-friendly with responsive design
 * - Keyboard shortcuts (Escape to close)
 * - Loading states and error handling
 * - Download and external link options
 */

export default function FlipbookModal({ isOpen, onClose, pdfUrl, title, storeName }: FlipbookModalProps) {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Handle keyboard shortcuts and body scroll
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Reset loading state when PDF URL changes
  useEffect(() => {
    if (pdfUrl && isOpen) {
      setIsLoading(true);
      setError(null);
      console.log('PDF Modal opened with URL:', pdfUrl);
    }
  }, [pdfUrl, isOpen]);

  // Handle iframe load events
  const handleIframeLoad = () => {
    setIsLoading(false);
    setError(null);
    console.log('PDF loaded successfully in iframe');
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setError('Failed to load PDF. Please try downloading the file instead.');
    console.error('Failed to load PDF in iframe:', pdfUrl);
  };

  // Close modal when clicking outside
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Handle download
  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = pdfUrl;
    link.download = `${storeName}_catalog.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle external link
  const handleExternalLink = () => {
    window.open(pdfUrl, '_blank');
  };

  // Don't render if not open
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg max-w-6xl max-h-[95vh] w-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gray-50">
          <div className="flex-1">
            <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
            <p className="text-sm text-gray-600">{storeName}</p>
          </div>

          {/* Action buttons */}
          <div className="flex items-center gap-2">
            <button
              onClick={handleDownload}
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
              title="Download PDF"
            >
              <Download className="w-5 h-5 text-gray-600" />
            </button>
            <button
              onClick={handleExternalLink}
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
              title="Open in new tab"
            >
              <ExternalLink className="w-5 h-5 text-gray-600" />
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
              title="Close"
            >
              <X className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 relative overflow-hidden">
          {/* Loading state */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading catalog...</p>
              </div>
            </div>
          )}

          {/* Error state */}
          {error && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
              <div className="text-center max-w-md mx-auto p-6">
                <p className="text-red-600 mb-4 text-lg">❌ {error}</p>
                <div className="space-y-2">
                  <button
                    onClick={handleDownload}
                    className="w-full bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    Download PDF
                  </button>
                  <button
                    onClick={handleExternalLink}
                    className="w-full bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    Open in New Tab
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* PDF iframe - render-safe approach */}
          {!error && (
            <iframe
              src={pdfUrl}
              className="w-full h-full border-0"
              title={`${storeName} Catalog - ${title}`}
              onLoad={handleIframeLoad}
              onError={handleIframeError}
              style={{ minHeight: '600px' }}
            />
          )}
        </div>
      </div>
    </div>
  );
}
