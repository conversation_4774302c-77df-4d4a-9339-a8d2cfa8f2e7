#!/usr/bin/env python3
import os
import sys
import logging
from datetime import datetime
from dotenv import load_dotenv
from database import init_db, SessionLocal, Store
from pdf_extractor import process_pdf_catalog
from gemini_parser import process_catalog_pages

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Main function to process PDF catalog and store in database"""
    # Load environment variables
    load_dotenv()
    
    print("PDF Database Import Tool")
    print("=" * 30)
    
    # Initialize database
    print("Initializing database...")
    init_db()
    print("Database initialized successfully")
    
    # PDF path
    pdf_path = "netto.pdf"
    if not os.path.exists(pdf_path):
        print(f"ERROR: PDF file not found at {pdf_path}")
        return
    
    print(f"Found PDF file: {pdf_path}")
    
    # Get Netto store from database
    db = SessionLocal()
    try:
        netto = db.query(Store).filter(Store.name == "Netto").first()
        
        # If Netto store doesn't exist, create it
        if not netto:
            print("Netto store not found in database. Creating it...")
            netto = Store(name="Netto")
            db.add(netto)
            db.commit()
            db.refresh(netto)
            print(f"Created Netto store with ID: {netto.id}")
        else:
            print(f"Found Netto store with ID: {netto.id}")
        
        # Process the PDF catalog
        print("\nProcessing PDF catalog...")
        catalog, image_paths = process_pdf_catalog(
            pdf_path,
            netto.name,
            "Netto Weekly Offers",
            datetime.now(),
            db=db  # Pass the database session
        )
        
        if not catalog:
            print("Failed to process catalog!")
            return
        
        # Need to refresh catalog object as it may have been detached
        catalog = db.merge(catalog)
        db.refresh(catalog)
        
        print(f"Successfully processed catalog: {catalog.title} (ID: {catalog.id})")
        print(f"Prepared {len(image_paths)} file(s) for analysis")
        
        # Extract products from catalog pages
        print("\nExtracting products from catalog pages...")
        total_products = process_catalog_pages(catalog.id)
        
        print(f"Successfully extracted {total_products} products from the catalog")
        
        print("\nPDF data has been successfully stored in the database!")
        print(f"Catalog ID: {catalog.id}")
        print(f"Total products: {total_products}")
    
    finally:
        # Close database connection
        db.close()

if __name__ == "__main__":
    main() 