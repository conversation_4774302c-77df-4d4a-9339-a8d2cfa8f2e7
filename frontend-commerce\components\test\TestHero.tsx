"use client";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import TestWrapper from "./TestWrapper";

function TestHeroContent() {
  const titleRef = useRef<HTMLHeadingElement>(null);
  const counterRef = useRef<HTMLSpanElement>(null);
  const [currentCount, setCurrentCount] = useState(47);
  const [activeUsers, setActiveUsers] = useState(23);
  const [catalogsProcessed, setCatalogsProcessed] = useState(156);
  const [recentSearches, setRecentSearches] = useState(89);
  const [recentDeals, setRecentDeals] = useState<Array<{
    id: string;
    title: string;
    store: string;
    price: string;
    savings: string;
    timestamp: Date;
  }>>([]);
  const [activityFeed, setActivityFeed] = useState<Array<{
    id: string;
    type: 'search' | 'discovery';
    user: string;
    action: string;
    timestamp: Date;
  }>>([]);
  const [pricingAlerts, setPricingAlerts] = useState<Array<{
    id: string;
    type: 'price_drop' | 'big_savings' | 'limited_time';
    title: string;
    oldPrice?: string;
    newPrice: string;
    savings: string;
    store: string;
    timestamp: Date;
  }>>([]);

  useEffect(() => {
    if (!titleRef.current) return;

    // Split text into individual characters and wrap each in a span
    const text = titleRef.current.textContent || "";
    titleRef.current.innerHTML = "";

    // Create spans for each character, preserving spaces
    text.split("").forEach((char, index) => {
      const span = document.createElement("span");
      span.textContent = char === " " ? "\u00A0" : char; // Use non-breaking space
      span.style.display = "inline-block";
      span.style.opacity = "0";
      span.style.transform = "translateY(100px)";
      titleRef.current?.appendChild(span);
    });

    // Animate each character with staggered timing
    const spans = titleRef.current.querySelectorAll("span");

    gsap.fromTo(spans,
      {
        opacity: 0,
        y: 100,
        rotationX: -90
      },
      {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: 0.8,
        ease: "back.out(1.7)",
        stagger: {
          amount: 1.2,
          from: "start"
        },
        delay: 0.5
      }
    );

    // Start the live counter animation after title animation
    const counterInterval = setInterval(() => {
      setCurrentCount(prev => {
        const increment = Math.floor(Math.random() * 5) + 1; // Random increment 1-5
        const newCount = prev + increment;

        // Animate the counter with GSAP
        if (counterRef.current) {
          gsap.fromTo(counterRef.current,
            { scale: 1 },
            {
              scale: 1.2,
              duration: 0.2,
              yoyo: true,
              repeat: 1,
              ease: "power2.out"
            }
          );
        }

        return newCount;
      });
    }, Math.random() * 3000 + 2000); // Random interval 2-5 seconds

    // Generate mock deals periodically
    const mockDeals = [
      { title: "Økologiske æg", store: "Netto", price: "25 kr", savings: "Spar 8 kr" },
      { title: "Coca-Cola 1.5L", store: "Rema 1000", price: "12 kr", savings: "Spar 5 kr" },
      { title: "Kyllingebryst", store: "Bilka", price: "89 kr/kg", savings: "Spar 20 kr" },
      { title: "Bananer", store: "Lidl", price: "8 kr/kg", savings: "Spar 3 kr" },
      { title: "Havregryn", store: "Spar", price: "15 kr", savings: "Spar 6 kr" },
      { title: "Mælk 1L", store: "Meny", price: "9 kr", savings: "Spar 4 kr" },
      { title: "Pasta", store: "Føtex", price: "12 kr", savings: "Spar 7 kr" },
      { title: "Yoghurt", store: "Netto", price: "18 kr", savings: "Spar 5 kr" }
    ];

    const dealInterval = setInterval(() => {
      // Failsafe: Ensure we have deals and safe array access
      if (!mockDeals || mockDeals.length === 0) return;

      const randomIndex = Math.floor(Math.random() * mockDeals.length);
      const randomDeal = mockDeals[randomIndex];

      // Double-check the deal exists and has required properties
      if (!randomDeal || !randomDeal.title || !randomDeal.store) return;

      const newDeal = {
        id: Math.random().toString(36).substr(2, 9),
        title: randomDeal.title,
        store: randomDeal.store,
        price: randomDeal.price || 'N/A',
        savings: randomDeal.savings || 'N/A',
        timestamp: new Date()
      };

      setRecentDeals(prev => {
        const updated = [newDeal, ...prev].slice(0, 4); // Keep only 4 most recent
        return updated;
      });
    }, Math.random() * 4000 + 3000); // Random interval 3-7 seconds

    // Social proof counters
    const socialProofInterval = setInterval(() => {
      // Randomly update one of the social proof counters
      const randomCounter = Math.floor(Math.random() * 3);

      if (randomCounter === 0) {
        setActiveUsers(prev => prev + Math.floor(Math.random() * 3) + 1);
      } else if (randomCounter === 1) {
        setCatalogsProcessed(prev => prev + Math.floor(Math.random() * 2) + 1);
      } else {
        setRecentSearches(prev => prev + Math.floor(Math.random() * 4) + 1);
      }
    }, Math.random() * 8000 + 5000); // Random interval 5-13 seconds

    // Activity feed generation
    const mockActivities = [
      { type: 'search' as const, actions: ['søgte efter "billig kaffe"', 'ledte efter "økologisk mælk"', 'spurgte om "tilbud på kød"', 'søgte "vaskepulver tilbud"'] },
      { type: 'discovery' as const, actions: ['fandt 15% rabat på pasta', 'opdagede tilbud på jordbær', 'sparede 25 kr på kylling', 'fandt billig yoghurt'] }
    ];

    const userNames = ['Anna K.', 'Lars M.', 'Sofie P.', 'Michael R.', 'Emma L.', 'Thomas H.', 'Maria S.', 'Peter J.'];

    const activityInterval = setInterval(() => {
      // Failsafe: Ensure we have activities and safe array access
      if (!mockActivities || mockActivities.length === 0) return;
      if (!userNames || userNames.length === 0) return;

      const randomActivity = mockActivities[Math.floor(Math.random() * mockActivities.length)];
      if (!randomActivity || !randomActivity.actions || randomActivity.actions.length === 0) return;

      const randomAction = randomActivity.actions[Math.floor(Math.random() * randomActivity.actions.length)];
      const randomUser = userNames[Math.floor(Math.random() * userNames.length)];

      if (!randomAction || !randomUser) return;

      const newActivity = {
        id: Math.random().toString(36).substr(2, 9),
        type: randomActivity.type,
        user: randomUser,
        action: randomAction,
        timestamp: new Date()
      };

      setActivityFeed(prev => {
        const updated = [newActivity, ...prev].slice(0, 5); // Keep only 5 most recent
        return updated;
      });
    }, Math.random() * 6000 + 4000); // Random interval 4-10 seconds

    // Pricing alerts generation
    const mockPricingAlerts = [
      { type: 'price_drop' as const, title: 'Kaffe', oldPrice: '45 kr', newPrice: '29 kr', savings: 'Spar 16 kr', store: 'Netto' },
      { type: 'big_savings' as const, title: 'Kyllingebryst', newPrice: '69 kr/kg', savings: 'Spar 30 kr', store: 'Bilka' },
      { type: 'limited_time' as const, title: 'Yoghurt 4-pak', newPrice: '15 kr', savings: 'Spar 10 kr', store: 'Lidl' },
      { type: 'price_drop' as const, title: 'Pasta', oldPrice: '18 kr', newPrice: '12 kr', savings: 'Spar 6 kr', store: 'Føtex' },
      { type: 'big_savings' as const, title: 'Bananer', newPrice: '8 kr/kg', savings: 'Spar 5 kr', store: 'Rema 1000' }
    ];

    const pricingAlertInterval = setInterval(() => {
      // Failsafe: Ensure we have pricing alerts and safe array access
      if (!mockPricingAlerts || mockPricingAlerts.length === 0) return;

      const randomAlert = mockPricingAlerts[Math.floor(Math.random() * mockPricingAlerts.length)];
      if (!randomAlert) return;

      const newAlert = {
        id: Math.random().toString(36).substr(2, 9),
        type: randomAlert.type,
        title: randomAlert.title || 'Unknown Product',
        oldPrice: randomAlert.oldPrice,
        newPrice: randomAlert.newPrice || 'N/A',
        savings: randomAlert.savings || 'N/A',
        store: randomAlert.store || 'Unknown Store',
        timestamp: new Date()
      };

      setPricingAlerts(prev => {
        const updated = [newAlert, ...prev].slice(0, 3); // Keep only 3 most recent
        return updated;
      });
    }, Math.random() * 8000 + 6000); // Random interval 6-14 seconds

    return () => {
      clearInterval(counterInterval);
      clearInterval(dealInterval);
      clearInterval(socialProofInterval);
      clearInterval(activityInterval);
      clearInterval(pricingAlertInterval);
    };
  }, []);

  return (
    <div className="text-center py-2 px-6">
      <div className="max-w-5xl mx-auto relative z-10 flex flex-col items-center">
        {/* Logo */}
        <div className="w-60 h-60 md:w-72 md:h-72 relative mb-4">
          <Image
            src="/logos/logo-samlet.png"
            alt="Tilbudsjægeren Owl Logo"
            fill
            className="object-contain"
            priority
          />
        </div>

        <h2
          ref={titleRef}
          className="text-2xl md:text-3xl font-bold text-gray-900 mb-3 leading-tight text-center"
          style={{ perspective: "1000px" }}
        >
          Find de Bedste Tilbud med AI
        </h2>
        
        <p className="text-gray-600 text-lg max-w-2xl mx-auto mb-4 text-center">
          Udforsk tilbud fra danske supermarkeder. Spørg AI'en og find præcis hvad du søger.
        </p>
        
        {/* Live Counter */}
        <div className="mt-4 p-3 bg-sky-50 rounded-lg border border-sky-200 shadow-sm">
          <div className="text-sm text-sky-600">
            🔍 <span ref={counterRef} className="font-semibold text-sky-700">{currentCount}</span> tilbud fundet i dag
          </div>
        </div>

        {/* Social Proof Counters */}
        <div className="mt-4 grid grid-cols-3 gap-3 max-w-md mx-auto">
          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 border border-gray-200 shadow-sm text-center">
            <div className="text-lg font-bold text-gray-800">{activeUsers}</div>
            <div className="text-xs text-gray-600">👥 Aktive brugere</div>
          </div>
          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 border border-gray-200 shadow-sm text-center">
            <div className="text-lg font-bold text-gray-800">{catalogsProcessed}</div>
            <div className="text-xs text-gray-600">📚 Kataloger scannet</div>
          </div>
          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-3 border border-gray-200 shadow-sm text-center">
            <div className="text-lg font-bold text-gray-800">{recentSearches}</div>
            <div className="text-xs text-gray-600">🔍 Søgninger i dag</div>
          </div>
        </div>

        {/* Animated Product Discovery Feed */}
        {recentDeals.length > 0 && (
          <div className="mt-6 w-full max-w-md mx-auto">
            <h3 className="text-sm font-semibold text-gray-600 mb-3 text-center">
              🔥 Seneste tilbud fundet
            </h3>
            <div className="space-y-2">
              {recentDeals.map((deal, index) => (
                <div
                  key={deal.id}
                  className="bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-300 animate-in slide-in-from-top-2"
                  style={{
                    animationDelay: `${index * 100}ms`,
                    animationDuration: '500ms'
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-900">{deal.title}</span>
                        <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                          {deal.savings}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-gray-500">{deal.store}</span>
                        <span className="text-xs text-gray-400">•</span>
                        <span className="text-sm font-semibold text-sky-600">{deal.price}</span>
                      </div>
                    </div>
                    <div className="text-xs text-gray-400">
                      {Math.floor((Date.now() - deal.timestamp.getTime()) / 1000)}s
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Real-time Activity Feed */}
        {activityFeed.length > 0 && (
          <div className="mt-6 w-full max-w-md mx-auto">
            <h3 className="text-sm font-semibold text-gray-600 mb-3 text-center">
              ⚡ Live aktivitet
            </h3>
            <div className="space-y-2">
              {activityFeed.map((activity, index) => (
                <div
                  key={activity.id}
                  className="bg-gray-50 border border-gray-100 rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-300 animate-in slide-in-from-top-2"
                  style={{
                    animationDelay: `${index * 100}ms`,
                    animationDuration: '500ms'
                  }}
                >
                  <div className="flex items-start gap-2">
                    <div className="text-lg">
                      {activity.type === 'search' ? '🔍' : '🎯'}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm text-gray-800">
                        <span className="font-medium">{activity.user}</span> {activity.action}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {Math.floor((Date.now() - activity.timestamp.getTime()) / 1000)}s siden
                      </div>
                    </div>
                    <div className={`w-2 h-2 rounded-full ${
                      activity.type === 'search' ? 'bg-blue-400' : 'bg-green-400'
                    }`}></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Dynamic Pricing Alerts */}
        {pricingAlerts.length > 0 && (
          <div className="mt-6 w-full max-w-md mx-auto">
            <h3 className="text-sm font-semibold text-gray-600 mb-3 text-center">
              💰 Prisalarmer
            </h3>
            <div className="space-y-2">
              {pricingAlerts.map((alert, index) => (
                <div
                  key={alert.id}
                  className={`rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-300 animate-in slide-in-from-right-2 ${
                    alert.type === 'price_drop'
                      ? 'bg-red-50 border border-red-200'
                      : alert.type === 'big_savings'
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-orange-50 border border-orange-200'
                  }`}
                  style={{
                    animationDelay: `${index * 100}ms`,
                    animationDuration: '500ms'
                  }}
                >
                  <div className="flex items-start gap-2">
                    <div className="text-lg">
                      {alert.type === 'price_drop' ? '📉' : alert.type === 'big_savings' ? '💚' : '⏰'}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-900">{alert.title}</span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          alert.type === 'price_drop'
                            ? 'bg-red-100 text-red-700'
                            : alert.type === 'big_savings'
                            ? 'bg-green-100 text-green-700'
                            : 'bg-orange-100 text-orange-700'
                        }`}>
                          {alert.type === 'price_drop' ? 'Prisfald' : alert.type === 'big_savings' ? 'Store besparelser' : 'Begrænset tid'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        {alert.oldPrice && (
                          <span className="text-xs text-gray-400 line-through">{alert.oldPrice}</span>
                        )}
                        <span className="text-sm font-semibold text-green-600">{alert.newPrice}</span>
                        <span className="text-xs text-gray-400">•</span>
                        <span className="text-xs text-gray-500">{alert.store}</span>
                      </div>
                      <div className="text-xs font-medium text-green-700 mt-1">
                        {alert.savings}
                      </div>
                    </div>
                    <div className="text-xs text-gray-400">
                      {Math.floor((Date.now() - alert.timestamp.getTime()) / 1000)}s
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default function TestHero() {
  return (
    <TestWrapper
      fallbackTitle="Test Hero Component"
      fallbackMessage="The test hero component is disabled in production builds"
    >
      <TestHeroContent />
    </TestWrapper>
  );
}
