"""drop_obsolete_queue_and_hash_tables

Revision ID: 769079bbbb19
Revises: 1c4abaedaac5
Create Date: 2025-07-03 14:38:50.860116

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '769079bbbb19'
down_revision: Union[str, None] = '1c4abaedaac5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Drop obsolete queue and hash tables."""
    # Drop the tables that are no longer needed since parsing is now integrated into main API
    op.drop_table('catalog_processing_queue')
    op.drop_index(op.f('ix_processed_catalog_hashes_pdf_content_hash'), table_name='processed_catalog_hashes')
    op.drop_table('processed_catalog_hashes')


def downgrade() -> None:
    """Recreate the dropped tables."""
    # Recreate processed_catalog_hashes table
    op.create_table(
        'processed_catalog_hashes',
        sa.Column('id', sa.Integer(), nullable=False, primary_key=True, autoincrement=True),
        sa.Column('store_id', sa.Integer(), nullable=False),
        sa.Column('catalog_title_from_scraper', sa.String(length=255), nullable=True),
        sa.Column('valid_from_date', sa.Date(), nullable=False),
        sa.Column('valid_to_date', sa.Date(), nullable=False),
        sa.Column('pdf_content_hash', sa.String(length=64), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False, server_default='PENDING'),
        sa.Column('processed_at', sa.DateTime(), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('catalog_db_id', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
        sa.ForeignKeyConstraint(['catalog_db_id'], ['catalogs.id'], ),
        sa.UniqueConstraint('store_id', 'valid_from_date', 'valid_to_date', 'pdf_content_hash', name='uq_store_date_hash')
    )
    op.create_index(op.f('ix_processed_catalog_hashes_pdf_content_hash'), 'processed_catalog_hashes', ['pdf_content_hash'], unique=False)

    # Recreate catalog_processing_queue table
    op.create_table(
        'catalog_processing_queue',
        sa.Column('id', sa.Integer(), nullable=False, primary_key=True, autoincrement=True),
        sa.Column('catalog_db_id', sa.Integer(), nullable=False),
        sa.Column('pdf_path_to_process', sa.String(length=512), nullable=False),
        sa.Column('pdf_content_hash', sa.String(length=64), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False, server_default='PENDING'),
        sa.Column('retry_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('added_at', sa.DateTime(), nullable=False, server_default=sa.func.now()),
        sa.Column('last_attempt_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['catalog_db_id'], ['catalogs.id'], )
    )
