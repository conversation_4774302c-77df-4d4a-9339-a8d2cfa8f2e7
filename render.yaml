# Best-practice Render configuration using an Environment Group for shared secrets
# and ensuring all services have access to the persistent disk.

envVarGroups:
  - name: avis-scanner-vars
    envVars:
      - key: ALGORITHM
        value: HS256
      - key: ACCESS_TOKEN_EXPIRE_MINUTES
        value: 60
      - key: GEMINI_PARSING_MODEL
        value: 'models/gemini-2.5-flash-preview-04-17'
      - key: GEMINI_QUERY_MODEL
        value: 'models/gemini-2.5-flash-preview-04-17'
      - key: GEMINI_INITIAL_RATE
        value: '10'
      - key: GEMINI_MAX_RATE
        value: '30'
      - key: GEMINI_MIN_RATE
        value: '1'
      - key: CRON_SECRET
        generateValue: true
      - key: GOOGLE_API_KEY
        sync: false
      - key: SECRET_KEY
        sync: false
      - key: GCS_BUCKET_NAME
        value: tilbudsjaegeren
      - key: GOOGLE_CLOUD_CREDENTIALS_JSON
        sync: false

services:
  # Service 1: The FastAPI Backend API with Background Parser
  - type: web
    name: avis-api
    runtime: python
    repo: https://github.com/wetfox/Avis
    plan: Starter
    region: frankfurt
    branch: main
    buildCommand: "pip install -r requirements.txt"
    startCommand: "alembic upgrade head && uvicorn api:app --host 0.0.0.0 --port $PORT"
    envVars:
      - fromGroup: avis-scanner-vars
      - key: DATABASE_URL
        sync: false
      - key: PLAYWRIGHT_BROWSERS_PATH
        value: "0"
      - key: GOOGLE_API_KEY
        sync: false
      - key: SECRET_KEY
        sync: false
      - key: PYTHON_VERSION
        value: 3.11.7
    autoDeploy: true

  # Service 2: The Next.js Frontend
  - type: web
    name: Tilbudsjægeren Website
    runtime: node
    repo: https://github.com/wetfox/Avis
    plan: Starter
    region: frankfurt
    branch: main
    rootDir: frontend-commerce
    buildCommand: "npm install && npm run build"
    startCommand: "npm start"
    envVars:
      - key: NEXT_PUBLIC_API_BASE
        value: https://avis-api.onrender.com # Updated to match actual API service name

  # Service 3: Cron Job to trigger catalog processing
  - type: cron
    name: Daily Catalog Scraper
    runtime: python
    repo: https://github.com/wetfox/Avis
    plan: Starter
    region: frankfurt
    schedule: '0 4 * * *' # 4:00 UTC
    buildCommand: "pip install requests"
    startCommand: "python -c \"import os, requests; requests.post('https://avis-api.onrender.com/internal/run_catalog_processor', headers={'x-cron-secret': os.environ.get('CRON_SECRET')})\""
    envVars:
      - fromGroup: avis-scanner-vars


