"use client";
import { useEffect, useState } from 'react';
import { BackendCatalog, getCatalogs } from '@/lib/backend';
import { buildThumbnailUrl, getStoreLogoUrl } from '@/lib/ui-helpers';
import FlipbookModal from '@/components/catalog/FlipbookModal';

// Note: Catalog cards are for browsing only, not for selection
// Selection is handled by QuerySection component

// Define the API base for image URLs
const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';

export default function CatalogList() {
  const [catalogs, setCatalogs] = useState<BackendCatalog[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCatalog, setSelectedCatalog] = useState<BackendCatalog | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // Removed selection context - catalog cards are for browsing only

  useEffect(() => {
    const fetchCatalogs = async () => {
      try {
        const catalogsData = await getCatalogs();
        setCatalogs(catalogsData);
      } catch (error) {
        console.error('Error fetching catalogs:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCatalogs();
  }, []);

  // Handle catalog browsing - open modal
  const handleCatalogClick = (catalog: BackendCatalog) => {
    setSelectedCatalog(catalog);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedCatalog(null);
  };

  if (loading) return (
    <div className="flex justify-center items-center py-12">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-sky-500"></div>
    </div>
  );

  if (catalogs.length === 0) return (
    <div className="text-center py-10 bg-black/20 backdrop-blur-sm border border-gray-700 rounded-lg">
      <p className="text-gray-300">Ingen kataloger fundet.</p>
    </div>
  );

  return (
    <section className="space-y-8">
      <h2 className="text-2xl font-semibold text-white">Uploadede tilbudskataloger</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {catalogs.map((catalog) => {
          return (
            <div
              key={catalog.id}
              className="border border-gray-700/60 rounded-lg overflow-hidden shadow-lg bg-black/30 backdrop-blur-sm hover:bg-black/40 transition-all hover:scale-102 duration-300 group cursor-pointer"
              onClick={() => handleCatalogClick(catalog)}
            >
              <div className="relative">
                {catalog.first_page_image_url || catalog.store_logo_url ? (
                  <img
                    src={catalog.first_page_image_url || catalog.store_logo_url}
                    alt={catalog.title}
                    className="w-full h-48 object-cover opacity-90 group-hover:opacity-100 transition-opacity"
                  />
                ) : (
                  <div className="w-full h-48 bg-gray-800/50 flex items-center justify-center text-gray-300 text-sm">
                    Ingen billede
                  </div>
                )}
                <div className="absolute top-0 right-0 bg-sky-500/80 backdrop-blur-sm text-white px-2 py-1 text-xs font-semibold rounded-bl-lg">
                  {catalog.pages || '?'} sider
                </div>

              </div>
              <div className="p-4 space-y-2">
                <h3 className="font-medium text-lg leading-tight text-gray-100 group-hover:text-white truncate">{catalog.title}</h3>
                <p className="text-sm font-semibold text-sky-400">{catalog.store_name}</p>
                {(catalog.valid_from || catalog.valid_to) && (
                  <p className="text-xs text-gray-400">
                    Gyldig: {catalog.valid_from || '?'} - {catalog.valid_to || '?'}
                  </p>
                )}
                {catalog.products !== undefined && (
                  <p className="text-xs text-gray-300 flex items-center gap-1">
                    <span className={`inline-block w-2 h-2 rounded-full ${catalog.products > 0 ? 'bg-green-400' : 'bg-amber-400'}`}></span>
                    {catalog.products} produkter
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Flipbook Modal */}
      {selectedCatalog && (
        <FlipbookModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          pdfUrl={selectedCatalog.pdf_path ? `https://storage.googleapis.com/tilbudsjaegeren/${selectedCatalog.pdf_path}` : ''}
          title={selectedCatalog.title}
          storeName={selectedCatalog.store_name}
        />
      )}
    </section>
  );
}
