"use client";

import { useState, useEffect } from "react";
import { useAuth } from "lib/auth";
import { useRouter } from "next/navigation";

const SettingsPage = () => {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:8000';

  // Form state - Existing settings
  const [querySystemPrompt, setQuerySystemPrompt] = useState("");
  const [parserSystemPrompt, setParserSystemPrompt] = useState("");
  const [parsingModel, setParsingModel] = useState("");
  const [queryModel, setQueryModel] = useState("");
  const [initialRate, setInitialRate] = useState("10");
  const [maxRate, setMaxRate] = useState("30");
  const [minRate, setMinRate] = useState("1");
  const [showAiDebugOutput, setShowAiDebugOutput] = useState(false);

  // New Google GenAI settings
  const [temperature, setTemperature] = useState("0.7");
  const [topP, setTopP] = useState("0.95");
  const [topK, setTopK] = useState("20");
  const [maxOutputTokens, setMaxOutputTokens] = useState("2048");
  const [candidateCount, setCandidateCount] = useState("1");
  const [seed, setSeed] = useState("");
  const [stopSequences, setStopSequences] = useState("[]");
  const [presencePenalty, setPresencePenalty] = useState("0.0");
  const [frequencyPenalty, setFrequencyPenalty] = useState("0.0");

  // Safety settings
  const [safetyHateSpeech, setSafetyHateSpeech] = useState("BLOCK_ONLY_HIGH");
  const [safetyDangerousContent, setSafetyDangerousContent] = useState("BLOCK_ONLY_HIGH");
  const [safetyHarassment, setSafetyHarassment] = useState("BLOCK_ONLY_HIGH");
  const [safetySexuallyExplicit, setSafetySexuallyExplicit] = useState("BLOCK_ONLY_HIGH");

  // Advanced features
  const [groundingWithGoogleSearch, setGroundingWithGoogleSearch] = useState(false);
  const [responseMimeType, setResponseMimeType] = useState("text/plain");

  // Query processing limits
  const [maxContextLength, setMaxContextLength] = useState("800000");
  const [maxProductsPerCategory, setMaxProductsPerCategory] = useState("100");
  const [maxProductsReturned, setMaxProductsReturned] = useState("12");
  
  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [statusMessage, setStatusMessage] = useState<{ text: string; isError: boolean } | null>(null);
  const [models, setModels] = useState<string[]>([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  // Settings verification state
  const [effectiveSettings, setEffectiveSettings] = useState<string>("");
  const [isLoadingEffective, setIsLoadingEffective] = useState(false);
  const [testResults, setTestResults] = useState<string>("");
  const [isTestingSettings, setIsTestingSettings] = useState(false);

  // Redirect non-admin users
  useEffect(() => {
    if (!isLoading && (!user || !user.is_admin)) {
      router.push("/login");
    }
  }, [isLoading, user, router]);

  // Fetch current settings
  useEffect(() => {
    if (user?.is_admin) {
      fetchSettings();
      fetchModels();
    }
  }, [user]);

  const fetchSettings = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/settings`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      
      if (!response.ok) throw new Error("Failed to fetch settings");
      
      const data = await response.json();
      console.log("Fetched settings:", data); // Debug logging
      
      // Update state with fetched settings - Existing
      setQuerySystemPrompt(data.current_query_system_prompt || "");
      setParserSystemPrompt(data.current_parser_system_prompt || "");
      setParsingModel(data.current_parsing_model || "");
      setQueryModel(data.current_query_model || "");
      setInitialRate(data.current_initial_rate || "10");
      setMaxRate(data.current_max_rate || "30");
      setMinRate(data.current_min_rate || "1");
      setShowAiDebugOutput(data.show_ai_debug_output === true);

      // Update state with new Google GenAI settings
      setTemperature(data.temperature || "0.7");
      setTopP(data.top_p || "0.95");
      setTopK(data.top_k || "20");
      setMaxOutputTokens(data.max_output_tokens || "2048");
      setCandidateCount(data.candidate_count || "1");
      setSeed(data.seed || "");
      setStopSequences(data.stop_sequences || "[]");
      setPresencePenalty(data.presence_penalty || "0.0");
      setFrequencyPenalty(data.frequency_penalty || "0.0");

      // Safety settings
      setSafetyHateSpeech(data.safety_hate_speech || "BLOCK_ONLY_HIGH");
      setSafetyDangerousContent(data.safety_dangerous_content || "BLOCK_ONLY_HIGH");
      setSafetyHarassment(data.safety_harassment || "BLOCK_ONLY_HIGH");
      setSafetySexuallyExplicit(data.safety_sexually_explicit || "BLOCK_ONLY_HIGH");

      // Advanced features
      setGroundingWithGoogleSearch(data.grounding_with_google_search === "true");
      setResponseMimeType(data.response_mime_type || "text/plain");

      // Query processing limits
      setMaxContextLength(data.max_context_length || "800000");
      setMaxProductsPerCategory(data.max_products_per_category || "100");
      setMaxProductsReturned(data.max_products_returned || "12");
    } catch (error) {
      console.error("Error fetching settings:", error);
      setStatusMessage({
        text: "Error loading settings. Please try again.",
        isError: true,
      });
    }
  };

  const fetchModels = async () => {
    setIsLoadingModels(true);
    try {
      const response = await fetch(`${API_BASE}/models`);
      
      if (!response.ok) throw new Error("Failed to fetch models");
      
      const modelsList = await response.json();
      console.log("Fetched models:", modelsList); // Debug logging
      setModels(modelsList);
    } catch (error) {
      console.error("Error fetching models:", error);
    } finally {
      setIsLoadingModels(false);
    }
  };

  // Fetch effective settings from backend
  const fetchEffectiveSettings = async () => {
    setIsLoadingEffective(true);
    try {
      const response = await fetch(`${API_BASE}/api/settings/effective`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setEffectiveSettings(JSON.stringify(data, null, 2));
      } else {
        setEffectiveSettings(`Error fetching settings: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      setEffectiveSettings(`Error: ${error}`);
    } finally {
      setIsLoadingEffective(false);
    }
  };

  // Test settings usage
  const testSettingsUsage = async () => {
    setIsTestingSettings(true);
    try {
      const response = await fetch(`${API_BASE}/api/settings/test`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTestResults(JSON.stringify(data, null, 2));
      } else {
        setTestResults(`Error testing settings: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      setTestResults(`Error: ${error}`);
    } finally {
      setIsTestingSettings(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setStatusMessage(null);

    try {
      // Construct the settings payload
      const payload = {
        // Existing settings
        parsing_model: parsingModel,
        query_model: queryModel,
        query_system_prompt: querySystemPrompt,
        parser_system_prompt: parserSystemPrompt,
        initial_rate: initialRate,
        max_rate: maxRate,
        min_rate: minRate,
        show_ai_debug_output: showAiDebugOutput ? "true" : "false",

        // New Google GenAI settings
        temperature: temperature,
        top_p: topP,
        top_k: topK,
        max_output_tokens: maxOutputTokens,
        candidate_count: candidateCount,
        seed: seed,
        stop_sequences: stopSequences,
        presence_penalty: presencePenalty,
        frequency_penalty: frequencyPenalty,

        // Safety settings
        safety_hate_speech: safetyHateSpeech,
        safety_dangerous_content: safetyDangerousContent,
        safety_harassment: safetyHarassment,
        safety_sexually_explicit: safetySexuallyExplicit,

        // Advanced features
        grounding_with_google_search: groundingWithGoogleSearch ? "true" : "false",
        response_mime_type: responseMimeType,

        // Query processing limits (ensure strings)
        max_context_length: String(maxContextLength),
        max_products_per_category: String(maxProductsPerCategory),
        max_products_returned: String(maxProductsReturned),
      };

      // Send the settings to the backend
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/settings`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        // FastAPI 422 errors have a specific structure in data.detail
        if (response.status === 422 && Array.isArray(data.detail)) {
          const errorMessages = data.detail.map(
            (err: any) => `[${err.loc.join(' -> ')}] ${err.msg}`
          ).join('; ');
          throw new Error(`Validation failed: ${errorMessages}`);
        }
        // Handle other errors
        throw new Error(data.detail || `Request failed with status ${response.status}`);
      }

      setStatusMessage({
        text: data.message || "Settings updated successfully!",
        isError: false,
      });
    } catch (error) {
      console.error("Error saving settings:", error);
      setStatusMessage({
        text: error instanceof Error ? error.message : "An error occurred",
        isError: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || !user || !user.is_admin) {
    return <p className="text-center py-10">Indlæser...</p>;
  }

  return (
    <div className="max-w-5xl mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Application Settings
        </h1>
        <button
          onClick={() => router.push("/admin")}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
        >
          ← Back to Admin
        </button>
      </div>

      {statusMessage && (
        <div
          className={`mb-6 p-4 rounded-md ${
            statusMessage.isError
              ? "bg-red-50 text-red-800 border border-red-300"
              : "bg-green-50 text-green-800 border border-green-300"
          }`}
        >
          {statusMessage.text}
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* System Prompts */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold border-b pb-2">System Prompts</h2>
            <div>
              <label htmlFor="querySystemPrompt" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Query System Prompt
              </label>
              <textarea
                id="querySystemPrompt"
                value={querySystemPrompt}
                onChange={(e) => setQuerySystemPrompt(e.target.value)}
                rows={10}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Enter the system prompt for the query AI..."
              ></textarea>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                The base instructions given to the AI when answering user questions.
              </p>
            </div>
            <div>
              <label htmlFor="parserSystemPrompt" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Parser System Prompt
              </label>
              <textarea
                id="parserSystemPrompt"
                value={parserSystemPrompt}
                onChange={(e) => setParserSystemPrompt(e.target.value)}
                rows={10}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Enter the system prompt for the catalog parser AI..."
              ></textarea>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                The instructions given to the AI when extracting product data from catalog pages.
              </p>
            </div>
          </div>

          {/* Model Settings */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold border-b pb-2">Model Settings</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="parsingModel" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Default Parsing Model
                </label>
                <select
                  id="parsingModel"
                  value={parsingModel}
                  onChange={(e) => setParsingModel(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  disabled={isLoadingModels}
                >
                  <option value="">
                    {isLoadingModels ? "Loading models..." : "Select a parsing model"}
                  </option>
                  {models.map((model) => (
                    <option key={model} value={model}>
                      {model}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="queryModel" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Default Query Model
                </label>
                <select
                  id="queryModel"
                  value={queryModel}
                  onChange={(e) => setQueryModel(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  disabled={isLoadingModels}
                >
                  <option value="">
                    {isLoadingModels ? "Loading models..." : "Select a query model"}
                  </option>
                  {models.map((model) => (
                    <option key={model} value={model}>
                      {model}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Rate Limiting */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold border-b pb-2">Rate Limiting</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label htmlFor="initialRate" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Initial Rate (RPM)
                </label>
                <input
                  type="number"
                  id="initialRate"
                  value={initialRate}
                  onChange={(e) => setInitialRate(e.target.value)}
                  min="1"
                  max="60"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label htmlFor="maxRate" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Maximum Rate (RPM)
                </label>
                <input
                  type="number"
                  id="maxRate"
                  value={maxRate}
                  onChange={(e) => setMaxRate(e.target.value)}
                  min="1"
                  max="120"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label htmlFor="minRate" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Minimum Rate (RPM)
                </label>
                <input
                  type="number"
                  id="minRate"
                  value={minRate}
                  onChange={(e) => setMinRate(e.target.value)}
                  min="1"
                  max="30"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
          </div>

          {/* Google GenAI Generation Parameters */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold border-b pb-2">🤖 AI Generation Parameters</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label htmlFor="temperature" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Temperature
                </label>
                <input
                  id="temperature"
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={temperature}
                  onChange={(e) => setTemperature(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Controls randomness (0.0-2.0). Higher = more creative.
                </p>
              </div>

              <div>
                <label htmlFor="topP" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Top P
                </label>
                <input
                  id="topP"
                  type="number"
                  min="0"
                  max="1"
                  step="0.01"
                  value={topP}
                  onChange={(e) => setTopP(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Nucleus sampling (0.0-1.0). Lower = more focused.
                </p>
              </div>

              <div>
                <label htmlFor="topK" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Top K
                </label>
                <input
                  id="topK"
                  type="number"
                  min="1"
                  max="40"
                  value={topK}
                  onChange={(e) => setTopK(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Top-K sampling (1-40). Lower = more focused.
                </p>
              </div>

              <div>
                <label htmlFor="maxOutputTokens" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Max Output Tokens
                </label>
                <input
                  id="maxOutputTokens"
                  type="number"
                  min="1"
                  max="8192"
                  value={maxOutputTokens}
                  onChange={(e) => setMaxOutputTokens(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Maximum response length in tokens.
                </p>
              </div>

              <div>
                <label htmlFor="candidateCount" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Candidate Count
                </label>
                <input
                  id="candidateCount"
                  type="number"
                  min="1"
                  max="8"
                  value={candidateCount}
                  onChange={(e) => setCandidateCount(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Number of response candidates to generate.
                </p>
              </div>

              <div>
                <label htmlFor="seed" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Seed (Optional)
                </label>
                <input
                  id="seed"
                  type="number"
                  value={seed}
                  onChange={(e) => setSeed(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="Leave empty for random"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Seed for reproducible outputs.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="presencePenalty" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Presence Penalty
                </label>
                <input
                  id="presencePenalty"
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={presencePenalty}
                  onChange={(e) => setPresencePenalty(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Penalty for new topics (0.0-2.0).
                </p>
              </div>

              <div>
                <label htmlFor="frequencyPenalty" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Frequency Penalty
                </label>
                <input
                  id="frequencyPenalty"
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={frequencyPenalty}
                  onChange={(e) => setFrequencyPenalty(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Penalty for repetition (0.0-2.0).
                </p>
              </div>
            </div>

            <div>
              <label htmlFor="stopSequences" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Stop Sequences (JSON Array)
              </label>
              <textarea
                id="stopSequences"
                value={stopSequences}
                onChange={(e) => setStopSequences(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder='["STOP!", "END"]'
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                JSON array of strings that will stop generation when encountered.
              </p>
            </div>
          </div>

          {/* Safety Settings */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold border-b pb-2">🛡️ AI Safety Settings</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="safetyHateSpeech" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Hate Speech Protection
                </label>
                <select
                  id="safetyHateSpeech"
                  value={safetyHateSpeech}
                  onChange={(e) => setSafetyHateSpeech(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="BLOCK_NONE">Block None</option>
                  <option value="BLOCK_ONLY_HIGH">Block Only High</option>
                  <option value="BLOCK_MEDIUM_AND_ABOVE">Block Medium and Above</option>
                  <option value="BLOCK_LOW_AND_ABOVE">Block Low and Above</option>
                </select>
              </div>

              <div>
                <label htmlFor="safetyDangerousContent" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Dangerous Content Protection
                </label>
                <select
                  id="safetyDangerousContent"
                  value={safetyDangerousContent}
                  onChange={(e) => setSafetyDangerousContent(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="BLOCK_NONE">Block None</option>
                  <option value="BLOCK_ONLY_HIGH">Block Only High</option>
                  <option value="BLOCK_MEDIUM_AND_ABOVE">Block Medium and Above</option>
                  <option value="BLOCK_LOW_AND_ABOVE">Block Low and Above</option>
                </select>
              </div>

              <div>
                <label htmlFor="safetyHarassment" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Harassment Protection
                </label>
                <select
                  id="safetyHarassment"
                  value={safetyHarassment}
                  onChange={(e) => setSafetyHarassment(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="BLOCK_NONE">Block None</option>
                  <option value="BLOCK_ONLY_HIGH">Block Only High</option>
                  <option value="BLOCK_MEDIUM_AND_ABOVE">Block Medium and Above</option>
                  <option value="BLOCK_LOW_AND_ABOVE">Block Low and Above</option>
                </select>
              </div>

              <div>
                <label htmlFor="safetySexuallyExplicit" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Sexually Explicit Protection
                </label>
                <select
                  id="safetySexuallyExplicit"
                  value={safetySexuallyExplicit}
                  onChange={(e) => setSafetySexuallyExplicit(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="BLOCK_NONE">Block None</option>
                  <option value="BLOCK_ONLY_HIGH">Block Only High</option>
                  <option value="BLOCK_MEDIUM_AND_ABOVE">Block Medium and Above</option>
                  <option value="BLOCK_LOW_AND_ABOVE">Block Low and Above</option>
                </select>
              </div>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Configure how strictly the AI filters potentially harmful content. "Block Only High" is recommended for most use cases.
            </p>
          </div>

          {/* Advanced Features */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold border-b pb-2">🚀 Advanced Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="groundingWithGoogleSearch"
                  checked={groundingWithGoogleSearch}
                  onChange={(e) => setGroundingWithGoogleSearch(e.target.checked)}
                  className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 dark:border-gray-600"
                />
                <label htmlFor="groundingWithGoogleSearch" className="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Enable Google Search Grounding
                </label>
              </div>

              <div>
                <label htmlFor="responseMimeType" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Response Format
                </label>
                <select
                  id="responseMimeType"
                  value={responseMimeType}
                  onChange={(e) => setResponseMimeType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="text/plain">Plain Text</option>
                  <option value="application/json">JSON</option>
                  <option value="text/x.enum">Enum</option>
                </select>
              </div>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Google Search Grounding allows the AI to access real-time information. Response format controls the output structure.
            </p>
          </div>

          {/* Query Processing Limits */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold border-b pb-2">🔍 Query Processing Limits</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label htmlFor="maxContextLength" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Max Context Length
                </label>
                <input
                  type="number"
                  id="maxContextLength"
                  value={maxContextLength}
                  onChange={(e) => setMaxContextLength(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="800000"
                />
                <p className="text-xs text-gray-500 mt-1">Maximum characters to send to AI (modern LLMs can handle 800k+)</p>
              </div>

              <div>
                <label htmlFor="maxProductsPerCategory" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Max Products Per Category
                </label>
                <input
                  type="number"
                  id="maxProductsPerCategory"
                  value={maxProductsPerCategory}
                  onChange={(e) => setMaxProductsPerCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="100"
                />
                <p className="text-xs text-gray-500 mt-1">Products per category to include in AI context</p>
              </div>

              <div>
                <label htmlFor="maxProductsReturned" className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Max Products Returned
                </label>
                <input
                  type="number"
                  id="maxProductsReturned"
                  value={maxProductsReturned}
                  onChange={(e) => setMaxProductsReturned(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="12"
                />
                <p className="text-xs text-gray-500 mt-1">Target number of products for AI to return</p>
              </div>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              <strong>🚀 Performance Tip:</strong> Modern LLMs can handle much larger contexts. Increase these limits to get better, more comprehensive search results instead of artificially limiting the AI.
            </p>
          </div>

          {/* Debugging */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold border-b pb-2">🐛 Debugging</h2>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="showAiDebugOutput"
                checked={showAiDebugOutput}
                onChange={(e) => setShowAiDebugOutput(e.target.checked)}
                className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 dark:border-gray-600"
              />
              <label htmlFor="showAiDebugOutput" className="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Show AI Debug Output in Responses
              </label>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              If enabled, the raw `RELEVANT_PRODUCTS` JSON block from the AI will be visible in the chat response. Useful for debugging AI filtering.
            </p>
          </div>

          {/* Settings Verification */}
          <div className="space-y-6 border-t pt-6">
            <h2 className="text-xl font-semibold border-b pb-2">🔍 Settings Verification</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Verify that your settings are actually being used by the backend. This shows the live, effective configuration.
            </p>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Effective Settings */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Live Backend Settings
                  </label>
                  <button
                    type="button"
                    onClick={fetchEffectiveSettings}
                    disabled={isLoadingEffective}
                    className="px-3 py-1 text-xs font-medium text-white bg-blue-600 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {isLoadingEffective ? "Loading..." : "Refresh"}
                  </button>
                </div>
                <textarea
                  value={effectiveSettings}
                  readOnly
                  rows={12}
                  className="w-full px-3 py-2 text-xs font-mono border border-gray-300 rounded-md shadow-sm bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300"
                  placeholder="Click 'Refresh' to see live backend settings..."
                />
                <p className="text-xs text-gray-500">
                  These are the actual values the backend is using right now, with fallbacks applied.
                </p>
              </div>

              {/* Settings Test */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Settings Usage Test
                  </label>
                  <button
                    type="button"
                    onClick={testSettingsUsage}
                    disabled={isTestingSettings}
                    className="px-3 py-1 text-xs font-medium text-white bg-green-600 rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                  >
                    {isTestingSettings ? "Testing..." : "Test Settings"}
                  </button>
                </div>
                <textarea
                  value={testResults}
                  readOnly
                  rows={12}
                  className="w-full px-3 py-2 text-xs font-mono border border-gray-300 rounded-md shadow-sm bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300"
                  placeholder="Click 'Test Settings' to verify backend is using your settings..."
                />
                <p className="text-xs text-gray-500">
                  This simulates real backend operations and shows which settings were actually used.
                </p>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="pt-5 flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                isSubmitting ? "opacity-75 cursor-not-allowed" : ""
              }`}
            >
              {isSubmitting ? "Saving..." : "Save Settings"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SettingsPage;
