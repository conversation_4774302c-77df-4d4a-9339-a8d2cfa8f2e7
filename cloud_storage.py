"""
Google Cloud Storage helper module for Avis Scanner.
Handles all file uploads/downloads to eliminate local vs Render file path issues.
"""

import os
import logging
from pathlib import Path
from typing import Optional, List, Tuple
from google.cloud import storage
from google.auth.exceptions import DefaultCredentialsError
import tempfile

logger = logging.getLogger(__name__)

class CloudStorageManager:
    """Manages all file operations with Google Cloud Storage"""
    
    def __init__(self):
        self.bucket_name = os.getenv('GCS_BUCKET_NAME', 'tilbudsjaegeren')
        self.client = None
        self.bucket = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Google Cloud Storage client"""
        try:
            # Try to get credentials from environment
            credentials_json = os.getenv('GOOGLE_CLOUD_CREDENTIALS_JSON')
            if credentials_json:
                # Parse JSON credentials from environment variable
                import json
                from google.oauth2 import service_account
                credentials_info = json.loads(credentials_json)
                credentials = service_account.Credentials.from_service_account_info(credentials_info)
                self.client = storage.Client(credentials=credentials)
            else:
                # Fall back to default credentials (for local development with gcloud auth)
                self.client = storage.Client()
            
            self.bucket = self.client.bucket(self.bucket_name)
            logger.info(f"✅ Connected to Google Cloud Storage bucket: {self.bucket_name}")
            
        except DefaultCredentialsError:
            logger.error("❌ Google Cloud credentials not found. Set GOOGLE_CLOUD_CREDENTIALS_JSON environment variable.")
            self.client = None
            self.bucket = None
        except Exception as e:
            logger.error(f"❌ Failed to initialize Google Cloud Storage: {e}")
            self.client = None
            self.bucket = None
    
    def is_available(self) -> bool:
        """Check if cloud storage is available"""
        return self.client is not None and self.bucket is not None
    
    def upload_file(self, local_path: str, cloud_path: str) -> bool:
        """
        Upload a file to Google Cloud Storage

        Args:
            local_path: Path to local file
            cloud_path: Path in cloud storage (e.g., 'catalogs/bilka_20250627.pdf')

        Returns:
            True if successful, False otherwise

        Note: Files are automatically public if bucket has public IAM policy set.
        """
        if not self.is_available():
            logger.error("Cloud storage not available")
            return False

        try:
            blob = self.bucket.blob(cloud_path)
            blob.upload_from_filename(local_path)
            logger.info(f"✅ Uploaded {local_path} to gs://{self.bucket_name}/{cloud_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to upload {local_path}: {e}")
            return False

    def download_file(self, cloud_path: str, local_path: str) -> bool:
        """
        Download a file from Google Cloud Storage
        
        Args:
            cloud_path: Path in cloud storage
            local_path: Where to save the file locally
        
        Returns:
            True if successful, False otherwise
        """
        if not self.is_available():
            logger.error("Cloud storage not available")
            return False
        
        try:
            # Ensure local directory exists
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            blob = self.bucket.blob(cloud_path)
            blob.download_to_filename(local_path)
            logger.info(f"✅ Downloaded gs://{self.bucket_name}/{cloud_path} to {local_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to download {cloud_path}: {e}")
            return False
    
    def file_exists(self, cloud_path: str) -> bool:
        """Check if a file exists in cloud storage"""
        if not self.is_available():
            return False
        
        try:
            blob = self.bucket.blob(cloud_path)
            return blob.exists()
        except Exception as e:
            logger.error(f"❌ Failed to check if {cloud_path} exists: {e}")
            return False
    
    def list_files(self, prefix: str = "") -> List[str]:
        """List files in cloud storage with optional prefix"""
        if not self.is_available():
            return []
        
        try:
            blobs = self.bucket.list_blobs(prefix=prefix)
            return [blob.name for blob in blobs]
        except Exception as e:
            logger.error(f"❌ Failed to list files with prefix {prefix}: {e}")
            return []
    
    def delete_file(self, cloud_path: str) -> bool:
        """Delete a file from cloud storage"""
        if not self.is_available():
            return False

        try:
            blob = self.bucket.blob(cloud_path)
            if blob.exists():
                blob.delete()
                logger.info(f"✅ Deleted gs://{self.bucket_name}/{cloud_path}")
                return True
            else:
                logger.warning(f"⚠️ File not found for deletion: gs://{self.bucket_name}/{cloud_path}")
                return True  # Consider it "deleted" if it doesn't exist
        except Exception as e:
            logger.error(f"❌ Failed to delete {cloud_path}: {e}")
            return False

    def delete_folder(self, folder_prefix: str) -> int:
        """
        Delete all files in a folder (prefix)

        Args:
            folder_prefix: Folder path like 'images/catalog_123/'

        Returns:
            Number of files deleted
        """
        if not self.is_available():
            return 0

        try:
            # Ensure folder_prefix ends with /
            if not folder_prefix.endswith('/'):
                folder_prefix += '/'

            blobs = list(self.bucket.list_blobs(prefix=folder_prefix))
            deleted_count = 0

            for blob in blobs:
                try:
                    blob.delete()
                    logger.info(f"✅ Deleted gs://{self.bucket_name}/{blob.name}")
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"❌ Failed to delete {blob.name}: {e}")

            logger.info(f"✅ Deleted {deleted_count} files from folder: {folder_prefix}")
            return deleted_count

        except Exception as e:
            logger.error(f"❌ Failed to delete folder {folder_prefix}: {e}")
            return 0

    def list_files_by_pattern(self, prefix: str = "", suffix: str = "") -> List[Tuple[str, dict]]:
        """
        List files matching pattern with metadata

        Returns:
            List of (filename, metadata) tuples
        """
        if not self.is_available():
            return []

        try:
            blobs = self.bucket.list_blobs(prefix=prefix)
            results = []

            for blob in blobs:
                if suffix and not blob.name.endswith(suffix):
                    continue

                metadata = {
                    'size': blob.size,
                    'created': blob.time_created,
                    'updated': blob.updated,
                    'content_type': blob.content_type
                }
                results.append((blob.name, metadata))

            return results

        except Exception as e:
            logger.error(f"❌ Failed to list files with pattern prefix={prefix}, suffix={suffix}: {e}")
            return []
    
    def get_temp_download_path(self, cloud_path: str) -> str:
        """Get a temporary local path for downloading a cloud file"""
        filename = os.path.basename(cloud_path)
        temp_dir = tempfile.gettempdir()
        return os.path.join(temp_dir, f"avis_temp_{filename}")

# Global instance
cloud_storage = CloudStorageManager()

def upload_catalog_pdf(local_pdf_path: str, store_name: str, valid_from: str, valid_to: str) -> Optional[str]:
    """
    Upload a catalog PDF to cloud storage using StoragePaths for consistent path construction

    Returns:
        Cloud path if successful, None otherwise
    """
    from storage_factory import StoragePaths
    from datetime import datetime

    filename = f"{store_name.lower().replace(' ', '_')}_{valid_from}_{valid_to}.pdf"

    try:
        # Convert string dates to datetime objects for StoragePaths
        valid_from_date = datetime.strptime(valid_from, '%Y%m%d').date()
        valid_to_date = datetime.strptime(valid_to, '%Y%m%d').date()

        # Generate proper cloud storage path using StoragePaths
        country_code = StoragePaths.detect_country_code(store_name)
        date_period = StoragePaths.generate_date_period(valid_from_date, valid_to_date)
        cloud_path = StoragePaths.catalog_path(
            filename=filename,
            country_code=country_code,
            store_name=store_name,
            date_period=date_period
        )
    except Exception as e:
        logger.warning(f"Failed to generate StoragePaths for {filename}, falling back to legacy: {e}")
        cloud_path = f"catalogs/{filename}"

    if cloud_storage.upload_file(local_pdf_path, cloud_path):
        return cloud_path
    return None

def download_catalog_pdf(cloud_path: str) -> Optional[str]:
    """
    Download a catalog PDF from cloud storage to temporary location
    
    Returns:
        Local temp path if successful, None otherwise
    """
    temp_path = cloud_storage.get_temp_download_path(cloud_path)
    
    if cloud_storage.download_file(cloud_path, temp_path):
        return temp_path
    return None

def upload_catalog_images(local_image_dir: str, catalog_id: int, store_name: str = None, valid_from=None, valid_to=None) -> List[str]:
    """
    Upload all images from a catalog extraction to cloud storage using StoragePaths

    Args:
        local_image_dir: Directory containing images to upload
        catalog_id: Catalog ID for legacy fallback
        store_name: Store name for StoragePaths (optional)
        valid_from: Valid from date for StoragePaths (optional)
        valid_to: Valid to date for StoragePaths (optional)

    Returns:
        List of cloud paths for uploaded images
    """
    from storage_factory import StoragePaths

    uploaded_paths = []
    image_dir = Path(local_image_dir)

    if not image_dir.exists():
        logger.warning(f"Image directory does not exist: {local_image_dir}")
        return uploaded_paths

    for image_file in image_dir.glob("*.png"):
        if store_name and valid_from and valid_to:
            try:
                # Use StoragePaths for consistent path construction
                country_code = StoragePaths.detect_country_code(store_name)
                date_period = StoragePaths.generate_date_period(valid_from, valid_to)
                cloud_path = StoragePaths.image_path(
                    filename=image_file.name,
                    country_code=country_code,
                    store_name=store_name,
                    date_period=date_period
                )
            except Exception as e:
                logger.warning(f"Failed to generate StoragePaths for {image_file.name}, falling back to legacy: {e}")
                cloud_path = f"images/catalog_{catalog_id}/{image_file.name}"
        else:
            # Legacy fallback when store info not available
            cloud_path = f"images/catalog_{catalog_id}/{image_file.name}"

        if cloud_storage.upload_file(str(image_file), cloud_path):
            uploaded_paths.append(cloud_path)

    return uploaded_paths

def delete_catalog_files(catalog_pdf_path: str, catalog_id: int) -> dict:
    """
    Delete all files associated with a catalog (PDF + images)

    Args:
        catalog_pdf_path: Cloud path to PDF (e.g., 'catalogs/bilka_20250627.pdf')
        catalog_id: Catalog ID for finding images

    Returns:
        Dict with deletion results
    """
    results = {
        'pdf_deleted': False,
        'images_deleted': 0,
        'errors': []
    }

    # Delete PDF file
    if catalog_pdf_path:
        try:
            results['pdf_deleted'] = cloud_storage.delete_file(catalog_pdf_path)
        except Exception as e:
            results['errors'].append(f"PDF deletion error: {e}")

    # Delete image folder
    try:
        image_folder = f"images/catalog_{catalog_id}/"
        results['images_deleted'] = cloud_storage.delete_folder(image_folder)
    except Exception as e:
        results['errors'].append(f"Image deletion error: {e}")

    return results
