import Grid from 'components/grid';
import ProductGridItems from 'components/layout/product-grid-items';
import EnhancedProductGrid from 'components/layout/enhanced-product-grid';
import { defaultSort, sorting } from 'lib/constants';
import { getProducts } from 'lib/backend';

export const metadata = {
  title: 'Search',
  description: 'Search for products in the store.'
};

export default async function SearchPage(props: {
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const searchParams = await props.searchParams;
  const { sort, q: searchValue } = searchParams as { [key: string]: string };
  // Sorting not yet implemented for backend; kept for future
const { sortKey, reverse } = sorting.find((item) => item.slug === sort) || defaultSort;

  const products = await getProducts(searchValue);
  const resultsText = products.length > 1 ? 'results' : 'result';

  return (
    <>
      {searchValue ? (
        <p className="mb-4">
          {products.length === 0
            ? 'There are no products that match '
            : `Showing ${products.length} ${resultsText} for `}
          <span className="font-bold">&quot;{searchValue}&quot;</span>
        </p>
      ) : null}
      {products.length > 0 ? (
        <EnhancedProductGrid
          products={products}
          title={searchValue ? `Search Results for "${searchValue}"` : "All Products"}
          showCount={true}
        />
      ) : null}
    </>
  );
}
