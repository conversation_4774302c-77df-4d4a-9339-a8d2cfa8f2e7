from fastapi import <PERSON><PERSON><PERSON>, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, RedirectResponse
import uvicorn
import os
from sqlalchemy.orm import Session
import logging
from database import init_db, get_db, Store, Catalog

# Configure logging with optimized format
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   datefmt='%d-%m %H:%M:%S')  # Optimized: dd-mm hh:mm:ss format saves tokens
logger = logging.getLogger(__name__)

# Create only local directories needed for web assets (not catalogs/images - those use cloud storage)
os.makedirs("templates", exist_ok=True)
os.makedirs("static", exist_ok=True)

# Initialize database
init_db()

# Create FastAPI app
app = FastAPI(title="Danish Supermarket Offers")

# CORS Middleware
origins = [
    "http://localhost:3000",  # Next.js frontend
    "http://localhost:6969",  # The backend itself
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
);

# Mount static files (images and catalogs are served from cloud storage via API endpoints)
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

# Import API routes after app creation
from api import app as api_app
for route in api_app.routes:
    app.routes.append(route)

@app.get("/", response_class=HTMLResponse)
async def home(request: Request, db: Session = Depends(get_db)):
    # Get all stores
    stores = db.query(Store).all()
    
    # Get all catalogs with related data
    catalogs = db.query(Catalog).all()
    
    # Get default model names from database settings
    default_parsing_model = config.get_setting_from_db('GEMINI_PARSING_MODEL', 'models/gemini-1.5-flash-latest')
    default_query_model = config.get_setting_from_db('GEMINI_QUERY_MODEL', 'models/gemini-1.5-flash-latest')
    
    # Add default models to the context
    context = {
        "request": request, 
        "stores": stores, 
        "catalogs": catalogs,
        "default_parsing_model": default_parsing_model,
        "default_query_model": default_query_model
    }
    
    return templates.TemplateResponse("index.html", context)

@app.get("/catalog/{catalog_id}", response_class=HTMLResponse)
async def view_catalog(request: Request, catalog_id: int, db: Session = Depends(get_db)):
    catalog = db.query(Catalog).filter(Catalog.id == catalog_id).first()
    if not catalog:
        return RedirectResponse(url="/")
        
    # Also pass default models here in case we want selectors on this page later
    default_parsing_model = config.get_setting_from_db('GEMINI_PARSING_MODEL', 'models/gemini-1.5-flash-latest')
    default_query_model = config.get_setting_from_db('GEMINI_QUERY_MODEL', 'models/gemini-1.5-flash-latest')
    
    context = {
        "request": request,
        "catalog": catalog,
        "stores": db.query(Store).all(),
        "catalogs": [catalog],  # Just show this catalog
        "default_parsing_model": default_parsing_model,
        "default_query_model": default_query_model
    }
    return templates.TemplateResponse("index.html", context) # Still renders index for now

if __name__ == "__main__":
    logger.info("Starting Danish Supermarket Offers application")
    logger.info("Access the web interface at http://localhost:8001")
    logger.info("API documentation available at http://localhost:8001/api/docs")
    uvicorn.run(app, host="0.0.0.0", port=8001) 