#!/usr/bin/env python3
"""
Test script to verify Google Cloud Storage setup and create bucket if needed.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from cloud_storage import cloud_storage
    from google.cloud import storage
    import json
    from google.oauth2 import service_account
except ImportError as e:
    print(f"❌ Missing required packages: {e}")
    print("   Run: pip install google-cloud-storage")
    sys.exit(1)

def test_cloud_storage_setup():
    """Test the cloud storage setup and create bucket if needed"""
    
    print("🔧 Testing Google Cloud Storage Setup")
    print("=" * 50)
    
    # Test 1: Check credentials
    print("\n1. Checking credentials...")
    credentials_json = os.getenv('GOOGLE_CLOUD_CREDENTIALS_JSON')
    if not credentials_json:
        print("❌ GOOGLE_CLOUD_CREDENTIALS_JSON not found in environment")
        return False
    
    try:
        credentials_info = json.loads(credentials_json)
        project_id = credentials_info.get('project_id')
        client_email = credentials_info.get('client_email')
        print(f"✅ Credentials loaded successfully")
        print(f"   Project ID: {project_id}")
        print(f"   Service Account: {client_email}")
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in credentials: {e}")
        return False
    
    # Test 2: Initialize client
    print("\n2. Initializing Google Cloud Storage client...")
    try:
        credentials = service_account.Credentials.from_service_account_info(credentials_info)
        client = storage.Client(credentials=credentials, project=project_id)
        print("✅ Client initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize client: {e}")
        return False
    
    # Test 3: Check/Create bucket
    bucket_name = os.getenv('GCS_BUCKET_NAME', 'avis-scanner-storage')
    print(f"\n3. Checking bucket: {bucket_name}")
    
    try:
        bucket = client.bucket(bucket_name)
        if bucket.exists():
            print(f"✅ Bucket '{bucket_name}' already exists")
        else:
            print(f"📦 Creating bucket '{bucket_name}'...")
            # Create bucket in EU region for better performance with Render
            bucket = client.create_bucket(bucket_name, location='EU')
            print(f"✅ Bucket '{bucket_name}' created successfully")
    except Exception as e:
        print(f"❌ Bucket operation failed: {e}")
        print("   This might be due to:")
        print("   - Bucket name already taken globally")
        print("   - Insufficient permissions")
        print("   - Billing not enabled on the project")
        return False
    
    # Test 4: Test file operations
    print(f"\n4. Testing file operations...")
    test_content = "Hello from Avis Scanner!"
    test_blob_name = "test/connection_test.txt"
    
    try:
        # Upload test file
        blob = bucket.blob(test_blob_name)
        blob.upload_from_string(test_content)
        print(f"✅ Test file uploaded: gs://{bucket_name}/{test_blob_name}")
        
        # Download test file
        downloaded_content = blob.download_as_text()
        if downloaded_content == test_content:
            print("✅ Test file downloaded and verified")
        else:
            print("❌ Downloaded content doesn't match")
            return False
        
        # Clean up test file
        blob.delete()
        print("✅ Test file cleaned up")
        
    except Exception as e:
        print(f"❌ File operations failed: {e}")
        return False
    
    # Test 5: Test our cloud storage manager
    print(f"\n5. Testing cloud storage manager...")
    if cloud_storage.is_available():
        print("✅ Cloud storage manager is available")
        
        # List existing files
        files = cloud_storage.list_files()
        print(f"   Current files in bucket: {len(files)}")
        if files:
            print(f"   Sample files: {files[:3]}")
    else:
        print("❌ Cloud storage manager not available")
        return False
    
    print("\n🎉 SUCCESS! Google Cloud Storage is fully configured and working!")
    print("\n📋 Next Steps:")
    print("   1. Update scrapers to upload PDFs to cloud storage")
    print("   2. Update parser to download PDFs from cloud storage")
    print("   3. Test end-to-end pipeline")
    
    return True

if __name__ == "__main__":
    success = test_cloud_storage_setup()
    if not success:
        print("\n💡 Troubleshooting:")
        print("   1. Ensure Google Cloud billing is enabled")
        print("   2. Try a different bucket name (must be globally unique)")
        print("   3. Check service account permissions")
        sys.exit(1)
    else:
        print("\n✅ Ready to proceed with cloud storage integration!")
