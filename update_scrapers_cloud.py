#!/usr/bin/env python3
"""
<PERSON><PERSON>t to update all scrapers to use cloud storage instead of local file storage.
This script will modify all scraper files to upload PDFs to cloud storage after download.
"""

import os
import re
from pathlib import Path

def update_scraper_file(scraper_path: Path) -> bool:
    """Update a single scraper file to use cloud storage"""
    
    print(f"📝 Updating {scraper_path.name}...")
    
    try:
        # Read the current file
        with open(scraper_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Pattern to find the download save section
        # Look for: await download.save_as(local_pdf_path)
        # Followed by: catalog_data = {...}
        # Followed by: self.collected_catalogs.append(catalog_data)
        
        pattern = r'(await download\.save_as\(local_pdf_path\)\s*\n\s*self\.logger\.info\([^)]+\)\s*\n\s*)(catalog_data = \{[^}]+\})\s*(\n\s*self\.collected_catalogs\.append\(catalog_data\))'
        
        def replacement(match):
            save_line = match.group(1)
            catalog_data = match.group(2)
            append_line = match.group(3)
            
            return f"""{save_line}
            # Create initial catalog data
            {catalog_data}
            
            # Upload to cloud storage
            cloud_path = self.upload_pdf_to_cloud(str(local_pdf_path), catalog_data)
            if cloud_path:
                catalog_data["cloud_pdf_path"] = cloud_path
                self.logger.info(f"{scraper_path.stem.title()}: PDF uploaded to cloud storage: {{cloud_path}}")
            else:
                self.logger.warning(f"{scraper_path.stem.title()}: Failed to upload PDF to cloud storage")
            {append_line}
            
            # Clean up local file after cloud upload
            try:
                local_pdf_path.unlink()
                self.logger.debug(f"{scraper_path.stem.title()}: Cleaned up local PDF: {{local_pdf_path}}")
            except Exception as cleanup_error:
                self.logger.warning(f"{scraper_path.stem.title()}: Failed to clean up local PDF: {{cleanup_error}}")"""
        
        # Apply the replacement
        updated_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # Check if any changes were made
        if updated_content == content:
            print(f"⚠️  No changes needed for {scraper_path.name}")
            return True
        
        # Write the updated content back
        with open(scraper_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print(f"✅ Updated {scraper_path.name}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating {scraper_path.name}: {e}")
        return False

def update_all_scrapers():
    """Update all scraper files in the store_scrapers directory"""
    
    print("🔄 Updating all scrapers to use cloud storage...")
    print("=" * 60)
    
    scrapers_dir = Path("scrapers/store_scrapers")
    if not scrapers_dir.exists():
        print(f"❌ Scrapers directory not found: {scrapers_dir}")
        return False
    
    success_count = 0
    total_count = 0
    
    # Process all Python files in the scrapers directory
    for scraper_file in scrapers_dir.glob("*_scraper.py"):
        total_count += 1
        if update_scraper_file(scraper_file):
            success_count += 1
    
    print(f"\n📊 Results: {success_count}/{total_count} scrapers updated successfully")
    
    if success_count == total_count:
        print("🎉 All scrapers updated successfully!")
        return True
    else:
        print("⚠️  Some scrapers failed to update")
        return False

def manual_update_patterns():
    """Manually update specific patterns that the regex might miss"""
    
    print("\n🔧 Applying manual updates for complex patterns...")
    
    # List of scrapers that might need manual updates
    manual_updates = [
        "scrapers/store_scrapers/rema1000_scraper.py",
        "scrapers/store_scrapers/superbrugsen_scraper.py",
        "scrapers/store_scrapers/brugsen_scraper.py"
    ]
    
    for scraper_path in manual_updates:
        if Path(scraper_path).exists():
            print(f"📝 Checking {scraper_path} for manual updates...")
            # Add specific manual updates here if needed
    
    print("✅ Manual updates complete")

def main():
    """Main function"""
    
    print("🚀 Scraper Cloud Storage Migration")
    print("=" * 60)
    
    # Step 1: Update all scrapers automatically
    if not update_all_scrapers():
        print("❌ Automatic updates failed")
        return False
    
    # Step 2: Apply manual updates for complex cases
    manual_update_patterns()
    
    print("\n🎉 Scraper migration complete!")
    print("\n📋 Next steps:")
    print("   1. Test scrapers to ensure cloud upload works")
    print("   2. Update catalog processor to use cloud storage")
    print("   3. Update PDF extractor to download from cloud")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
