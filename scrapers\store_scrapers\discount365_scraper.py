# File: scrapers/store_scrapers/discount365_scraper.py

import asyncio
import re
from pathlib import Path
from typing import List, Dict, Any, Optional

from playwright_stealth import Stealth
from ..scraper_core import Base<PERSON><PERSON>raper, PlaywrightTimeoutError, PlaywrightError, Page


class Discount365Scraper(BaseScraper):
    """
    Scraper for 365discount catalogs.
    Assumed to be very similar to Brugsen/SuperBrugsen (Coop ShopGun platform).
    """

    def __init__(self, config: dict):
        super().__init__(config)
        self.logger.info(f"Discount365Scraper initialized for store: {self.store_name}") # Changed log

    async def scrape_catalogs(self) -> List[Dict[str, Any]]:
        self.logger.info("🚀 STARTING scrape_catalogs method")
        if not self.page or not self.context or not self.catalog_list_url:
            self.logger.error("Page, context, or catalog_list_url not available for Discount365Scraper.") # Changed log
            return []

        # Set timeouts
        try:
            default_nav_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            default_el_timeout = self.config.get('behavior_flags', {}).get('element_wait_timeout_ms', 30000)
            if self.page:
                self.page.set_default_navigation_timeout(default_nav_timeout)
                self.page.set_default_timeout(default_el_timeout)
                self.logger.info(f"Discount365Scraper: Page timeouts set.") # Changed log
        except Exception as e_timeout_set:
            self.logger.error(f"Discount365Scraper: Error setting default timeouts: {e_timeout_set}") # Changed log
            return []

        # Apply stealth patches to avoid detection
        try:
            self.logger.info("365discount: Applying stealth patches...")
            stealth = Stealth()
            await stealth.apply_stealth_async(self.page)
            self.logger.info("365discount: Stealth patches applied successfully")
        except Exception as stealth_error:
            self.logger.warning(f"365discount: Failed to apply stealth patches: {stealth_error}")
            # Continue anyway, stealth is not critical

        # Block window.close() calls that might close our page
        await self.page.add_init_script("""
            // Block window.close() calls
            window.close = function() {
                console.log('Blocked window.close() call');
            };

            // Also block any attempts to close via other methods
            if (window.parent && window.parent.close) {
                window.parent.close = function() {
                    console.log('Blocked parent.close() call');
                };
            }
        """)

        # 1. Navigate to 365discount /365avis/ page (ShopGun viewer)
        self.logger.info(f"365discount: Navigating to /365avis/ page: {self.catalog_list_url}")
        self.logger.info(f"365discount: Page state before navigation - closed: {self.page.is_closed()}, url: {self.page.url}")

        # Add try/finally guard to keep browser alive
        try:
            try:
                nav_success = await self._navigate_to_url(self.page, self.catalog_list_url)
                self.logger.info(f"🧭 _navigate_to_url returned: {nav_success}")
                if not nav_success:
                    self.logger.error("365discount: Navigation failed")
                    return []

                self.logger.info("365discount: Navigation successful, checking page state...")
                self.logger.info("🔍 ABOUT TO CHECK PAGE STATE - this is where it might crash")
            except Exception as nav_exception:
                self.logger.error(f"💥 CAUGHT EXCEPTION DURING/AFTER NAVIGATION: {nav_exception}", exc_info=True)
                self.logger.error(f"📄 Page state during nav exception - closed: {self.page.is_closed() if self.page else 'No page'}")
                return []
        finally:
            if self.page and not self.page.is_closed():
                self.logger.info("🛡️ try/finally guard: Page is still alive!")
            else:
                self.logger.error("💀 try/finally guard: Page was closed!")

        try:
            page_closed = self.page.is_closed()
            page_url = self.page.url
            self.logger.info(f"365discount: Page state after navigation - closed: {page_closed}, url: {page_url}")

            if page_closed:
                self.logger.error("365discount: Page was closed after navigation")
                return []
        except Exception as e:
            self.logger.error(f"365discount: Error checking page state: {e}")
            return []

        # Replace blind sleep with visual wait for page stability
        self.logger.info("365discount: Waiting for DOM content to be fully loaded...")
        try:
            await self.page.wait_for_load_state("domcontentloaded", timeout=15000)
            self.logger.info("365discount: DOM content loaded - page should be stable")

            # Add a small additional wait for any dynamic content
            await asyncio.sleep(1)
            self.logger.info("365discount: Page settling complete")
        except Exception as e:
            self.logger.error(f"365discount: Error during page load wait: {e}")
            return []

        # 2. Handle Cookies - RE-ENABLED NOW THAT PAGE STAYS OPEN
        self.logger.info("365discount: Starting cookie handling...")
        self.logger.info(f"365discount: Page state before cookies - closed: {self.page.is_closed()}, url: {self.page.url}")

        cookie_selectors = self.config.get('selectors', {}).get('cookie_accept_selectors', [])
        if cookie_selectors:
            try:
                cookie_result = await self._handle_cookies(self.page, cookie_selectors)
                self.logger.info(f"365discount: Cookie handling result: {cookie_result}")
            except Exception as e:
                self.logger.error(f"365discount: Error during cookie handling: {e}")
                # Continue anyway, cookies might not be critical
        else:
            self.logger.info("365discount: No cookie selectors configured")

        self.logger.info("365discount: Checking page state after cookie handling...")
        self.logger.info(f"365discount: Page state after cookies - closed: {self.page.is_closed()}, url: {self.page.url}")
        if self.page.is_closed():
            self.logger.error("365discount: Page was closed after cookie handling")
            return []

        # 3. Wait for catalog viewer to load then immediately proceed
        self.logger.info("365discount: Waiting for catalog viewer to load completely...")
        try:
            # Use 'domcontentloaded' instead of 'networkidle' to avoid page closure
            await self.page.wait_for_load_state('domcontentloaded', timeout=15000)
            self.logger.info("365discount: DOM content loaded")

            # Add a simple time-based wait instead of networkidle
            await asyncio.sleep(3)
            self.logger.info("365discount: Catalog viewer should be ready")
        except Exception as e:
            self.logger.warning(f"365discount: Timeout waiting for catalog viewer to load, continuing anyway: {e}")

        # Skip the wait - proceed immediately since page closes quickly
        self.logger.info("365discount: Proceeding immediately to find PDF trigger button (no wait)")

        # 4. Extract Date from this /365avis/ page
        raw_date_info = "365discount_Unknown_Date" # Changed default
        display_title = f"{self.store_name} Catalog"

        date_on_viewer_selector = self.config.get('selectors', {}).get('date_extraction_selector')
        if date_on_viewer_selector:
            try:
                date_el = self.page.locator(date_on_viewer_selector).first
                await date_el.wait_for(state="visible", timeout=10000)
                text_content = await date_el.text_content()
                if text_content: raw_date_info = text_content.strip()
                self.logger.info(f"365discount: Date from viewer page: '{raw_date_info}'") # Changed log
                if "Unknown_Date" not in raw_date_info:
                    display_title = f"{self.store_name} Catalog ({raw_date_info.split('-')[0].strip()})"
            except Exception as e:
                self.logger.warning(f"365discount: Error extracting date from viewer page: {e}") # Changed log

        # 4. Click the ShopGun PDF trigger (e.g., SVG icon) on the /365avis/ page
        shopgun_pdf_trigger_selector = self.config.get('selectors', {}).get('shopgun_viewer_pdf_trigger_selector')
        final_download_button_selector = self.config.get('selectors', {}).get('final_pdf_download_button_selector')

        if not shopgun_pdf_trigger_selector and not final_download_button_selector:
            self.logger.error("365discount: Neither 'shopgun_viewer_pdf_trigger_selector' nor 'final_pdf_download_button_selector' are configured.") # Changed log
            return []
        
        current_page_for_download = self.page

        if shopgun_pdf_trigger_selector:
            try:
                # Check if page is still alive before proceeding
                if self.page.is_closed():
                    self.logger.error("365discount: Page was closed before PDF trigger interaction")
                    return []

                # Ready to find the PDF trigger
                self.logger.info("365discount: Looking for PDF trigger element")

                self.logger.info(f"365discount Viewer: Looking for PDF trigger element: '{shopgun_pdf_trigger_selector}'")
                pdf_trigger_el = self.page.locator(shopgun_pdf_trigger_selector).first

                # Check if element exists before waiting
                element_count = await pdf_trigger_el.count()
                self.logger.info(f"365discount: Found {element_count} elements matching trigger selector")

                if element_count == 0:
                    self.logger.error("365discount: No PDF trigger elements found on page")
                    return []

                self.logger.info(f"365discount Viewer: Waiting for PDF trigger element to be visible")
                await pdf_trigger_el.wait_for(state="visible", timeout=15000)

                # Click and wait for navigation to complete
                await pdf_trigger_el.click(timeout=5000)

                # Wait for the page to load after navigation
                try:
                    await self.page.wait_for_load_state('load', timeout=self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000))
                    self.logger.info(f"365discount: Successfully navigated after PDF trigger click. New URL: {self.page.url}")
                except Exception as nav_error:
                    self.logger.warning(f"365discount: Navigation wait failed: {nav_error}")
                    # Continue anyway, the page might have loaded

                # Additional wait time after ShopGun PDF trigger click
                wait_after_trigger = self.config.get('behavior_flags', {}).get('wait_after_viewer_pdf_trigger_click_ms', 2000)
                self.logger.info(f"365discount: Waiting additional {wait_after_trigger}ms after ShopGun trigger click")
                await asyncio.sleep(wait_after_trigger/1000)  # Convert ms to seconds

                self.logger.info(f"365discount: Clicked ShopGun PDF trigger. New URL for download: {self.page.url}")

            except Exception as e:
                self.logger.error(f"365discount Viewer: Error clicking ShopGun PDF trigger '{shopgun_pdf_trigger_selector}': {e}")
                return []
        
        # 5. Locate ShopGun Iframe and click the final download button
        if not final_download_button_selector:
            self.logger.error("365discount: 'final_pdf_download_button_selector' not configured.")
            return []

        download_url: Optional[str] = None
        try:
            self.logger.info(f"365discount: Attempting to locate ShopGun iframe on page {self.page.url}")
            # Check if page is still alive before proceeding
            if self.page.is_closed():
                self.logger.error("365discount: Page was closed before final download button interaction")
                return []

            navigation_timeout = self.config.get('behavior_flags', {}).get('navigation_timeout_ms', 60000)
            click_timeout       = self.config.get('behavior_flags', {}).get('element_click_timeout_ms', 10000)

            try:
                # Skip waiting for catalog content - just proceed directly to download button
                self.logger.info(f"365discount: 🚀 Proceeding directly to download button on {self.page.url}")

                # Give the page a moment to settle after navigation
                await asyncio.sleep(2)



                # The PDF viewer is not in an iframe. Locate the download button directly on the page.
                self.logger.info(f"365discount: Locating final PDF download button directly on page using selector: '{final_download_button_selector}'")
                download_button = self.page.locator(final_download_button_selector).first
                await download_button.wait_for(state="visible", timeout=20000)
                self.logger.info("365discount: Clicking download button and capturing resulting PDF URL...")

                # Try download approach first (for direct file downloads)
                try:
                    self.logger.info("365discount: Clicking download button and waiting for download event...")
                    async with self.page.expect_download(timeout=self.config.get('behavior_flags', {}).get('download_event_timeout_ms', 60000)) as download_info:
                        await download_button.click(timeout=click_timeout)
                    download = await download_info.value
                    download_url = download.url
                    self.logger.info(f"365discount: Direct download captured: {download_url}")

                except PlaywrightTimeoutError:
                    # Fallback: Handle navigation to S3 PDF URL (not a download event)
                    self.logger.info("365discount: Download failed, trying navigation to S3 URL...")

                    # Wait for navigation to S3 PDF URL
                    async with self.page.expect_navigation(timeout=navigation_timeout) as navigation_info:
                        await download_button.click(timeout=click_timeout)

                    response = await navigation_info.value
                    download_url = response.url
                    self.logger.info(f"365discount: Successfully navigated to S3 PDF URL: {download_url}")

                # PDF navigation successful - no need to wait here

                # Verify we got to a PDF URL (could be intermediate or final)
                if '365avisen-pdf' in download_url:
                    self.logger.info(f"365discount: Got intermediate PDF URL: {download_url}")
                elif 'amazonaws.com' in download_url or 'sgn-prd-assets' in download_url:
                    self.logger.info(f"365discount: Got final AWS PDF URL: {download_url}")
                else:
                    self.logger.warning(f"365discount: Final URL doesn't look like a PDF URL: {download_url}")
                    # But continue anyway - might still be valid

            except PlaywrightTimeoutError:
                # Fallback: try popup approach for older sites
                try:
                    async with self.page.context.expect_page(timeout=5000) as popup_info:
                        await download_button.click(timeout=click_timeout)
                    popup_page = await popup_info.value
                    await popup_page.wait_for_load_state('load', timeout=navigation_timeout)
                    download_url = popup_page.url

                except PlaywrightTimeoutError:
                    raise PlaywrightTimeoutError("Failed to capture PDF URL via navigation or popup")

            # For COOP sites, PDF URLs are AWS S3 URLs that don't end with .pdf
            # Validate that we have a valid HTTP URL
            if not download_url or not download_url.startswith('http'):
                raise ValueError(f"Invalid or non-HTTP PDF URL obtained: {download_url}")

            # Check if it's an AWS S3 URL (common for COOP sites) or ends with .pdf
            is_s3_url = 's3.eu-west-1.amazonaws.com' in download_url or 'sgn-prd-assets' in download_url
            is_pdf_url = download_url.lower().endswith('.pdf')

            if not (is_s3_url or is_pdf_url):
                self.logger.warning(f"365discount: Captured URL does not appear to be a PDF or S3 URL: {download_url}")

            self.logger.info(f"365discount: Captured PDF URL: {download_url}")

        except PlaywrightTimeoutError as pe: # Catch specific Playwright TimeoutError
            self.logger.error(f"365discount: Timeout error during PDF navigation or iframe interaction: {pe}", exc_info=True)
            return []
        except Exception as e:
            self.logger.error(f"365discount: Failed to navigate to PDF or capture its URL. Error: {e}", exc_info=True)
            return []

        # 7. Save PDF from URL
        if not download_url:
            self.logger.error("365discount: download_url was not captured, cannot save PDF.")
            return []

        self.logger.info(f"365discount: 🎯 About to download PDF from S3 URL: {download_url}")
        self.logger.info(f"365discount: URL length: {len(download_url)} characters")

        # Remove redundant wait - we'll wait once before download

        file_title_part = self._sanitize_filename(raw_date_info.split('-')[0].strip() if "Unknown_Date" not in raw_date_info else display_title)
        suggested_filename = f"{file_title_part}.pdf"
        final_pdf_filename = self._sanitize_filename(suggested_filename)
        if not final_pdf_filename.lower().endswith(".pdf"): final_pdf_filename += ".pdf"
            
        download_dir = self._ensure_download_dir()
        local_pdf_path = download_dir / final_pdf_filename

        try:
            # Wait for S3 PDF content to be fully available before downloading
            self.logger.info("365discount: ⏳ Waiting 10 seconds for S3 PDF content to be fully ready...")
            await asyncio.sleep(10)
            self.logger.info("365discount: ✅ S3 PDF should be ready, starting download")

            self.logger.info(f"365discount: 📥 Starting PDF download with 60s timeout...")

            # Add timeout to prevent hanging
            pdf_content = await asyncio.wait_for(
                self._download_file_content(download_url),
                timeout=60.0
            )

            if not pdf_content:
                self.logger.error(f"365discount: ❌ Failed to download PDF content from {download_url}.")
                return []

            self.logger.info(f"365discount: ✅ Downloaded {len(pdf_content)} bytes of PDF content")

            with open(local_pdf_path, 'wb') as f:
                f.write(pdf_content)
            self.logger.info(f"365discount: 💾 Successfully saved PDF to: {local_pdf_path}")

            # Verify the file was written correctly
            file_size = local_pdf_path.stat().st_size
            self.logger.info(f"365discount: 📊 Saved file size: {file_size} bytes")

            # Parse dates using existing smart utilities
            valid_from = None
            valid_to = None

            if raw_date_info and "Unknown_Date" not in raw_date_info:
                # Use the existing robust date parsing from catalog_processor
                try:
                    from catalog_processor import parse_verbose_danish_date_range
                    date_result = parse_verbose_danish_date_range(raw_date_info)

                    if date_result:
                        start_date, end_date = date_result
                        valid_from = start_date.strftime('%Y%m%d')
                        valid_to = end_date.strftime('%Y%m%d')
                        self.logger.info(f"365discount: Parsed dates using smart utilities: {valid_from} to {valid_to}")
                    else:
                        self.logger.warning(f"365discount: Smart date parsing failed for: {raw_date_info}")
                except ImportError:
                    self.logger.warning("365discount: Could not import catalog_processor date utilities")
                except Exception as e:
                    self.logger.warning(f"365discount: Date parsing error: {e}")

            # Create initial catalog data
            catalog_data = {
                "store_name": self.store_name, "title": display_title,
                "raw_date_info": raw_date_info, "pdf_url": download_url,
                "local_path": str(local_pdf_path),
                "valid_from": valid_from,
                "valid_to": valid_to
            }

            # Upload to cloud storage
            cloud_path = self.upload_pdf_to_cloud(str(local_pdf_path), catalog_data)
            if cloud_path:
                catalog_data["cloud_pdf_path"] = cloud_path
                self.logger.info(f"365discount: PDF uploaded to cloud storage: {cloud_path}")
            else:
                self.logger.warning(f"365discount: Failed to upload PDF to cloud storage")

            self.collected_catalogs.append(catalog_data)

            # Clean up local file after cloud upload
            try:
                local_pdf_path.unlink()
                self.logger.debug(f"365discount: Cleaned up local PDF: {local_pdf_path}")
            except Exception as cleanup_error:
                self.logger.warning(f"365discount: Failed to clean up local PDF: {cleanup_error}")
        except Exception as e:
            self.logger.error(f"365discount: Error saving PDF from URL {download_url}: {e}", exc_info=True) # Changed log

        return self.collected_catalogs