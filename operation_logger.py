#!/usr/bin/env python3
"""
🏗️ ENTERPRISE OPERATION LOGGER
===============================

Hands-off, automatic operation logging and status reconciliation system.
Provides comprehensive audit trail and automatic status healing.

Features:
- Automatic operation logging for all pipeline activities
- Self-healing status reconciliation 
- Enterprise-grade error tracking
- Zero manual intervention required
- Full audit trail for compliance

Usage:
    from operation_logger import log_operation, auto_reconcile_status
    
    # Log operations automatically
    log_operation("SCRAPE", "STARTED", catalog_id=123, store_name="Netto")
    log_operation("SCRAPE", "SUCCESS", catalog_id=123, metadata={"pages": 10})
    
    # Auto-reconcile runs automatically but can be triggered manually
    auto_reconcile_status()
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from contextlib import contextmanager

from database import SessionLocal, OperationLog, Catalog, Product, Store, ProcessedCatalogHashes, CatalogProcessingQueue
from sqlalchemy import func, desc
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

class OperationLogger:
    """
    🏗️ Enterprise Operation Logger
    
    Provides automatic, hands-off logging and status reconciliation
    for the entire pipeline with zero manual intervention.
    """
    
    @staticmethod
    @contextmanager
    def get_db_session():
        """Context manager for database sessions with automatic cleanup."""
        db = SessionLocal()
        try:
            yield db
        except Exception as e:
            logger.error(f"Database error in operation logger: {e}")
            db.rollback()
            raise
        finally:
            db.close()
    
    @staticmethod
    def log_operation(
        operation_type: str,
        operation_status: str,
        catalog_id: Optional[int] = None,
        store_name: Optional[str] = None,
        message: Optional[str] = None,
        error_details: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        triggered_by: str = "SYSTEM"
    ) -> Optional[int]:
        """
        🔧 AUTOMATIC OPERATION LOGGING
        
        Logs all pipeline operations with comprehensive context.
        Returns operation log ID for tracking.
        
        Args:
            operation_type: SCRAPE, PARSE, STATUS_UPDATE, RECONCILE, MANUAL_RETRY
            operation_status: STARTED, SUCCESS, FAILED, CANCELLED
            catalog_id: Optional catalog ID
            store_name: Store name for context
            message: Human-readable message
            error_details: Detailed error information
            metadata: Additional context (product counts, timing, etc.)
            triggered_by: USER, CRON, AUTO_RECONCILE, MANUAL
        """
        try:
            with OperationLogger.get_db_session() as db:
                # Auto-detect store name if catalog_id provided but store_name missing
                if catalog_id and not store_name:
                    catalog = db.query(Catalog).join(Store).filter(Catalog.id == catalog_id).first()
                    if catalog:
                        store_name = catalog.store.name
                
                operation_log = OperationLog(
                    catalog_id=catalog_id,
                    store_name=store_name,
                    operation_type=operation_type,
                    operation_status=operation_status,
                    message=message,
                    error_details=error_details,
                    operation_metadata=metadata,
                    started_at=datetime.now(),
                    triggered_by=triggered_by
                )
                
                db.add(operation_log)
                db.commit()
                db.refresh(operation_log)
                
                logger.info(f"📝 Logged operation: {operation_type} {operation_status} for {store_name or 'system'} (ID: {operation_log.id})")
                return operation_log.id
                
        except Exception as e:
            logger.error(f"❌ Failed to log operation {operation_type} {operation_status}: {e}")
            return None
    
    @staticmethod
    def complete_operation(operation_log_id: int, status: str, message: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None):
        """
        🎯 COMPLETE OPERATION LOGGING
        
        Updates an operation log entry with completion details.
        Automatically calculates duration.
        """
        try:
            with OperationLogger.get_db_session() as db:
                operation_log = db.query(OperationLog).filter(OperationLog.id == operation_log_id).first()
                if operation_log:
                    operation_log.operation_status = status
                    operation_log.completed_at = datetime.now()
                    operation_log.duration_seconds = (operation_log.completed_at - operation_log.started_at).total_seconds()
                    
                    if message:
                        operation_log.message = message
                    if metadata:
                        # Merge with existing metadata
                        existing_metadata = operation_log.operation_metadata or {}
                        existing_metadata.update(metadata)
                        operation_log.operation_metadata = existing_metadata
                    
                    db.commit()
                    logger.info(f"✅ Completed operation {operation_log_id}: {status} ({operation_log.duration_seconds:.2f}s)")
                else:
                    logger.warning(f"⚠️  Operation log {operation_log_id} not found for completion")
                    
        except Exception as e:
            logger.error(f"❌ Failed to complete operation {operation_log_id}: {e}")
    
    @staticmethod
    def auto_reconcile_status() -> Dict[str, Any]:
        """
        🔧 AUTOMATIC STATUS RECONCILIATION
        
        Hands-off status healing that runs automatically.
        Detects and fixes status mismatches without manual intervention.
        
        Returns reconciliation summary for monitoring.
        """
        reconciliation_start = datetime.now()
        reconciled_catalogs = []
        
        try:
            # Log the reconciliation start
            reconcile_log_id = OperationLogger.log_operation(
                "RECONCILE", "STARTED", 
                message="Automatic status reconciliation started",
                triggered_by="AUTO_RECONCILE"
            )
            
            with OperationLogger.get_db_session() as db:
                # Get all catalogs with their actual product counts
                catalogs_with_products = db.query(
                    Catalog.id,
                    Store.name.label('store_name'),
                    Catalog.title,
                    func.count(Product.id).label('product_count')
                ).join(Store, Catalog.store_id == Store.id)\
                 .outerjoin(Product, Catalog.id == Product.catalog_id)\
                 .group_by(Catalog.id, Store.name, Catalog.title).all()
                
                for catalog in catalogs_with_products:
                    catalog_id = catalog.id
                    product_count = catalog.product_count
                    store_name = catalog.store_name
                    
                    # Only reconcile catalogs with products (successful processing)
                    if product_count > 0:
                        needs_update = False
                        updates = []
                        
                        # Check and fix CatalogProcessingQueue status
                        queue_entry = db.query(CatalogProcessingQueue).filter(
                            CatalogProcessingQueue.catalog_db_id == catalog_id
                        ).first()
                        
                        if queue_entry and queue_entry.status == 'PENDING':
                            queue_entry.status = 'SUCCESS'
                            queue_entry.last_attempt_at = datetime.now()
                            needs_update = True
                            updates.append("queue")
                        
                        # Check and fix ProcessedCatalogHashes status
                        hash_entry = db.query(ProcessedCatalogHashes).filter(
                            ProcessedCatalogHashes.catalog_db_id == catalog_id
                        ).first()
                        
                        if hash_entry and hash_entry.status == 'PENDING':
                            hash_entry.status = 'SUCCESS'
                            hash_entry.processed_at = datetime.now()
                            needs_update = True
                            updates.append("hash")
                        
                        if needs_update:
                            reconciled_catalogs.append({
                                'catalog_id': catalog_id,
                                'store_name': store_name,
                                'product_count': product_count,
                                'updates': updates
                            })
                            
                            # Log individual reconciliation
                            OperationLogger.log_operation(
                                "STATUS_UPDATE", "SUCCESS",
                                catalog_id=catalog_id,
                                store_name=store_name,
                                message=f"Auto-reconciled status for {product_count} products",
                                metadata={"product_count": product_count, "updates": updates},
                                triggered_by="AUTO_RECONCILE"
                            )
                
                # Commit all changes
                db.commit()
            
            # Complete the reconciliation log
            reconciliation_duration = (datetime.now() - reconciliation_start).total_seconds()
            
            if reconcile_log_id:
                OperationLogger.complete_operation(
                    reconcile_log_id, 
                    "SUCCESS",
                    message=f"Auto-reconciled {len(reconciled_catalogs)} catalogs",
                    metadata={
                        "reconciled_count": len(reconciled_catalogs),
                        "reconciled_catalogs": reconciled_catalogs,
                        "duration_seconds": reconciliation_duration
                    }
                )
            
            logger.info(f"🔧 Auto-reconciliation complete: {len(reconciled_catalogs)} catalogs updated in {reconciliation_duration:.2f}s")
            
            return {
                "success": True,
                "reconciled_count": len(reconciled_catalogs),
                "reconciled_catalogs": reconciled_catalogs,
                "duration_seconds": reconciliation_duration
            }
            
        except Exception as e:
            logger.error(f"❌ Auto-reconciliation failed: {e}")
            
            # Log the failure
            if 'reconcile_log_id' in locals() and reconcile_log_id:
                OperationLogger.complete_operation(
                    reconcile_log_id,
                    "FAILED", 
                    message=f"Auto-reconciliation failed: {str(e)}",
                    metadata={"error": str(e)}
                )
            
            return {
                "success": False,
                "error": str(e),
                "reconciled_count": 0
            }
    
    @staticmethod
    def get_operation_history(catalog_id: Optional[int] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        📊 GET OPERATION HISTORY
        
        Retrieves operation history for monitoring and debugging.
        """
        try:
            with OperationLogger.get_db_session() as db:
                query = db.query(OperationLog)
                
                if catalog_id:
                    query = query.filter(OperationLog.catalog_id == catalog_id)
                
                operations = query.order_by(desc(OperationLog.started_at)).limit(limit).all()
                
                return [{
                    'id': op.id,
                    'catalog_id': op.catalog_id,
                    'store_name': op.store_name,
                    'operation_type': op.operation_type,
                    'operation_status': op.operation_status,
                    'message': op.message,
                    'started_at': op.started_at.isoformat() if op.started_at else None,
                    'completed_at': op.completed_at.isoformat() if op.completed_at else None,
                    'duration_seconds': op.duration_seconds,
                    'triggered_by': op.triggered_by,
                    'metadata': op.operation_metadata
                } for op in operations]
                
        except Exception as e:
            logger.error(f"❌ Failed to get operation history: {e}")
            return []

    @staticmethod
    def cleanup_stale_in_memory_status(processing_catalogs_dict: dict, max_age_hours: int = 24) -> int:
        """
        🧹 AUTOMATIC IN-MEMORY STATUS CLEANUP

        Removes stale entries from the in-memory PROCESSING_CATALOGS dict.
        Entries older than max_age_hours are considered stale and removed.

        Args:
            processing_catalogs_dict: The PROCESSING_CATALOGS dict to clean
            max_age_hours: Maximum age in hours before considering entry stale

        Returns:
            Number of stale entries removed
        """
        from datetime import datetime, timedelta

        if not processing_catalogs_dict:
            return 0

        stale_cutoff = datetime.now() - timedelta(hours=max_age_hours)
        stale_keys = []

        for catalog_id, status_info in processing_catalogs_dict.items():
            # Check if entry has a timestamp
            if 'last_updated' in status_info:
                try:
                    last_updated = datetime.fromisoformat(status_info['last_updated'])
                    if last_updated < stale_cutoff:
                        stale_keys.append(catalog_id)
                except (ValueError, TypeError):
                    # Invalid timestamp, consider it stale
                    stale_keys.append(catalog_id)
            else:
                # No timestamp, check if it's in a final state
                status = status_info.get('status', '').lower()
                if status in ['completed', 'cancelled', 'error', 'failed']:
                    stale_keys.append(catalog_id)

        # Remove stale entries
        removed_count = 0
        for catalog_id in stale_keys:
            try:
                del processing_catalogs_dict[catalog_id]
                removed_count += 1
                logger.info(f"🧹 Removed stale in-memory status for catalog {catalog_id}")
            except KeyError:
                pass  # Already removed

        if removed_count > 0:
            logger.info(f"🧹 Cleaned up {removed_count} stale in-memory status entries")

            # Log the cleanup operation
            OperationLogger.log_operation(
                "CLEANUP", "SUCCESS",
                message=f"Cleaned up {removed_count} stale in-memory status entries",
                metadata={"removed_count": removed_count, "stale_catalog_ids": stale_keys},
                triggered_by="AUTO_CLEANUP"
            )

        return removed_count

# Convenience functions for easy import
log_operation = OperationLogger.log_operation
complete_operation = OperationLogger.complete_operation
auto_reconcile_status = OperationLogger.auto_reconcile_status
get_operation_history = OperationLogger.get_operation_history
cleanup_stale_in_memory_status = OperationLogger.cleanup_stale_in_memory_status
