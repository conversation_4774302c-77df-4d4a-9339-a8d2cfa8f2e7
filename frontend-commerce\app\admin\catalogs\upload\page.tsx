"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth';
import Link from 'next/link';

// Add store interface
interface Store {
  id: number;
  name: string;
}

export default function CatalogUploadPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [file, setFile] = useState<File | null>(null);
  const [name, setName] = useState('');
  const [storeId, setStoreId] = useState<number | ''>('');
  const [validFrom, setValidFrom] = useState('');
  const [validTo, setValidTo] = useState('');
  
  const [stores, setStores] = useState<Store[]>([]);
  const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:6969';
  const [uploadLoading, setUploadLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Redirect non-admin users
  useEffect(() => {
    if (!isLoading && (!user || !user.is_admin)) {
      router.push('/');
    }
  }, [user, isLoading, router]);

  // Load stores for dropdown
  useEffect(() => {
    if (user?.is_admin) {
      const loadStores = async () => {
        try {
          const response = await fetch(`${API_BASE}/stores`);
          if (!response.ok) {
            throw new Error('Kunne ikke indlæse butikker');
          }
          const data = await response.json();
          setStores(data);
        } catch (err) {
          console.error('Failed to load stores:', err);
          setError('Kunne ikke indlæse butikker');
        }
      };
      
      loadStores();
    }
  }, [user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) {
      setError('Vælg venligst en katalogfil');
      return;
    }

    if (!name) {
      setError('Indtast venligst et navn til kataloget');
      return;
    }

    if (!storeId) {
      setError('Vælg venligst en butik');
      return;
    }

    setError(null);
    setUploadLoading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('name', name);
      formData.append('store_id', storeId.toString());
      
      if (validFrom) {
        formData.append('valid_from', validFrom);
      }
      if (validTo) {
        formData.append('valid_to', validTo);
      }

      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/upload_catalog`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Upload fejlede');
      }

      // Redirect to catalog list on success
      router.push('/admin/catalogs');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload fejlede');
      setUploadLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[70vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Don't render admin content until we confirm the user is an admin
  if (!user || !user.is_admin) {
    return null;
  }

  return (
    <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Upload Nyt Katalog</h1>
        <p className="mt-2 text-gray-600">Upload et nyt katalog fra en supermarkedskæde.</p>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white shadow overflow-hidden sm:rounded-lg p-6">
        <div className="space-y-6">
          {/* File Upload */}
          <div>
            <label htmlFor="file" className="block text-sm font-medium text-gray-700 mb-1">
              Katalogfil (PDF)
            </label>
            <input
              type="file"
              id="file"
              accept=".pdf"
              onChange={(e) => e.target.files?.[0] ? setFile(e.target.files[0]) : null}
              className="block w-full text-sm text-gray-500
                file:mr-4 file:py-2 file:px-4
                file:rounded-md file:border-0
                file:text-sm file:font-semibold
                file:bg-indigo-50 file:text-indigo-700
                hover:file:bg-indigo-100"
              required
            />
            <p className="mt-1 text-sm text-gray-500">Vælg en PDF-fil med kataloget.</p>
          </div>

          {/* Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Katalognavn
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm py-2 px-3 border"
              placeholder="F.eks. 'Netto Tilbudsavis Uge 25'"
              required
            />
          </div>

          {/* Store Selection */}
          <div>
            <label htmlFor="store" className="block text-sm font-medium text-gray-700 mb-1">
              Butik
            </label>
            <select
              id="store"
              value={storeId}
              onChange={(e) => setStoreId(e.target.value ? parseInt(e.target.value) : '')}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm py-2 px-3 border"
              required
            >
              <option value="">Vælg butik...</option>
              {stores.map((store) => (
                <option key={store.id} value={store.id}>
                  {store.name}
                </option>
              ))}
            </select>
          </div>

          {/* Valid From Date */}
          <div>
            <label htmlFor="valid_from" className="block text-sm font-medium text-gray-700 mb-1">
              Gyldig Fra
            </label>
            <input
              type="date"
              id="valid_from"
              value={validFrom}
              onChange={(e) => setValidFrom(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm py-2 px-3 border"
            />
          </div>

          {/* Valid To Date */}
          <div>
            <label htmlFor="valid_to" className="block text-sm font-medium text-gray-700 mb-1">
              Gyldig Til
            </label>
            <input
              type="date"
              id="valid_to"
              value={validTo}
              onChange={(e) => setValidTo(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm py-2 px-3 border"
            />
            <p className="mt-1 text-sm text-gray-500">Valgfrit. Hvornår udløber tilbuddene i dette katalog?</p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4 pt-4">
            <Link 
              href="/admin/catalogs"
              className="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Annuller
            </Link>
            <button
              type="submit"
              disabled={uploadLoading}
              className="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center gap-2"
            >
              {uploadLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
              )}
              {uploadLoading ? 'Uploader...' : 'Upload Katalog'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
