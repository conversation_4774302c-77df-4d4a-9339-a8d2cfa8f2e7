#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to trigger catalog processing on Render via the internal API endpoint.
This will cause the scrapers to run on Render and save PDFs to the persistent disk
where the background worker can access them.
"""

import requests
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def trigger_catalog_processor():
    """Trigger the catalog processor on Render"""
    
    # Get the cron secret from environment
    cron_secret = os.getenv('CRON_SECRET')
    if not cron_secret:
        print("❌ CRON_SECRET not found in environment variables")
        print("   You need to set this to match the value in your Render environment")
        return False
    
    # Render API endpoint
    url = "https://avis-api.onrender.com/internal/run_catalog_processor"
    
    headers = {
        "x-cron-secret": cron_secret,
        "Content-Type": "application/json"
    }
    
    print(f"🚀 Triggering catalog processor on Render...")
    print(f"   URL: {url}")
    print(f"   Secret: {cron_secret[:8]}...")
    
    try:
        response = requests.post(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print("✅ Catalog processor triggered successfully!")
            print(f"   Response: {response.json()}")
            return True
        elif response.status_code == 403:
            print("❌ Forbidden - CRON_SECRET is incorrect")
            print(f"   Make sure CRON_SECRET matches the value in Render environment")
            return False
        else:
            print(f"❌ Failed to trigger catalog processor")
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🎯 RENDER CATALOG PROCESSOR TRIGGER")
    print("=" * 60)
    print()
    print("This script will trigger the catalog processor to run on Render,")
    print("which will download PDFs to the persistent disk where the")
    print("background worker can access them for AI processing.")
    print()
    
    success = trigger_catalog_processor()
    
    if success:
        print()
        print("🎉 SUCCESS!")
        print("   The catalog processor is now running on Render.")
        print("   PDFs will be downloaded to /var/data/catalogs/")
        print("   The background worker will then be able to process them.")
        print()
        print("📊 You can monitor progress in the Render logs:")
        print("   - avis-api service logs (for scraping)")
        print("   - PDF Parser Worker logs (for AI processing)")
    else:
        print()
        print("💡 TROUBLESHOOTING:")
        print("   1. Check that CRON_SECRET is set correctly in your .env file")
        print("   2. Verify the Render service is running at avis-api.onrender.com")
        print("   3. Check Render logs for any deployment issues")
